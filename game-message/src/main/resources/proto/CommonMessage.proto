syntax = "proto3";

package ProtoMessage;
option java_package = "com.proto";

// 货币单元
message DCurrencyItem {
	 int32 currencyId       = 1; // 货币id
	 double value           = 2; // 货币值
	 string name            = 3; // 货币名字
	 string icon            = 4; // 货币图标
	 string symbol          = 5; // 货币符号
	 bool show              = 6; // 是否显示
   double bonusValue      = 7; // 赠金
}

// 物品展示信息 - 预览或纯展示，包括货币
message DItemShow {
	 int32 itemId           = 1; // 当用于展示时，可能为货币id
	 double num             = 2; // 数量
   int32 rewardType       = 3; // 1.cash 2.bonus
}

// 玩家个人信息
message PlayerInfo {
	 int64 playerId			                    = 1; //玩家唯一id
	 string playerName		                  = 2; //名字
	 string headId                          = 3; //头像id
	 int64 createTime                       = 4; //创建时间
	 repeated DCurrencyItem cItem           = 5; //货币
	 string region                          = 6; //国家简称
	 int32 currencyId                       = 7; //当前货币
	 string superiorId                      = 8; //上级id
	 int32 viewFiat                         = 9; //显示法币
	 int32 gameId                           = 10; //游戏id
	 repeated int32 accountBind             = 11; //账号绑定 1.邮件 2.谷歌 3.Telegram 4.Facebook 5.Twitter
	 string email                           = 12; //邮箱
	 int32 vipLevel                         = 13; //vip等级
	 double curVipExp                       = 14; //当前vip经验
	 double needVipExp                      = 15; //需要的vip经验
	 bool enable2FA                         = 16; //Two-factor authentication
	 string referralLink                    = 17; //推广连接
	 string referralCode                    = 18; //推广码
	 string areaCode                        = 19; //区号
	 string phone                           = 20; //电话
	 repeated ThreePartyInfo threePartyList = 21; //三方账户
	 bool hideName                          = 22; //隐藏名字
	 int32 language                         = 23; //语言
   int32 basicVerification                = 24; //基础认证 0.未认证 1.认证中 2.认证成功 3.认证失败
   int32 advancedVerification             = 25; //高级认证  0.未认证 1.认证中 2.认证成功 3.认证失败
   int32 rechargeTimes                    = 26; //充值次数
   int32 agentId                          = 27; //代理id
   int32 channelId                        = 28; //渠道id
   string account                         = 29; //账号
   double rechargeAmount                  = 30; //充值金额
   double curVipRecharge                  = 31; //当前vip充值
   bool rechargeBonus                     = 32; //充值是否赠送bonus
   bool receivePwa                        = 33; //是否领取pwa
   repeated int32 channels                = 34; //1.pwa 2.akp
   int32 dailyRechargeTimes               = 35; //每日充值次数
   string site                            = 36; //站点
   int32 mediaId                          = 37; //媒体id
   int32 adId                             = 38; //广告id
   int32 channel                          = 39; //渠道 0.h5 1.pwa 2.apk
   string registerRegion                  = 40; //注册
   string business_no                     = 41; //商户
   int32 hallId                           = 42; //大厅id
   int64 totalBetTimes                    = 43; //下注次数
   string device                          = 44; //设备信息
   string model                           = 45; //机型
   int32 loginTimes                       = 46; //登录次数
   int64 loginTime                        = 47; //登录时间
   int64 logoutTime                       = 48; //登出时间
   int32 weeklyRechargeTimes              = 49; //每周充值次数
   int32 tgShareTimes                     = 50; //tg分享次数
   CurrencyTraderData currencyTraderData  = 51; //平台币商
   bool isWeb3Authorized                  = 52; // 是否为Web3钱包授权
}

message CurrencyTraderData {
  string head                              = 1; //头像
  string name                              = 2; //名字
  string mediaLink                         = 3; //社媒连接
  string contactDetails                    = 4; //联系方式
  string mediaIcon                         = 5; //社媒icon
}

message ThreePartyInfo {
     int32 threeParty             = 1; // 1.邮件 2.谷歌 3.Telegram 4.Facebook 5.Twitter
     string account               = 2; // 账户
     string threePartyId          = 3; // 三方id
}

message AccountInfo {
    double totalWin               = 1; //总赢分
    double totalBet               = 2; //总下注
    double todayWin               = 3; //今天赢分
    double todayBet               = 4; //今天下注
}

message InboxInfo {
     string inboxId                = 1; //邮件id
     int32 inboxType               = 2; //邮件类型 1.系统 2.活动 3.公告
     string title                  = 3; //标题
     string context                = 4; //内容
     string fileUrl                = 5; //文件地址
     int32 jumpType                = 6; //1.内链 2.外链
     int32 popupLinks              = 7; //弹框类型 1.任务 2.转盘 3.充值 4.客服
     string innerLinks             = 8; //内部链接
     string externalLinks          = 9; //外链接
	   bool read                     =10; //是否已读
     int64 createTime              =11; //创建时间
	   string imageText              =12; //图片文字
     int32 isJump                  =13; //是否有弹窗
}

//游戏列表
message GameApiInfo {
    int32 gameId                   = 1; //游戏id
    int32 platformId               = 2; //平台id
    string platformGameId          = 3; //平台游戏id
    string gameProvider            = 4; //游戏品牌
    string name                    = 5; //游戏名字
    int32 type                     = 6; //游戏类型
    repeated int32 subType         = 7; //游戏子类型 1.buyIn 2.jp 3.mega 4.free
    repeated int32 supportDevices  = 8; //支持设备 1.pc 2.mobile 3.pad
    repeated int32 supportCurrency = 9; //支持货币
    int32 tag                      =10; //标签 1.热门 2.最新 3.推荐
    string fileUrl                 =11; //图片地址
    int32 status                   =12; //状态 1.正常 2.关闭 3.维护
    int32 onlineNum                =13; //在线人数
    bool favorites                 =14; //是否收藏
    string rtp                     =15; //rtp
    string desc                    =16; //描述
    int32 maxMul                   =17; //最大倍数
    int32 gameFluctuate            =18; //游戏波动 1.高 2.中 3.低
    bool freePlay                  =19; //是否试玩
    int32 seqNum                   =20; //序号
    bool bonus                     =21; //赠金
    string innerLink               =22; //内链
}

//下注信息
message BetInfo {
   string gameName           = 1; //游戏名字
   int32 currencyId          = 2; //货币id
   double amount             = 3; //金额
   int64 time                = 4; //时间
   double mul                = 5; //赔率
   double payout             = 6; //利润
   int32 gameType            = 7; //游戏类型
   string noteId             = 8; //注单id
   string playerName         = 9; //玩家名字
   int32 gameId              =10; //游戏id
   int32 headId              =11; //头像id
   int32 platformId          =12; //平台id
   string platformName       =13; //平台名字
}

message FbInfo {
   string event_name         = 1; //事件名字
   string pixelId            = 2;
   string fbToken            = 3;
}

message KWaiInfo {
  string pixelId            = 1;
  string KWaiToken          = 2;
  string clickId            = 3;
}






