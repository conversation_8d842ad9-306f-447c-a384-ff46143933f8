syntax = "proto3";

package ProtoMessage;
option java_package = "com.proto";

import "CommonMessage.proto";

message InnerReqServerListMessage {
	 int32 msgID    = 1;
	repeated int32 type		= 2;//服务器类型 1、网关、大厅、登录、游戏
}

message InnerResServerListMessage {
	 int32 msgID                = 1;
	repeated ServerInfoList serverList  = 2;//服务器列表
}

message ServerInfoList{
     int32 type		             = 1;//服务器类型
	repeated InnerServerInfo serverInfos = 2;// 服务器列表
}

message InnerServerInfo {
     int32 id			        = 1;// 服务器ID
     string ip			        = 2;// 内网地址
     int32 type			        = 3;// 类型
     int32 port			        = 4;// 端口
     int32 gameState	        = 5;// 状态 -1表示维护；0表示准备开启；1表示正常，2表示不显示，3表示内部开启
     string version		        = 6;// 版本号
     string content		        = 7;// 服务器信息：玩家可修改
     int32 power		        = 8;// 权重
     int32 online		        = 9;// 在线人数
     int32 httpPort		        = 10;// http端口
     string name		        = 11;// 名字
     string wwwIp		        = 12;// 外网地址
     string ipWhiteList	        = 13;//ip白名单，当内部开始或隐藏时，白名单的可以进入。以；分割ip，ip可以只有2个网段
     string accountWhiteList	= 14;//帐号白名单，当内部开始或隐藏时，白名单的可以进入。以；分割帐号
}

message InnerReqRegisterUpdateMessage {
     int32 msgID                 = 1;
     InnerServerInfo serverInfo  = 2;
}

//登出大厅
message InnerReqLoginOutHallMessage {
     int32 msgID                 = 1;
     int64 playerID              = 2;
}

//内部http处理
message InnerReqHttpHandlerMessage {
     int32 msgID                 = 1;
	 string params                 = 2;//参数
	 string requestPath            = 3;//请求地址
	 int64 playerID                = 4;//玩家id
}

//内部http处理
message InnerResHttpHandlerMessage {
   int32 msgID                   = 1;
   string params                 = 2;//参数
	 string requestPath            = 3;//请求地址
}

//广播消息 暂定用来指定玩家广播
message InnerBroadcastMessage {
    int32 msgID                  = 1;
    repeated int64 playerGuid    = 2;//玩家id
    string languageId            = 3;//语言id
    repeated string params       = 4;//参数
}

//通知充值
message InnerNotifyRechargeMessage {
    int32 msgID                  = 1;
    int32 currencyId             = 2;
    double amount                = 3;
    int64 orderId                = 4;
    int32 status                 = 5;
    FbInfo fbInfo                = 6;
}

//通知提现
message InnerNotifyWithdrawMessage {
    int32 msgID                  = 1;
    int32 currencyId             = 2;
    double amount                = 3;
    int64 orderId                = 4;
    int32 status                 = 5;
    int32 currencyTraderId       = 6;
}

//通知货币更新
message InnerNotifyCurrencyUpdateMessage {
    int32 msgID                  = 1;
    int32 error                  = 2; //错误码
    NotifyData notifyData        = 3;
}

//通知游戏数据更新
message InnerNotifyGameDataUpdateMessage {
  int32 msgID                  = 1;
  int32 error                  = 2; //错误码
  NotifyData notifyData        = 3;
}

message NotifyData {
  int32 gameId                 = 1; //游戏id
  int32 type                   = 2; //1.bet 2.win 3.refund
  int32 currencyId             = 3; //货币
  double betAmount             = 4; //下注
  double validBet              = 5; //有效下注
  double win                   = 6; //赢分
  string noteId                = 7; //注单id
  double totalWin              = 8; //总赢分
  int32 freeTimes              = 9; //免费次数
  double balance               =10; //余额
  int32 supplierId             =12; //供应商id
  int64 updSessionId           =13;
  string playerName            =14;
  string currency              =15;
  int64 updated_time           =16;
  double real_transfer_amount  =17;
  string data                  =18;
}

//通知更新游戏信息
message InnerNotifyUpdateGameInfoMessage {
    int32 msgID                  = 1;
    int64 playerId               = 2;
    int32 gameId                 = 3; //游戏id
    int32 type                   = 4; //1.进入 2.退出
}

//获取游戏信息
message InnerGetGameListMessage {
    int32 msgID                           = 1;
    repeated AgentGameInfo agentGameList  = 2;
}

message AgentGameInfo {
    int32 gameId                 = 1; //游戏id
    int32 online                 = 2; //在线人数
}










