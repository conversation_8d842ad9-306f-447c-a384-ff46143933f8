syntax = "proto3";

package ProtoMessage;
option java_package = "com.proto";


// 消息ID枚举
// 客户端请求消息ReqXX，服务器返回消息ResXX
// 十万位：服务器类型；万位千位：模块编号，百位十位个位：消息编号，依次递增
//
// 1网关服务器+内部服务器转发
// 2登录服务器
// 3游戏服务器 服务器id+消息id（30001000）
// 4大厅服务器
// 5日志服务器
// 6充值服务器


enum MID {
    none                                = 0;

	  //服务器内部消息
	  InnerReqRegisterUpdate				      = 100001; // 服务器注册请求
	  InnerReqHttpHandler                 = 100002; // 内部http处理
	  InnerResHttpHandler                 = 100003; // 内部http处理
	  InnerBroadcast                      = 100004; // 广播消息
	  InnerReqServerList                  = 100005; // 请求服务器列表
	  InnerResServerList                  = 100006; // 返回服务器列表
	  InnerReqLoginOutHall                = 100007; // 请求退出大厅
	  InnerNotifyRecharge                 = 100008; // 通知充值
	  InnerNotifyWithdraw                 = 100009; // 通知提现
	  InnerNotifyCurrencyUpdate           = 100010; // 通知货币更新
    InnerNotifyUpdateGameInfo           = 100011; // 通知更新游戏信息
    InnerGetGameList                    = 100012; // 获取游戏列表信息
    InnerNotifyGameDataUpdate           = 100013; // 通知游戏数据更新

    //登录 200001
	  ReqRegister                         = 200001; // 请求注册
	  ReqLogin                            = 200002; // 请求登录
	  ResLogin                            = 200003; // 返回登录
	  ReqMailVerifyCode                   = 200004; // 请求邮箱验证码
	  ResMailVerifyCode                   = 200005; // 返回邮箱验证码
	  ReqNotLoggedIn                      = 200006; // 请求未登录
    ResNotLoggedIn                      = 200007; // 返回未登录
    ReqWebSiteModel                     = 200008; // 请求网站模板
    ResWebSiteModel                     = 200009; // 返回网站模板
    ReqPhoneVerifyCode                  = 200010; // 请求电话验证码
    ResPhoneVerifyCode                  = 200011; // 返回电话验证码
    ReqRegisterAuth                     = 200012; // 请求注册认证
    ResRegisterAuth                     = 200013; // 返回注册认证
    ReqChannelInstall                   = 200014; // 请求渠道安装
    ResChannelInstall                   = 200015; // 返回渠道安装

    //大厅 400001-400200
    ReqPlayerEntryHall                  = 400001; // 请求玩家进入大厅
    ResPlayerEntryHall                  = 400002; // 返回玩家进入大厅
	  ReqPlayerSignOut                    = 400003; // 请求玩家登出
	  ResPlayerSignOut                    = 400004; // 返回玩家登出
	  ReqHeadChange                       = 400005; // 请求头像更换
	  ResHeadChange                       = 400006; // 返回头像更换
	  ReqBindAccount                      = 400007; // 请求绑定账号
	  ResBindAccount                      = 400008; // 返回绑定账号
	  ReqResetPassword                    = 400009; // 请求重置密码
    ResResetPassword                    = 400010; // 返回重置密码
    ReqNameModify                       = 400011; // 请求名字修改
    ResNameModify                       = 400012; // 返回名字修改
    ReqAccountData                      = 400013; // 请求用户数据
    ResAccountData                      = 400014; // 返回用户数据
    ReqInboxData                        = 400015; // 请求邮件数据
    ResInboxData                        = 400016; // 返回邮件数据
    ReqReadInBox                        = 400017; // 请求读邮件
    ResReadInBox                        = 400018; // 返回读邮件
    ReqDeleteInbox                      = 400019; // 请求删除邮件
    ResDeleteInbox                      = 400020; // 返回删除邮件
    ReqChangeCurrency                   = 400021; // 请求切换货币
    ResChangeCurrency                   = 400022; // 返回切换货币
    ReqViewInFiat                       = 400023; // 请求显示法币
    ResViewInFiat                       = 400024; // 返回显示法币
    ReqCasinoData                       = 400027; // 请求游戏列表数据
    ResCasinoData                       = 400028; // 返回游戏列表数据
    ReqGameChannelData                  = 400029; // 请求游戏频道数据
    ResGameChannelData                  = 400030; // 返回游戏频道数据
    ReqSearchGameData                   = 400031; // 请求搜索游戏
    ResSearchGameData                   = 400032; // 返回搜索游戏
    ReqEnable2FAData                    = 400033; // 请求Enable2FA数据
    ResEnable2FAData                    = 400034; // 返回Enable2FA数据
    Req2FAVerificationCode              = 400035; // 请求2FA验证码
    Res2FAVerificationCode              = 400036; // 返回2FA验证码
    ReqEntryAgentGame                   = 400037; // 请求进入三方游戏
    ResEntryAgentGame                   = 400038; // 返回进入三方游戏
    ReqUserInteraction                  = 400039; // 请求收藏、点赞
    ResUserInteraction                  = 400040; // 返回收藏、点赞
    ReqGameOptData                      = 400041; // 请求游戏opt数据
    ResGameOptData                      = 400042; // 返回游戏opt数据
    ReqGetRankData                      = 400043; // 请求获取排行数据
    ResGetRankData                      = 400044; // 返回获取排行数据
    ReqRecentBigWinsData                = 400045; // 请求最近最大数据
    ResRecentBigWinsData                = 400046; // 返回最近最大数据
    ReqUnbindThreeParty                 = 400051; // 请求解绑三方
    ResUnbindThreeParty                 = 400052; // 返回解绑三方
    ReqSetPrivacyPreferences            = 400053; // 请求设置隐私
    ResSetPrivacyPreferences            = 400054; // 返回设置隐私
    ReqBindThreeParty                   = 400055; // 请求绑定三方
    ResBindThreeParty                   = 400056; // 返回绑定三方
    ReqPaymentMethodsData               = 400057; // 请求支付方式管理数据
    ResPaymentMethodsData               = 400058; // 返回支付方式管理数据
    ReqCheckSessionData                 = 400059; // 请求查看sessions数据
    ResCheckSessionData                 = 400060; // 返回查看sessions数据
    ReqChangeLanguage                   = 400061; // 请求切换语言
    ResChangeLanguage                   = 400062; // 返回切换语言
    ReqRefreshPlayerData                = 400063; // 请求刷新玩家数据
    ResRefreshPlayerData                = 400064; // 返回刷新玩家数据
    ReqVerifyAccount                    = 400065; // 请求验证账号
    ResVerifyAccount                    = 400066; // 返回验证账号
    ReqChangeAccount                    = 400067; // 请求更换账号
    ResChangeAccount                    = 400068; // 返回更换账号
    ReqKycAuth                          = 400069; // 请求kyc认证
    ResKycAuth                          = 400070; // 请求kyc认证
    ReqAddAccount                       = 400071; // 请求添加账号
    ResAddAccount                       = 400072; // 返回添加账号
    ReqRedemptionCodeReward             = 400073; // 请求兑换码奖励
    ResRedemptionCodeReward             = 400074; // 返回兑换码奖励
    ReqRechargeBonusOpen                = 400075; // 请求充值奖励开关
    ResRechargeBonusOpen                = 400076; // 返回充值奖励开关

    //钱包 400201-400300
    ReqBetHistoryData                   = 400201; // 请求下注历史数据
    ResBetHistoryData                   = 400202; // 返回下注历史数据
    ReqTransactionData                  = 400203; // 请求交易数据
    ResTransactionData                  = 400204; // 返回交易数据

    //充值 400301-400400
    ReqDepositWithdrawData              = 400301; // 请求充提货币数据
    ResDepositWithdrawData              = 400302; // 返回充提货币数据
    ReqDepositData                      = 400303; // 请求充值数据
    ResDepositData                      = 400304; // 请求充值数据
    ReqWithdrawData                     = 400305; // 请求提现数据
    ResWithdrawData                     = 400306; // 返回提现数据
    ReqCreateRechargeOrder              = 400307; // 请求创建充值订单
    ResCreateRechargeOrder              = 400308; // 返回创建充值订单
    ReqCreateWithdrawOrder              = 400309; // 请求创建提现订单
    ResCreateWithdrawOrder              = 400310; // 返回创建提现订单
    ReqAddDeleteWithdrawAccount         = 400311; // 请求添加删除提现账户
    ResAddDeleteWithdrawAccount         = 400312; // 返回添加删除提现账户
    ReqCurrencyTraderWithdrawData       = 400313; // 请求币商提现数据
    ResCurrencyTraderWithdrawData       = 400314; // 返回币商提现数据
    ReqCreateCurrencyTraderWithdrawOrder= 400315; // 请求创建币商订单
    ResCreateCurrencyTraderWithdrawOrder= 400316; // 返回创建币商订单

    //推广 400401-400500
    ReqBindSuperior                     = 400401; // 请求绑定上级
    ResBindSuperior                     = 400402; // 返回绑定上级
    ReqDashboardData                    = 400403; // 请求仪表盘数据
    ResDashboardData                    = 400404; // 返回仪表盘数据
    ReqMyRewardData                     = 400405; // 请求我的奖励数据
    ResMyRewardData                     = 400406; // 返回我的奖励数据
    ReqCommissionRewardData             = 400407; // 请求佣金奖励数据
    ResCommissionRewardData             = 400408; // 返回佣金奖励数据
    ReqReferralRewardData               = 400409; // 请求推广奖励数据
    ResReferralRewardData               = 400410; // 返回推广奖励数据
    ReqReferralCodeAndFriendsData       = 400411; // 请求推广码和下级数据
    ResReferralCodeAndFriendsData       = 400412; // 返回推广码和下级数据
    ReqCreateReferralCode               = 400413; // 请求创建推广码
    ResCreateReferralCode               = 400414; // 返回创建推广码
    ReqReferralCodeData                 = 400415; // 请求推广码数据
    ResReferralCodeData                 = 400416; // 返回推广码数据
    ReqFriendsData                      = 400417; // 请求下级数据
    ResFriendsData                      = 400418; // 返回下级数据
    ReqWithdrawToWallet                 = 400419; // 请求提现到钱包
    ResWithdrawToWallet                 = 400420; // 返回提现到钱包
    ReqHistoryData                      = 400421; // 请求历史数据
    ResHistoryData                      = 400422; // 返回历史数据
    ReqAffiliateWithdrawData            = 400423; // 请求提现数据
    ResAffiliateWithdrawData            = 400424; // 返回提现数据
    ReqTeamRewardData                   = 400425; // 请求团长奖励
    ResTeamRewardData                   = 400426; // 返回团长奖励
    ReqTeamData                         = 400427; // 请求团长数据
    ResTeamData                         = 400428; // 返回团长数据
    ReqThreeLevelRewardData             = 400429; // 请求三级奖励数据
    ResThreeLevelRewardData             = 400430; // 返回三级奖励数据
    ReqThreeLevelData                   = 400431; // 请求三级数据
    ResThreeLevelData                   = 400432; // 返回三级数据
    ReqDayRebateData                    = 400433; // 请求当天返回数据
    ResDayRebateData                    = 400434; // 返回当天返回数据
    ReqRecentSevenDaysData              = 400435; // 请求最近7天数据
    ResRecentSevenDaysData              = 400436; // 返回最近7天数据
    ReqMonthlyReportData                = 400437; // 请求月份报告数据
    ResMonthlyReportData                = 400438; // 返回月份报告数据

    //配置数据 400501-400600
    ReqHelpCenterData                   = 400501; // 请求帮助中心数据
    ResHelpCenterData                   = 400502; // 返回帮助中心数据
    ReqNewsData                         = 400503; // 请求新闻数据
    ResNewsData                         = 400504; // 返回新闻数据
    ReqConfigData                       = 400505; // 请求配置数据
    ResConfigData                       = 400506; // 返回配置数据

    //活动数据 400601-400700
    ReqPromotionsData                   = 400601; // 请求活动数据
    ResPromotionsData                   = 400602; // 返回活动数据
    ReqLuckSpinData                     = 400603; // 请求幸运转盘数据
    ResLuckSpinData                     = 400604; // 返回幸运转盘数据
    ReqClickLuckSpin                    = 400605; // 请求点击幸运转盘
    ResClickLuckSpin                    = 400606; // 返回点击幸运转盘
    ReqLuckSpinReferralWithdraw         = 400607; // 请求幸运推广提现
    ResLuckSpinReferralWithdraw         = 400608; // 返回幸运推广提现
    ReqBonusData                        = 400609; // 请求bonus数据
    ResBonusData                        = 400610; // 返回bonus数据
    ReqVipBonusRewardsReceive           = 400611; // 请求vipBonus奖励领取
    ResVipBonusRewardsReceive           = 400612; // 返回vipBonus奖励领取
    ReqBonusDetailsData                 = 400613; // 请求bonus明细数据
    ResBonusDetailsData                 = 400614; // 返回bonus明细数据
    ReqBonusTransactionsData            = 400615; // 请求bonus交易数据
    ResBonusTransactionsData            = 400616; // 返回bonus交易数据
    ReqDailyContestData                 = 400617; // 请求每日竞赛数据
    ResDailyContestData                 = 400618; // 返回每日竞赛数据
    ReqDailyContestHistoryData          = 400619; // 请求每日竞赛历史数据
    ResDailyContestHistoryData          = 400620; // 返回每日竞赛历史数据
    ReqWeeklyRaffleData                 = 400621; // 请求每周抽奖数据
    ResWeeklyRaffleData                 = 400622; // 返回每周抽奖数据
    ReqWeeklyRaffleMyTicketsData        = 400623; // 请求每周抽奖我的数据
    ResWeeklyRaffleMyTicketsData        = 400624; // 返回每周抽奖我的数据
    ReqWeeklyRaffleResultData           = 400625; // 请求每周抽奖结果数据
    ResWeeklyRaffleResultData           = 400626; // 返回每周抽奖结果数据
    ReqReceivePromotionsReward          = 400627; // 请求领取活动奖励
    ResReceivePromotionsReward          = 400628; // 返回领取活动奖励
    ReqActivitySignUp                   = 400629; // 请求活动报名
    ResActivitySignUp                   = 400630; // 返回活动报名
    ReqActivityRankData                 = 400631; // 请求活动排行数据
    ResActivityRankData                 = 400632; // 请求活动排行数据
    ReqActivityData                     = 400633; // 请求活动数据
    ResActivityData                     = 400634; // 返回活动雨数据
    ReqReceiveRedEnvelope               = 400635; // 请求领取红包
    ResReceiveRedEnvelope               = 400636; // 返回领取红包
    ReqReceiveRewardBox                 = 400637; // 请求领取推荐宝箱
    ResReceiveRewardBox                 = 400638; // 返回领取推荐宝箱
    ReqReceiveMysteryBonus              = 400639; // 请求领取神秘奖金
    ResReceiveMysteryBonus              = 400640; // 返回领取神秘奖金
    ReqReceivePiggyBank                 = 400641; // 请求领取存钱罐
    ResReceivePiggyBank                 = 400642; // 返回领取存钱罐
    ReqRewardBoxSubordinateData         = 400643; // 请求推荐宝箱下级数据
    ResRewardBoxSubordinateData         = 400644; // 返回推荐宝箱下级数据
    ReqVipData                          = 400645; // 请求vip数据
    ResVipData                          = 400646; // 返回vip数据
    ReqVipSignIn                        = 400647; // 请求vip签到
    ResVipSignIn                        = 400648; // 返回vip签到
    ReqReceiveVipReward                 = 400649; // 请求领取vip奖励
    ResReceiveVipReward                 = 400650; // 返回领取vip奖励
    ReqFirstChargeSignIn                = 400651; // 请求首充签到
    ResFirstChargeSignIn                = 400652; // 返回首充签到
    ReqReceiveRechargeRecover           = 400653; // 请求首充返奖
    ResReceiveRechargeRecover           = 400654; // 返回首充返奖
    ReqReceiveWageredRebates            = 400655; // 请求领取投注返利奖励
    ResReceiveWageredRebates            = 400656; // 返回领取投注返利奖励
    ReqReceiveDepositInviteBonus        = 400657; // 请求领取充值邀请奖励
    ResReceiveDepositInviteBonus        = 400658; // 返回领取充值邀请奖励
    ReqCheckDetailsData                 = 400659; // 请求查看明细数据
    ResCheckDetailsData                 = 400660; // 返回查看明细数据
    ReqReceiveVipBackCash               = 400661; // 请求领取vip返现
    ResReceiveVipBackCash               = 400662; // 返回领取vip返现
    ReqReceiveTgShareReward             = 400663; // 请求领取tg分享奖励
    ResReceiveTgShareReward             = 400664; // 返回领取tg分享奖励

    //任务 400801-400900
    ReqQuestData                        = 400801; //请求任务数据
    ResQuestData                        = 400802; //返回任务数据
    ReqReceiveQuestReward               = 400803; //请求领取任务奖励
    ResReceiveQuestReward               = 400804; //返回领取任务奖励
    ReqPreviousQuestsData               = 400805; //请求先前任务数据
    ResPreviousQuestsData               = 400806; //返回先前任务奖励

    //TCP 450001-450100
    ReqTcpTokenAuth                     = 450001; // 请求token认证
    ResTcpTokenAuth                     = 450002; // 返回token认证
    ReqTcpHeartBeat                     = 450003; // 请求心跳
    ResTcpHeartBeat                     = 450004; // 返回心跳
    ResTcpSysError                      = 450005; // 返回错误异常
    ResTcpCurrencyUpdate                = 450006; // 返回货币更新
    ResTcpReceiveInbox                  = 450007; // 返回接受邮件
    ResTcpVipClubExpChange              = 450008; // 返回vip经验改变
    ResTcpBulletinData                  = 450009; // 返回公告数据
    ResTcpGameNoteData                  = 450010; // 返回游戏记录
    ResTcpMyBetData                     = 450011; // 返回我的下注列表
    ResTcpDailyContestPrizePool         = 450012; // 返回每日竞赛奖池
    ReqTcpQuitAgentGame                 = 450013; // 请求退出三方游戏
    ResTcpQuitAgentGame                 = 450014; // 返回退出三方游戏
    ResTcpKickOutPlayer                 = 450015; // 返回踢出玩家
    ResTcpRechargeSuccess               = 450016; // 返回充值成功
    ResTcpWithdrawNotify                = 450017; // 返回提现通知
}