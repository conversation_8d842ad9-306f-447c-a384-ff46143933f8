syntax = "proto3";

package ProtoMessage;
option java_package = "com.proto";

import "CommonMessage.proto";

//请求充提货币数据
message ReqDepositWithdrawDataMessage {
   int32 msgID 		                                            = 1;
}

//返回充提货币数据
message ResDepositWithdrawDataMessage {
   int32 msgID 		                                            = 1;
   int32 error                                                  = 2; //错误码
   repeated DepositWithdrawData depositWithdrawData             = 3; //加密货币
}

message DepositWithdrawData {
   int32 currencyId                           = 1; //货币id
   repeated NetworkInfo networkList           = 2; //网络协议
}

message NetworkInfo {
  string network                             = 1;
  string remark                              = 2;
}

//请求充值数据
message ReqDepositDataMessage {
   int32 msgID 		                            = 1;
   int32 currencyId                           = 2; //货币id
   string network                             = 3; //网络协议
}

//返回充值法币数据
message ResDepositDataMessage {
   int32 msgID 		                            = 1;
   int32 error                                = 2; //错误码
   DepositCryptoData depositCryptoData        = 3; //加密货币
   repeated DepositFiatData depositFiatData   = 4; //法币
}

//充值法币数据
message DepositFiatData {
    int32 currencyId                           = 1; //货币
    int32 paymentMethod                        = 2; //支付方式
    string paymentMethodName                   = 3; //支付方式名字
    string paymentMethodLog                    = 4; //log地址
    double lowerLimit                          = 5; //单笔下限
    double upperLimit                          = 6; //单笔上限
    int32 channel                              = 7; //渠道id
    int32 category                             = 8; //类别 1.Bank transfer 2.E-wallet 3.Mobile Payment
    repeated RechargeAmount rechargeAmount     = 9; //充值金额
    repeated DepositInfo depositInfo           =10; //充值信息
    repeated FirstRecharge firstRechargeList   =11; //首充列表
    WithdrawAccount withdrawAccount            =12; //账号
    int32 sort                                 =13; //排序
}

message FirstRecharge {
    double rechargeAmountMin                   = 1; //充值金额
    double rechargeAmountMax                   = 2; //充值金额
    int32 rewardType                           = 3; //1.固定 2.比例
    double reward                              = 4;
    double maxReward                           = 5; //最大奖励
}

message RechargeAmount {
    double amount                              = 1; //金额
    double extra                               = 2; //额外
    int32 type                                 = 3; //1.固定值 2.百分比
    int32 tag                                  = 4; //1.hot
    double maxExtra                            = 5; //最大额外值
}

message DepositInfo {
    string field                               = 1; //字段
    repeated string params                     = 2; //下拉参数，
    string type                                = 3; //类型
}

//充值加密货币数据
message DepositCryptoData {
    int32 currencyId                           = 1; //货币
    string network                             = 2; //网络协议
    string walletAddress                       = 3; //钱包地址
    int32 minimum                              = 4; //最低充值额度
    double extra                               = 5; //额外
    int32 type                                 = 6; //1.固定值 2.百分比
    double minimumExtra                        = 7; //最小值额外
    double exchangeRate                        = 8; //兑换比例
}

//请求提现数据
message ReqWithdrawDataMessage {
   int32 msgID 		                          = 1;
   int32 currencyId                           = 2; //货币id
   string network                             = 3; //网络协议
}

//返回提现数据
message ResWithdrawDataMessage {
   int32 msgID 		                          = 1;
   int32 error                                = 2; //错误码
   WithdrawCryptoData withdrawCrypto          = 3; //加密货币
   repeated WithdrawFiatData withdrawFiatData = 4; //法币
   double available                           = 5; //可用余额
   double lockedFunds                         = 6; //锁住的余额
   double turnoverRate                        = 7; //打码进度值
   int32 freeTimes                            = 8; //免费次数
}

//提现加密货币数据
message WithdrawCryptoData {
    int32 currencyId                         = 1; //货币
    string network                           = 2; //网络协议
    double minimum                           = 3; //最低提现额度
    double fixedWithdrawalFee                = 4; //提现固定手续费
    double minimumWithdrawalFee              = 5; //提现最低手续费
    double withdrawalFeeRate                 = 6; //提现手续费费率
    repeated WithdrawAccount withdrawAccount = 7; //提现账号
    double maxMum                            = 8; //最高提现额度
    double exchangeRate                      = 9; //兑换比例
}

//提现法币数据
message WithdrawFiatData {
    int32 currencyId                         = 1; //货币
    int32 paymentMethod                      = 2; //支付方式
    string paymentMethodName                 = 3; //支付方式名字
    string paymentMethodLog                  = 4; //log地址
    double lowerLimit                        = 5; //单笔下限
    double upperLimit                        = 6; //单笔上限
    int32 channel                            = 7; //渠道id
    int32 category                           = 8; //类别 1.Bank transfer 2.E-wallet 3.Mobile Payments
    double fixedWithdrawalFee                = 9; //提现固定手续费
    double minimumWithdrawalFee              =10; //提现最低手续费
    double withdrawalFeeRate                 =11; //提现手续费费率
    repeated WithdrawInfo withdrawInfos      =12; //提现信息
    repeated WithdrawAccount withdrawAccount =13; //提现账号
}

message WithdrawInfo {
    string field                             = 1; //字段
    repeated string params                   = 2; //下拉参数
    string type                              = 3; //类型
}

//请求币商提现数据
message ReqCurrencyTraderWithdrawDataMessage {
    int32 msgID 		                           = 1;
    int32 currencyId                           = 2; //货币id
    string network                             = 3; //网络协议
}

//返回币商提现数据
message ResCurrencyTraderWithdrawDataMessage {
    int32 msgID 		                           = 1;
    int32 error                                = 2; //错误码
    repeated CurrencyTraderWithdrawData withdraw  = 3; //币商提现数据
    double available                           = 4; //可用余额
    double lockedFunds                         = 5; //锁住的余额
    double turnoverRate                        = 6; //打码进度值
    int32 freeTimes                            = 7; //免费次数
    int32 currencyTraderId                     = 8; //玩家的币商id
    CurrencyTraderWithdrawCryptoData withdrawCryptoData = 9; //提现加密
    repeated CurrencyTraderWithdrawFiatData withdrawFiatData = 10; //提现法币数据
}

message CurrencyTraderWithdrawData {
    int32 currencyTraderId                   = 1; //币商id
    double minimum                           = 2; //最低提现额度
    double maxMum                            = 3; //最高提现额度
    double withdrawalFeeRate                 = 4; //提现手续费费率
    string head                              = 5; //头像
    string name                              = 6; //名字
    string mediaLink                         = 7; //社媒连接
    string contactDetails                    = 8; //联系方式
    int32 withdrawTimes                      = 9; //提现次数
    string mediaIcon                         =10; //社媒icon
}

message CurrencyTraderWithdrawFiatData {
    int32 currencyId                         = 1; //货币
    int32 paymentMethod                      = 2; //支付方式
    string paymentMethodName                 = 3; //支付方式名字
    string paymentMethodLog                  = 4; //log地址
    repeated WithdrawInfo withdrawInfos      = 5; //提现信息
    repeated WithdrawAccount withdrawAccount = 6; //提现账号
}

message CurrencyTraderWithdrawCryptoData {
    repeated WithdrawAccount withdrawAccount = 1; //提现账号
}

//请求创建充值订单
message ReqCreateRechargeOrderMessage {
    int32 msgID 		                         = 1;
    int32 currencyId                         = 2; //货币
    int32 paymentMethod                      = 3; //支付方式
    string paymentMethodName                 = 4; //支付方式名字
    int32 channel                            = 5; //渠道id
    double amounts                           = 6; //金额
    RechargeAccount rechargeAccount          = 7; //充值账户
    FbInfo fbInfo                            = 8;
    int32 paymentSource                      = 9; //1.首充活动 2.每日充值 3.商场页面充值 4.vip页面充值 5.VIP签到弹窗页面充值 6.注册成功后功能弹窗 7.注册成功后活动弹窗 8.余额不足时活动弹窗
    PlayerInfo playerInfo                    =10; //玩家信息（服务器使用）
    double beforeBalance                     =11; //之前余额 （服务器使用）
}

//返回创建充值订单
message ResCreateRechargeOrderMessage {
    int32 msgID 		                         = 1;
    int32 error                              = 2; //错误码
    string payAddress                        = 3; //支付地址
    int32 type                               = 4; //1.返回链接 2.参数
    string channelOrderNo                    = 5; //渠道编号
    int64 expireTime                         = 6; //过期时间
    int64 orderId                            = 7; //订单id
    int32 currencyId                         = 8; //货币id
    string paymentMethod                     = 9; //支付方式
    double rechargeAmount                    =10; //充值金额
    string QRCode                            =11; //二维码
    int64 createTime                         =12; //订单创建时间
}

//请求创建提现订单
message ReqCreateWithdrawOrderMessage {
    int32 msgID                             = 1;
    int32 currencyId                        = 2; //货币
    WithdrawCrypto withdrawCrypto           = 3; //加密货币
    WithdrawFiat withdrawFiat               = 4; //法币
    double fee                              = 5; //手续费（服务器使用）
    double balance                          = 6; //余额 （服务器使用）
    int64 orderId                           = 7; //订单id （服务器使用）
    double drawStandard                     = 8; //要求打码量（服务器使用）
    double bettingVolume                    = 9; //实际洗码量（服务器使用）
    int32 exchangeCurrency                  =10; //兑换货币（服务器使用）
}

//返回创建提现订单
message ResCreateWithdrawOrderMessage {
    int32 msgID                            = 1;
    int32 error                            = 2; //错误码
}

//请求创建提现订单
message ReqCreateCurrencyTraderWithdrawOrderMessage {
    int32 msgID                             = 1;
    int32 currencyTraderId                  = 2;
    int32 currencyId                        = 3; //货币
    string network                          = 4; //网络协议
    string walletAddress                    = 5; //钱包地址
    double amount                           = 6; //金额
    string withdrawAccountId                = 7; //提现账户id
    double fee                              = 8; //手续费（服务器使用）
    double balance                          = 9; //余额 （服务器使用）
    int64 orderId                           =10; //订单id （服务器使用）
    double drawStandard                     =11; //要求打码量（服务器使用）
    double bettingVolume                    =12; //实际洗码量（服务器使用）
}

//返回创建提现订单
message ResCreateCurrencyTraderWithdrawOrderMessage {
    int32 msgID                            = 1;
    int32 error                            = 2; //错误码
}

//充值账号
message RechargeAccount {
    string extend_0                      = 1; //扩展
    string extend_1                      = 2; //扩展1
    string extend_2                      = 3; //扩展2
    string extend_3                      = 4; //扩展3
}

//提现加密货币
message WithdrawCrypto {
    int32 currencyId                      = 1; //货币
    string network                        = 2; //网络协议
    string walletAddress                  = 3; //钱包地址
    double amount                         = 4; //金额
}

//提现法币
message WithdrawFiat {
    int32 currencyId                      = 1; //货币
    int32 paymentMethod                   = 2; //支付方式
    string paymentMethodName              = 3; //支付方式名字
    int32 channel                         = 4; //渠道id
    string withdrawAccountId              = 5; //提现账号
    double amount                         = 6; //金额
}

//提现账号
message WithdrawAccount {
    string withdrawAccountId             = 1;
    string extend_0                      = 2; //扩展
    string extend_1                      = 3; //扩展1
    string extend_2                      = 4; //扩展2
    string extend_3                      = 5; //扩展3
}

//请求添加删除提现账户
message ReqAddDeleteWithdrawAccountMessage {
    int32 msgID                          = 1;
    int32 type                           = 2; //1.添加 2.删除
    string withdrawAccountId             = 3; //提现账户id
    WithdrawAccount withdrawAccount      = 4; //提现账户
    int32 currencyId                     = 5; //货币id
    int32 paymentMethod                  = 6; //支付方式
    string network                       = 7; //网络
    int32 channel                        = 8; //渠道
}

//返回添加删除提现账户
message ResAddDeleteWithdrawAccountMessage {
    int32 msgID                          = 1;
    int32 error                          = 2; //错误码
    int32 type                           = 3; //1.添加 2.删除
    string withdrawAccountId             = 4; //提现账户id
    WithdrawAccount withdrawAccount      = 5; //提现账户
}

//请求支付方式管理数据
message ReqPaymentMethodsDataMessage {
    int32 msgID                          = 1;
}

//返回支付方式管理数据
message ResPaymentMethodsDataMessage {
    int32 msgID                              = 1;
    int32 error                              = 2; //错误码
    repeated FiatPayment fiatPaymentList     = 3; //法币支付
    repeated CryptoPayment cryptoPaymentList = 4; //加密支付
}

message FiatPayment {
    int32 currencyId                         = 1; //货币id
    int32 paymentMethod                      = 2; //支付方式
    string paymentMethodName                 = 3; //支付方式名字
    repeated WithdrawInfo withdrawInfos      = 4; //提现信息
    repeated WithdrawAccount withdrawAccount = 5; //提现账号
}

message CryptoPayment {
    int32 currencyId                         = 1; //货币id
    string network                           = 2; //网络
    repeated WithdrawAccount withdrawAccount = 3; //提现账号
}




