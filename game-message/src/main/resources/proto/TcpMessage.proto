syntax = "proto3";

package ProtoMessage;
option java_package = "com.proto";

import "CommonMessage.proto";
import "BackStageMessage.proto";

//请求token认证
message ReqTcpTokenAuthMessage {
   int32 msgID             = 1;
   string token            = 2; //token
}

//返回token认证
message ResTcpTokenAuthMessage {
   int32 msgID             = 1;
   int32 error             = 2; //错误码
}

//请求心跳
message ReqTcpHeartBeatMessage {
	 int32 msgID 		        = 1;
	 string gameName        = 2; //游戏名字
	 string account         = 3; //账号（服务器使用）
	 string business_no     = 4; //商户 （服务器使用）
	 int64 userSessionId    = 5; //用户sessionId (服务器使用)
}

//返回心跳
message ResTcpHeartBeatMessage {
	 int32 msgID 		        = 1;
	 int32 error            = 2; //错误码
	 int64 systemTime 		  = 3; //服务器时间毫秒
}

//请求退出三方游戏
message ReqTcpQuitAgentGameMessage {
     int32 msgID 		      = 1;
}

//返回退出三方游戏
message ResTcpQuitAgentGameMessage {
     int32 msgID 		                     = 1;
     int32 error                         = 2; //错误码
}

//返回系统错误消息
message ResTcpSysErrorMessage {
	int32 msgID 		                       = 1;
	int32 error		                         = 2; // 错误码
	string context	                       = 3; // 错误文本
}

//返回公告信息(广播消息)
message ResTcpBulletinDataMessage {
  int32 msgID                            = 1;
	int32 error		                         = 2; // 错误码
	string business_no                     = 3; // （服务器使用）
  MaintainNoticeInfo maintainNoticeInfo  = 4; // 公告
}

//返回货币更新
message ResTcpCurrencyUpdateMessage {
    int32 msgID                     = 1;
    repeated DCurrencyItem cItem    = 2; //货币列表
    int32 reason                    = 3; //原因 1.奖励 2.消耗
    int32 source                    = 4; //来源
}

//返回接收邮件
message ResTcpReceiveInboxMessage {
    int32 msgID                     = 1;
	repeated InboxInfo inboxList      = 2; //邮件列表
}

//返回vip经验变化
message ResTcpVipClubExpChangeMessage {
   int32 msgID                      = 1;
   double curExp                    = 2; //当前经验
   double needExp                   = 3; //需要经验
   int32 vipLevel                   = 4; //等级
   double curRecharge               = 5; //当前充值
   double needRecharge              = 6; //需要充值
}

//返回游戏下注
message ResTcpGameNoteDataMessage {
    int32 msgID                     = 1;
    string business_no              = 2; //商户（服务器使用）
    repeated BetInfo allBet         = 3; //下注列表
    repeated BetInfo highRollers    = 4; //高手
    repeated BetInfo luckyBet       = 5; //幸运
}

//返回myBet
message ResTcpMyBetDataMessage {
    int32 msgID                    = 1;
    repeated BetInfo myBetList     = 2; //我的下注列表
}

//返回每日竞赛奖池
message ResTcpDailyContestPrizePoolMessage {
    int32 msgID                    = 1;
    string business_no             = 2; //商户（服务器使用）
    double prizePool               = 3; //奖池（usd）
}

//返回踢出玩家
message ResTcpKickOutPlayerMessage {
    int32 msgID                    = 1;
    int32 reason                   = 2;
}

//返回充值成功
message ResTcpRechargeSuccessMessage {
    int32 msgID                    = 1;
    int64 playerId                 = 2;
    int32 rechargeTimes            = 3; //充值次数
    int32 currencyId               = 4; //币种
    double currRecharge            = 5; //当前金额
    double totalRecharge           = 6; //总金额
    int64 orderId                  = 7; //订单id
    int32 status                   = 8; //1.complete 2.processing 3.failed 4.canceled
}

//返回提现通知
message ResTcpWithdrawNotifyMessage {
  int32 msgID                    = 1;
  int64 playerId                 = 2;
  int32 withdrawTimes            = 3; //提现次数
  int32 currencyId               = 4; //币种
  double amount                  = 5; //当前金额
  int64 orderId                  = 6; //订单id
  int32 status                   = 7; //1.complete 2.processing 3.failed 4.canceled
}


