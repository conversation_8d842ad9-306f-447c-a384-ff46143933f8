// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: TcpMessage.proto

package com.proto;

public final class TcpMessage {
  private TcpMessage() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ReqTcpTokenAuthMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ProtoMessage.ReqTcpTokenAuthMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    int getMsgID();

    /**
     * <pre>
     *token
     * </pre>
     *
     * <code>string token = 2;</code>
     * @return The token.
     */
    java.lang.String getToken();
    /**
     * <pre>
     *token
     * </pre>
     *
     * <code>string token = 2;</code>
     * @return The bytes for token.
     */
    com.google.protobuf.ByteString
        getTokenBytes();
  }
  /**
   * <pre>
   *请求token认证
   * </pre>
   *
   * Protobuf type {@code ProtoMessage.ReqTcpTokenAuthMessage}
   */
  public static final class ReqTcpTokenAuthMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ProtoMessage.ReqTcpTokenAuthMessage)
      ReqTcpTokenAuthMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqTcpTokenAuthMessage.newBuilder() to construct.
    private ReqTcpTokenAuthMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqTcpTokenAuthMessage() {
      token_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqTcpTokenAuthMessage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqTcpTokenAuthMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              msgID_ = input.readInt32();
              break;
            }
            case 18: {
              java.lang.String s = input.readStringRequireUtf8();

              token_ = s;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.proto.TcpMessage.internal_static_ProtoMessage_ReqTcpTokenAuthMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.proto.TcpMessage.internal_static_ProtoMessage_ReqTcpTokenAuthMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.proto.TcpMessage.ReqTcpTokenAuthMessage.class, com.proto.TcpMessage.ReqTcpTokenAuthMessage.Builder.class);
    }

    public static final int MSGID_FIELD_NUMBER = 1;
    private int msgID_;
    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    @java.lang.Override
    public int getMsgID() {
      return msgID_;
    }

    public static final int TOKEN_FIELD_NUMBER = 2;
    private volatile java.lang.Object token_;
    /**
     * <pre>
     *token
     * </pre>
     *
     * <code>string token = 2;</code>
     * @return The token.
     */
    @java.lang.Override
    public java.lang.String getToken() {
      java.lang.Object ref = token_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        token_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *token
     * </pre>
     *
     * <code>string token = 2;</code>
     * @return The bytes for token.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getTokenBytes() {
      java.lang.Object ref = token_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        token_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (msgID_ != 0) {
        output.writeInt32(1, msgID_);
      }
      if (!getTokenBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, token_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (msgID_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, msgID_);
      }
      if (!getTokenBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, token_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.proto.TcpMessage.ReqTcpTokenAuthMessage)) {
        return super.equals(obj);
      }
      com.proto.TcpMessage.ReqTcpTokenAuthMessage other = (com.proto.TcpMessage.ReqTcpTokenAuthMessage) obj;

      if (getMsgID()
          != other.getMsgID()) return false;
      if (!getToken()
          .equals(other.getToken())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + MSGID_FIELD_NUMBER;
      hash = (53 * hash) + getMsgID();
      hash = (37 * hash) + TOKEN_FIELD_NUMBER;
      hash = (53 * hash) + getToken().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.proto.TcpMessage.ReqTcpTokenAuthMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ReqTcpTokenAuthMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ReqTcpTokenAuthMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ReqTcpTokenAuthMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ReqTcpTokenAuthMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ReqTcpTokenAuthMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ReqTcpTokenAuthMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ReqTcpTokenAuthMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.TcpMessage.ReqTcpTokenAuthMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ReqTcpTokenAuthMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.TcpMessage.ReqTcpTokenAuthMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ReqTcpTokenAuthMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.proto.TcpMessage.ReqTcpTokenAuthMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *请求token认证
     * </pre>
     *
     * Protobuf type {@code ProtoMessage.ReqTcpTokenAuthMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ProtoMessage.ReqTcpTokenAuthMessage)
        com.proto.TcpMessage.ReqTcpTokenAuthMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ReqTcpTokenAuthMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ReqTcpTokenAuthMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.proto.TcpMessage.ReqTcpTokenAuthMessage.class, com.proto.TcpMessage.ReqTcpTokenAuthMessage.Builder.class);
      }

      // Construct using com.proto.TcpMessage.ReqTcpTokenAuthMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        msgID_ = 0;

        token_ = "";

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ReqTcpTokenAuthMessage_descriptor;
      }

      @java.lang.Override
      public com.proto.TcpMessage.ReqTcpTokenAuthMessage getDefaultInstanceForType() {
        return com.proto.TcpMessage.ReqTcpTokenAuthMessage.getDefaultInstance();
      }

      @java.lang.Override
      public com.proto.TcpMessage.ReqTcpTokenAuthMessage build() {
        com.proto.TcpMessage.ReqTcpTokenAuthMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.proto.TcpMessage.ReqTcpTokenAuthMessage buildPartial() {
        com.proto.TcpMessage.ReqTcpTokenAuthMessage result = new com.proto.TcpMessage.ReqTcpTokenAuthMessage(this);
        result.msgID_ = msgID_;
        result.token_ = token_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.proto.TcpMessage.ReqTcpTokenAuthMessage) {
          return mergeFrom((com.proto.TcpMessage.ReqTcpTokenAuthMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.proto.TcpMessage.ReqTcpTokenAuthMessage other) {
        if (other == com.proto.TcpMessage.ReqTcpTokenAuthMessage.getDefaultInstance()) return this;
        if (other.getMsgID() != 0) {
          setMsgID(other.getMsgID());
        }
        if (!other.getToken().isEmpty()) {
          token_ = other.token_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.proto.TcpMessage.ReqTcpTokenAuthMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.proto.TcpMessage.ReqTcpTokenAuthMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int msgID_ ;
      /**
       * <code>int32 msgID = 1;</code>
       * @return The msgID.
       */
      @java.lang.Override
      public int getMsgID() {
        return msgID_;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @param value The msgID to set.
       * @return This builder for chaining.
       */
      public Builder setMsgID(int value) {
        
        msgID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgID() {
        
        msgID_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object token_ = "";
      /**
       * <pre>
       *token
       * </pre>
       *
       * <code>string token = 2;</code>
       * @return The token.
       */
      public java.lang.String getToken() {
        java.lang.Object ref = token_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          token_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *token
       * </pre>
       *
       * <code>string token = 2;</code>
       * @return The bytes for token.
       */
      public com.google.protobuf.ByteString
          getTokenBytes() {
        java.lang.Object ref = token_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          token_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *token
       * </pre>
       *
       * <code>string token = 2;</code>
       * @param value The token to set.
       * @return This builder for chaining.
       */
      public Builder setToken(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        token_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *token
       * </pre>
       *
       * <code>string token = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearToken() {
        
        token_ = getDefaultInstance().getToken();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *token
       * </pre>
       *
       * <code>string token = 2;</code>
       * @param value The bytes for token to set.
       * @return This builder for chaining.
       */
      public Builder setTokenBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        token_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ProtoMessage.ReqTcpTokenAuthMessage)
    }

    // @@protoc_insertion_point(class_scope:ProtoMessage.ReqTcpTokenAuthMessage)
    private static final com.proto.TcpMessage.ReqTcpTokenAuthMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.proto.TcpMessage.ReqTcpTokenAuthMessage();
    }

    public static com.proto.TcpMessage.ReqTcpTokenAuthMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqTcpTokenAuthMessage>
        PARSER = new com.google.protobuf.AbstractParser<ReqTcpTokenAuthMessage>() {
      @java.lang.Override
      public ReqTcpTokenAuthMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqTcpTokenAuthMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqTcpTokenAuthMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqTcpTokenAuthMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.proto.TcpMessage.ReqTcpTokenAuthMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResTcpTokenAuthMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ProtoMessage.ResTcpTokenAuthMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    int getMsgID();

    /**
     * <pre>
     *错误码
     * </pre>
     *
     * <code>int32 error = 2;</code>
     * @return The error.
     */
    int getError();
  }
  /**
   * <pre>
   *返回token认证
   * </pre>
   *
   * Protobuf type {@code ProtoMessage.ResTcpTokenAuthMessage}
   */
  public static final class ResTcpTokenAuthMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ProtoMessage.ResTcpTokenAuthMessage)
      ResTcpTokenAuthMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResTcpTokenAuthMessage.newBuilder() to construct.
    private ResTcpTokenAuthMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResTcpTokenAuthMessage() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResTcpTokenAuthMessage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResTcpTokenAuthMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              msgID_ = input.readInt32();
              break;
            }
            case 16: {

              error_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpTokenAuthMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpTokenAuthMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.proto.TcpMessage.ResTcpTokenAuthMessage.class, com.proto.TcpMessage.ResTcpTokenAuthMessage.Builder.class);
    }

    public static final int MSGID_FIELD_NUMBER = 1;
    private int msgID_;
    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    @java.lang.Override
    public int getMsgID() {
      return msgID_;
    }

    public static final int ERROR_FIELD_NUMBER = 2;
    private int error_;
    /**
     * <pre>
     *错误码
     * </pre>
     *
     * <code>int32 error = 2;</code>
     * @return The error.
     */
    @java.lang.Override
    public int getError() {
      return error_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (msgID_ != 0) {
        output.writeInt32(1, msgID_);
      }
      if (error_ != 0) {
        output.writeInt32(2, error_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (msgID_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, msgID_);
      }
      if (error_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, error_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.proto.TcpMessage.ResTcpTokenAuthMessage)) {
        return super.equals(obj);
      }
      com.proto.TcpMessage.ResTcpTokenAuthMessage other = (com.proto.TcpMessage.ResTcpTokenAuthMessage) obj;

      if (getMsgID()
          != other.getMsgID()) return false;
      if (getError()
          != other.getError()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + MSGID_FIELD_NUMBER;
      hash = (53 * hash) + getMsgID();
      hash = (37 * hash) + ERROR_FIELD_NUMBER;
      hash = (53 * hash) + getError();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.proto.TcpMessage.ResTcpTokenAuthMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ResTcpTokenAuthMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpTokenAuthMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ResTcpTokenAuthMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpTokenAuthMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ResTcpTokenAuthMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpTokenAuthMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ResTcpTokenAuthMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpTokenAuthMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ResTcpTokenAuthMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpTokenAuthMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ResTcpTokenAuthMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.proto.TcpMessage.ResTcpTokenAuthMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *返回token认证
     * </pre>
     *
     * Protobuf type {@code ProtoMessage.ResTcpTokenAuthMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ProtoMessage.ResTcpTokenAuthMessage)
        com.proto.TcpMessage.ResTcpTokenAuthMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpTokenAuthMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpTokenAuthMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.proto.TcpMessage.ResTcpTokenAuthMessage.class, com.proto.TcpMessage.ResTcpTokenAuthMessage.Builder.class);
      }

      // Construct using com.proto.TcpMessage.ResTcpTokenAuthMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        msgID_ = 0;

        error_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpTokenAuthMessage_descriptor;
      }

      @java.lang.Override
      public com.proto.TcpMessage.ResTcpTokenAuthMessage getDefaultInstanceForType() {
        return com.proto.TcpMessage.ResTcpTokenAuthMessage.getDefaultInstance();
      }

      @java.lang.Override
      public com.proto.TcpMessage.ResTcpTokenAuthMessage build() {
        com.proto.TcpMessage.ResTcpTokenAuthMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.proto.TcpMessage.ResTcpTokenAuthMessage buildPartial() {
        com.proto.TcpMessage.ResTcpTokenAuthMessage result = new com.proto.TcpMessage.ResTcpTokenAuthMessage(this);
        result.msgID_ = msgID_;
        result.error_ = error_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.proto.TcpMessage.ResTcpTokenAuthMessage) {
          return mergeFrom((com.proto.TcpMessage.ResTcpTokenAuthMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.proto.TcpMessage.ResTcpTokenAuthMessage other) {
        if (other == com.proto.TcpMessage.ResTcpTokenAuthMessage.getDefaultInstance()) return this;
        if (other.getMsgID() != 0) {
          setMsgID(other.getMsgID());
        }
        if (other.getError() != 0) {
          setError(other.getError());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.proto.TcpMessage.ResTcpTokenAuthMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.proto.TcpMessage.ResTcpTokenAuthMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int msgID_ ;
      /**
       * <code>int32 msgID = 1;</code>
       * @return The msgID.
       */
      @java.lang.Override
      public int getMsgID() {
        return msgID_;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @param value The msgID to set.
       * @return This builder for chaining.
       */
      public Builder setMsgID(int value) {
        
        msgID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgID() {
        
        msgID_ = 0;
        onChanged();
        return this;
      }

      private int error_ ;
      /**
       * <pre>
       *错误码
       * </pre>
       *
       * <code>int32 error = 2;</code>
       * @return The error.
       */
      @java.lang.Override
      public int getError() {
        return error_;
      }
      /**
       * <pre>
       *错误码
       * </pre>
       *
       * <code>int32 error = 2;</code>
       * @param value The error to set.
       * @return This builder for chaining.
       */
      public Builder setError(int value) {
        
        error_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *错误码
       * </pre>
       *
       * <code>int32 error = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearError() {
        
        error_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ProtoMessage.ResTcpTokenAuthMessage)
    }

    // @@protoc_insertion_point(class_scope:ProtoMessage.ResTcpTokenAuthMessage)
    private static final com.proto.TcpMessage.ResTcpTokenAuthMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.proto.TcpMessage.ResTcpTokenAuthMessage();
    }

    public static com.proto.TcpMessage.ResTcpTokenAuthMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResTcpTokenAuthMessage>
        PARSER = new com.google.protobuf.AbstractParser<ResTcpTokenAuthMessage>() {
      @java.lang.Override
      public ResTcpTokenAuthMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResTcpTokenAuthMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResTcpTokenAuthMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResTcpTokenAuthMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.proto.TcpMessage.ResTcpTokenAuthMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqTcpHeartBeatMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ProtoMessage.ReqTcpHeartBeatMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    int getMsgID();

    /**
     * <pre>
     *游戏名字
     * </pre>
     *
     * <code>string gameName = 2;</code>
     * @return The gameName.
     */
    java.lang.String getGameName();
    /**
     * <pre>
     *游戏名字
     * </pre>
     *
     * <code>string gameName = 2;</code>
     * @return The bytes for gameName.
     */
    com.google.protobuf.ByteString
        getGameNameBytes();

    /**
     * <pre>
     *账号（服务器使用）
     * </pre>
     *
     * <code>string account = 3;</code>
     * @return The account.
     */
    java.lang.String getAccount();
    /**
     * <pre>
     *账号（服务器使用）
     * </pre>
     *
     * <code>string account = 3;</code>
     * @return The bytes for account.
     */
    com.google.protobuf.ByteString
        getAccountBytes();

    /**
     * <pre>
     *商户 （服务器使用）
     * </pre>
     *
     * <code>string business_no = 4;</code>
     * @return The businessNo.
     */
    java.lang.String getBusinessNo();
    /**
     * <pre>
     *商户 （服务器使用）
     * </pre>
     *
     * <code>string business_no = 4;</code>
     * @return The bytes for businessNo.
     */
    com.google.protobuf.ByteString
        getBusinessNoBytes();

    /**
     * <pre>
     *用户sessionId (服务器使用)
     * </pre>
     *
     * <code>int64 userSessionId = 5;</code>
     * @return The userSessionId.
     */
    long getUserSessionId();
  }
  /**
   * <pre>
   *请求心跳
   * </pre>
   *
   * Protobuf type {@code ProtoMessage.ReqTcpHeartBeatMessage}
   */
  public static final class ReqTcpHeartBeatMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ProtoMessage.ReqTcpHeartBeatMessage)
      ReqTcpHeartBeatMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqTcpHeartBeatMessage.newBuilder() to construct.
    private ReqTcpHeartBeatMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqTcpHeartBeatMessage() {
      gameName_ = "";
      account_ = "";
      businessNo_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqTcpHeartBeatMessage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqTcpHeartBeatMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              msgID_ = input.readInt32();
              break;
            }
            case 18: {
              java.lang.String s = input.readStringRequireUtf8();

              gameName_ = s;
              break;
            }
            case 26: {
              java.lang.String s = input.readStringRequireUtf8();

              account_ = s;
              break;
            }
            case 34: {
              java.lang.String s = input.readStringRequireUtf8();

              businessNo_ = s;
              break;
            }
            case 40: {

              userSessionId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.proto.TcpMessage.internal_static_ProtoMessage_ReqTcpHeartBeatMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.proto.TcpMessage.internal_static_ProtoMessage_ReqTcpHeartBeatMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.proto.TcpMessage.ReqTcpHeartBeatMessage.class, com.proto.TcpMessage.ReqTcpHeartBeatMessage.Builder.class);
    }

    public static final int MSGID_FIELD_NUMBER = 1;
    private int msgID_;
    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    @java.lang.Override
    public int getMsgID() {
      return msgID_;
    }

    public static final int GAMENAME_FIELD_NUMBER = 2;
    private volatile java.lang.Object gameName_;
    /**
     * <pre>
     *游戏名字
     * </pre>
     *
     * <code>string gameName = 2;</code>
     * @return The gameName.
     */
    @java.lang.Override
    public java.lang.String getGameName() {
      java.lang.Object ref = gameName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        gameName_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *游戏名字
     * </pre>
     *
     * <code>string gameName = 2;</code>
     * @return The bytes for gameName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getGameNameBytes() {
      java.lang.Object ref = gameName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        gameName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ACCOUNT_FIELD_NUMBER = 3;
    private volatile java.lang.Object account_;
    /**
     * <pre>
     *账号（服务器使用）
     * </pre>
     *
     * <code>string account = 3;</code>
     * @return The account.
     */
    @java.lang.Override
    public java.lang.String getAccount() {
      java.lang.Object ref = account_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        account_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *账号（服务器使用）
     * </pre>
     *
     * <code>string account = 3;</code>
     * @return The bytes for account.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getAccountBytes() {
      java.lang.Object ref = account_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        account_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int BUSINESS_NO_FIELD_NUMBER = 4;
    private volatile java.lang.Object businessNo_;
    /**
     * <pre>
     *商户 （服务器使用）
     * </pre>
     *
     * <code>string business_no = 4;</code>
     * @return The businessNo.
     */
    @java.lang.Override
    public java.lang.String getBusinessNo() {
      java.lang.Object ref = businessNo_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        businessNo_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *商户 （服务器使用）
     * </pre>
     *
     * <code>string business_no = 4;</code>
     * @return The bytes for businessNo.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getBusinessNoBytes() {
      java.lang.Object ref = businessNo_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        businessNo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int USERSESSIONID_FIELD_NUMBER = 5;
    private long userSessionId_;
    /**
     * <pre>
     *用户sessionId (服务器使用)
     * </pre>
     *
     * <code>int64 userSessionId = 5;</code>
     * @return The userSessionId.
     */
    @java.lang.Override
    public long getUserSessionId() {
      return userSessionId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (msgID_ != 0) {
        output.writeInt32(1, msgID_);
      }
      if (!getGameNameBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, gameName_);
      }
      if (!getAccountBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, account_);
      }
      if (!getBusinessNoBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, businessNo_);
      }
      if (userSessionId_ != 0L) {
        output.writeInt64(5, userSessionId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (msgID_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, msgID_);
      }
      if (!getGameNameBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, gameName_);
      }
      if (!getAccountBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, account_);
      }
      if (!getBusinessNoBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, businessNo_);
      }
      if (userSessionId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(5, userSessionId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.proto.TcpMessage.ReqTcpHeartBeatMessage)) {
        return super.equals(obj);
      }
      com.proto.TcpMessage.ReqTcpHeartBeatMessage other = (com.proto.TcpMessage.ReqTcpHeartBeatMessage) obj;

      if (getMsgID()
          != other.getMsgID()) return false;
      if (!getGameName()
          .equals(other.getGameName())) return false;
      if (!getAccount()
          .equals(other.getAccount())) return false;
      if (!getBusinessNo()
          .equals(other.getBusinessNo())) return false;
      if (getUserSessionId()
          != other.getUserSessionId()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + MSGID_FIELD_NUMBER;
      hash = (53 * hash) + getMsgID();
      hash = (37 * hash) + GAMENAME_FIELD_NUMBER;
      hash = (53 * hash) + getGameName().hashCode();
      hash = (37 * hash) + ACCOUNT_FIELD_NUMBER;
      hash = (53 * hash) + getAccount().hashCode();
      hash = (37 * hash) + BUSINESS_NO_FIELD_NUMBER;
      hash = (53 * hash) + getBusinessNo().hashCode();
      hash = (37 * hash) + USERSESSIONID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getUserSessionId());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.proto.TcpMessage.ReqTcpHeartBeatMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ReqTcpHeartBeatMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ReqTcpHeartBeatMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ReqTcpHeartBeatMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ReqTcpHeartBeatMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ReqTcpHeartBeatMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ReqTcpHeartBeatMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ReqTcpHeartBeatMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.TcpMessage.ReqTcpHeartBeatMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ReqTcpHeartBeatMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.TcpMessage.ReqTcpHeartBeatMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ReqTcpHeartBeatMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.proto.TcpMessage.ReqTcpHeartBeatMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *请求心跳
     * </pre>
     *
     * Protobuf type {@code ProtoMessage.ReqTcpHeartBeatMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ProtoMessage.ReqTcpHeartBeatMessage)
        com.proto.TcpMessage.ReqTcpHeartBeatMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ReqTcpHeartBeatMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ReqTcpHeartBeatMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.proto.TcpMessage.ReqTcpHeartBeatMessage.class, com.proto.TcpMessage.ReqTcpHeartBeatMessage.Builder.class);
      }

      // Construct using com.proto.TcpMessage.ReqTcpHeartBeatMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        msgID_ = 0;

        gameName_ = "";

        account_ = "";

        businessNo_ = "";

        userSessionId_ = 0L;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ReqTcpHeartBeatMessage_descriptor;
      }

      @java.lang.Override
      public com.proto.TcpMessage.ReqTcpHeartBeatMessage getDefaultInstanceForType() {
        return com.proto.TcpMessage.ReqTcpHeartBeatMessage.getDefaultInstance();
      }

      @java.lang.Override
      public com.proto.TcpMessage.ReqTcpHeartBeatMessage build() {
        com.proto.TcpMessage.ReqTcpHeartBeatMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.proto.TcpMessage.ReqTcpHeartBeatMessage buildPartial() {
        com.proto.TcpMessage.ReqTcpHeartBeatMessage result = new com.proto.TcpMessage.ReqTcpHeartBeatMessage(this);
        result.msgID_ = msgID_;
        result.gameName_ = gameName_;
        result.account_ = account_;
        result.businessNo_ = businessNo_;
        result.userSessionId_ = userSessionId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.proto.TcpMessage.ReqTcpHeartBeatMessage) {
          return mergeFrom((com.proto.TcpMessage.ReqTcpHeartBeatMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.proto.TcpMessage.ReqTcpHeartBeatMessage other) {
        if (other == com.proto.TcpMessage.ReqTcpHeartBeatMessage.getDefaultInstance()) return this;
        if (other.getMsgID() != 0) {
          setMsgID(other.getMsgID());
        }
        if (!other.getGameName().isEmpty()) {
          gameName_ = other.gameName_;
          onChanged();
        }
        if (!other.getAccount().isEmpty()) {
          account_ = other.account_;
          onChanged();
        }
        if (!other.getBusinessNo().isEmpty()) {
          businessNo_ = other.businessNo_;
          onChanged();
        }
        if (other.getUserSessionId() != 0L) {
          setUserSessionId(other.getUserSessionId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.proto.TcpMessage.ReqTcpHeartBeatMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.proto.TcpMessage.ReqTcpHeartBeatMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int msgID_ ;
      /**
       * <code>int32 msgID = 1;</code>
       * @return The msgID.
       */
      @java.lang.Override
      public int getMsgID() {
        return msgID_;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @param value The msgID to set.
       * @return This builder for chaining.
       */
      public Builder setMsgID(int value) {
        
        msgID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgID() {
        
        msgID_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object gameName_ = "";
      /**
       * <pre>
       *游戏名字
       * </pre>
       *
       * <code>string gameName = 2;</code>
       * @return The gameName.
       */
      public java.lang.String getGameName() {
        java.lang.Object ref = gameName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          gameName_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *游戏名字
       * </pre>
       *
       * <code>string gameName = 2;</code>
       * @return The bytes for gameName.
       */
      public com.google.protobuf.ByteString
          getGameNameBytes() {
        java.lang.Object ref = gameName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          gameName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *游戏名字
       * </pre>
       *
       * <code>string gameName = 2;</code>
       * @param value The gameName to set.
       * @return This builder for chaining.
       */
      public Builder setGameName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        gameName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *游戏名字
       * </pre>
       *
       * <code>string gameName = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearGameName() {
        
        gameName_ = getDefaultInstance().getGameName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *游戏名字
       * </pre>
       *
       * <code>string gameName = 2;</code>
       * @param value The bytes for gameName to set.
       * @return This builder for chaining.
       */
      public Builder setGameNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        gameName_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object account_ = "";
      /**
       * <pre>
       *账号（服务器使用）
       * </pre>
       *
       * <code>string account = 3;</code>
       * @return The account.
       */
      public java.lang.String getAccount() {
        java.lang.Object ref = account_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          account_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *账号（服务器使用）
       * </pre>
       *
       * <code>string account = 3;</code>
       * @return The bytes for account.
       */
      public com.google.protobuf.ByteString
          getAccountBytes() {
        java.lang.Object ref = account_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          account_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *账号（服务器使用）
       * </pre>
       *
       * <code>string account = 3;</code>
       * @param value The account to set.
       * @return This builder for chaining.
       */
      public Builder setAccount(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        account_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *账号（服务器使用）
       * </pre>
       *
       * <code>string account = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearAccount() {
        
        account_ = getDefaultInstance().getAccount();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *账号（服务器使用）
       * </pre>
       *
       * <code>string account = 3;</code>
       * @param value The bytes for account to set.
       * @return This builder for chaining.
       */
      public Builder setAccountBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        account_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object businessNo_ = "";
      /**
       * <pre>
       *商户 （服务器使用）
       * </pre>
       *
       * <code>string business_no = 4;</code>
       * @return The businessNo.
       */
      public java.lang.String getBusinessNo() {
        java.lang.Object ref = businessNo_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          businessNo_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *商户 （服务器使用）
       * </pre>
       *
       * <code>string business_no = 4;</code>
       * @return The bytes for businessNo.
       */
      public com.google.protobuf.ByteString
          getBusinessNoBytes() {
        java.lang.Object ref = businessNo_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          businessNo_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *商户 （服务器使用）
       * </pre>
       *
       * <code>string business_no = 4;</code>
       * @param value The businessNo to set.
       * @return This builder for chaining.
       */
      public Builder setBusinessNo(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        businessNo_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *商户 （服务器使用）
       * </pre>
       *
       * <code>string business_no = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearBusinessNo() {
        
        businessNo_ = getDefaultInstance().getBusinessNo();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *商户 （服务器使用）
       * </pre>
       *
       * <code>string business_no = 4;</code>
       * @param value The bytes for businessNo to set.
       * @return This builder for chaining.
       */
      public Builder setBusinessNoBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        businessNo_ = value;
        onChanged();
        return this;
      }

      private long userSessionId_ ;
      /**
       * <pre>
       *用户sessionId (服务器使用)
       * </pre>
       *
       * <code>int64 userSessionId = 5;</code>
       * @return The userSessionId.
       */
      @java.lang.Override
      public long getUserSessionId() {
        return userSessionId_;
      }
      /**
       * <pre>
       *用户sessionId (服务器使用)
       * </pre>
       *
       * <code>int64 userSessionId = 5;</code>
       * @param value The userSessionId to set.
       * @return This builder for chaining.
       */
      public Builder setUserSessionId(long value) {
        
        userSessionId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *用户sessionId (服务器使用)
       * </pre>
       *
       * <code>int64 userSessionId = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearUserSessionId() {
        
        userSessionId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ProtoMessage.ReqTcpHeartBeatMessage)
    }

    // @@protoc_insertion_point(class_scope:ProtoMessage.ReqTcpHeartBeatMessage)
    private static final com.proto.TcpMessage.ReqTcpHeartBeatMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.proto.TcpMessage.ReqTcpHeartBeatMessage();
    }

    public static com.proto.TcpMessage.ReqTcpHeartBeatMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqTcpHeartBeatMessage>
        PARSER = new com.google.protobuf.AbstractParser<ReqTcpHeartBeatMessage>() {
      @java.lang.Override
      public ReqTcpHeartBeatMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqTcpHeartBeatMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqTcpHeartBeatMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqTcpHeartBeatMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.proto.TcpMessage.ReqTcpHeartBeatMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResTcpHeartBeatMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ProtoMessage.ResTcpHeartBeatMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    int getMsgID();

    /**
     * <pre>
     *错误码
     * </pre>
     *
     * <code>int32 error = 2;</code>
     * @return The error.
     */
    int getError();

    /**
     * <pre>
     *服务器时间毫秒
     * </pre>
     *
     * <code>int64 systemTime = 3;</code>
     * @return The systemTime.
     */
    long getSystemTime();
  }
  /**
   * <pre>
   *返回心跳
   * </pre>
   *
   * Protobuf type {@code ProtoMessage.ResTcpHeartBeatMessage}
   */
  public static final class ResTcpHeartBeatMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ProtoMessage.ResTcpHeartBeatMessage)
      ResTcpHeartBeatMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResTcpHeartBeatMessage.newBuilder() to construct.
    private ResTcpHeartBeatMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResTcpHeartBeatMessage() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResTcpHeartBeatMessage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResTcpHeartBeatMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              msgID_ = input.readInt32();
              break;
            }
            case 16: {

              error_ = input.readInt32();
              break;
            }
            case 24: {

              systemTime_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpHeartBeatMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpHeartBeatMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.proto.TcpMessage.ResTcpHeartBeatMessage.class, com.proto.TcpMessage.ResTcpHeartBeatMessage.Builder.class);
    }

    public static final int MSGID_FIELD_NUMBER = 1;
    private int msgID_;
    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    @java.lang.Override
    public int getMsgID() {
      return msgID_;
    }

    public static final int ERROR_FIELD_NUMBER = 2;
    private int error_;
    /**
     * <pre>
     *错误码
     * </pre>
     *
     * <code>int32 error = 2;</code>
     * @return The error.
     */
    @java.lang.Override
    public int getError() {
      return error_;
    }

    public static final int SYSTEMTIME_FIELD_NUMBER = 3;
    private long systemTime_;
    /**
     * <pre>
     *服务器时间毫秒
     * </pre>
     *
     * <code>int64 systemTime = 3;</code>
     * @return The systemTime.
     */
    @java.lang.Override
    public long getSystemTime() {
      return systemTime_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (msgID_ != 0) {
        output.writeInt32(1, msgID_);
      }
      if (error_ != 0) {
        output.writeInt32(2, error_);
      }
      if (systemTime_ != 0L) {
        output.writeInt64(3, systemTime_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (msgID_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, msgID_);
      }
      if (error_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, error_);
      }
      if (systemTime_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, systemTime_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.proto.TcpMessage.ResTcpHeartBeatMessage)) {
        return super.equals(obj);
      }
      com.proto.TcpMessage.ResTcpHeartBeatMessage other = (com.proto.TcpMessage.ResTcpHeartBeatMessage) obj;

      if (getMsgID()
          != other.getMsgID()) return false;
      if (getError()
          != other.getError()) return false;
      if (getSystemTime()
          != other.getSystemTime()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + MSGID_FIELD_NUMBER;
      hash = (53 * hash) + getMsgID();
      hash = (37 * hash) + ERROR_FIELD_NUMBER;
      hash = (53 * hash) + getError();
      hash = (37 * hash) + SYSTEMTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getSystemTime());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.proto.TcpMessage.ResTcpHeartBeatMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ResTcpHeartBeatMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpHeartBeatMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ResTcpHeartBeatMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpHeartBeatMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ResTcpHeartBeatMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpHeartBeatMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ResTcpHeartBeatMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpHeartBeatMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ResTcpHeartBeatMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpHeartBeatMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ResTcpHeartBeatMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.proto.TcpMessage.ResTcpHeartBeatMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *返回心跳
     * </pre>
     *
     * Protobuf type {@code ProtoMessage.ResTcpHeartBeatMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ProtoMessage.ResTcpHeartBeatMessage)
        com.proto.TcpMessage.ResTcpHeartBeatMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpHeartBeatMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpHeartBeatMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.proto.TcpMessage.ResTcpHeartBeatMessage.class, com.proto.TcpMessage.ResTcpHeartBeatMessage.Builder.class);
      }

      // Construct using com.proto.TcpMessage.ResTcpHeartBeatMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        msgID_ = 0;

        error_ = 0;

        systemTime_ = 0L;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpHeartBeatMessage_descriptor;
      }

      @java.lang.Override
      public com.proto.TcpMessage.ResTcpHeartBeatMessage getDefaultInstanceForType() {
        return com.proto.TcpMessage.ResTcpHeartBeatMessage.getDefaultInstance();
      }

      @java.lang.Override
      public com.proto.TcpMessage.ResTcpHeartBeatMessage build() {
        com.proto.TcpMessage.ResTcpHeartBeatMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.proto.TcpMessage.ResTcpHeartBeatMessage buildPartial() {
        com.proto.TcpMessage.ResTcpHeartBeatMessage result = new com.proto.TcpMessage.ResTcpHeartBeatMessage(this);
        result.msgID_ = msgID_;
        result.error_ = error_;
        result.systemTime_ = systemTime_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.proto.TcpMessage.ResTcpHeartBeatMessage) {
          return mergeFrom((com.proto.TcpMessage.ResTcpHeartBeatMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.proto.TcpMessage.ResTcpHeartBeatMessage other) {
        if (other == com.proto.TcpMessage.ResTcpHeartBeatMessage.getDefaultInstance()) return this;
        if (other.getMsgID() != 0) {
          setMsgID(other.getMsgID());
        }
        if (other.getError() != 0) {
          setError(other.getError());
        }
        if (other.getSystemTime() != 0L) {
          setSystemTime(other.getSystemTime());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.proto.TcpMessage.ResTcpHeartBeatMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.proto.TcpMessage.ResTcpHeartBeatMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int msgID_ ;
      /**
       * <code>int32 msgID = 1;</code>
       * @return The msgID.
       */
      @java.lang.Override
      public int getMsgID() {
        return msgID_;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @param value The msgID to set.
       * @return This builder for chaining.
       */
      public Builder setMsgID(int value) {
        
        msgID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgID() {
        
        msgID_ = 0;
        onChanged();
        return this;
      }

      private int error_ ;
      /**
       * <pre>
       *错误码
       * </pre>
       *
       * <code>int32 error = 2;</code>
       * @return The error.
       */
      @java.lang.Override
      public int getError() {
        return error_;
      }
      /**
       * <pre>
       *错误码
       * </pre>
       *
       * <code>int32 error = 2;</code>
       * @param value The error to set.
       * @return This builder for chaining.
       */
      public Builder setError(int value) {
        
        error_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *错误码
       * </pre>
       *
       * <code>int32 error = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearError() {
        
        error_ = 0;
        onChanged();
        return this;
      }

      private long systemTime_ ;
      /**
       * <pre>
       *服务器时间毫秒
       * </pre>
       *
       * <code>int64 systemTime = 3;</code>
       * @return The systemTime.
       */
      @java.lang.Override
      public long getSystemTime() {
        return systemTime_;
      }
      /**
       * <pre>
       *服务器时间毫秒
       * </pre>
       *
       * <code>int64 systemTime = 3;</code>
       * @param value The systemTime to set.
       * @return This builder for chaining.
       */
      public Builder setSystemTime(long value) {
        
        systemTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *服务器时间毫秒
       * </pre>
       *
       * <code>int64 systemTime = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearSystemTime() {
        
        systemTime_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ProtoMessage.ResTcpHeartBeatMessage)
    }

    // @@protoc_insertion_point(class_scope:ProtoMessage.ResTcpHeartBeatMessage)
    private static final com.proto.TcpMessage.ResTcpHeartBeatMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.proto.TcpMessage.ResTcpHeartBeatMessage();
    }

    public static com.proto.TcpMessage.ResTcpHeartBeatMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResTcpHeartBeatMessage>
        PARSER = new com.google.protobuf.AbstractParser<ResTcpHeartBeatMessage>() {
      @java.lang.Override
      public ResTcpHeartBeatMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResTcpHeartBeatMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResTcpHeartBeatMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResTcpHeartBeatMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.proto.TcpMessage.ResTcpHeartBeatMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqTcpQuitAgentGameMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ProtoMessage.ReqTcpQuitAgentGameMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    int getMsgID();
  }
  /**
   * <pre>
   *请求退出三方游戏
   * </pre>
   *
   * Protobuf type {@code ProtoMessage.ReqTcpQuitAgentGameMessage}
   */
  public static final class ReqTcpQuitAgentGameMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ProtoMessage.ReqTcpQuitAgentGameMessage)
      ReqTcpQuitAgentGameMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqTcpQuitAgentGameMessage.newBuilder() to construct.
    private ReqTcpQuitAgentGameMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqTcpQuitAgentGameMessage() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqTcpQuitAgentGameMessage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqTcpQuitAgentGameMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              msgID_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.proto.TcpMessage.internal_static_ProtoMessage_ReqTcpQuitAgentGameMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.proto.TcpMessage.internal_static_ProtoMessage_ReqTcpQuitAgentGameMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.proto.TcpMessage.ReqTcpQuitAgentGameMessage.class, com.proto.TcpMessage.ReqTcpQuitAgentGameMessage.Builder.class);
    }

    public static final int MSGID_FIELD_NUMBER = 1;
    private int msgID_;
    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    @java.lang.Override
    public int getMsgID() {
      return msgID_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (msgID_ != 0) {
        output.writeInt32(1, msgID_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (msgID_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, msgID_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.proto.TcpMessage.ReqTcpQuitAgentGameMessage)) {
        return super.equals(obj);
      }
      com.proto.TcpMessage.ReqTcpQuitAgentGameMessage other = (com.proto.TcpMessage.ReqTcpQuitAgentGameMessage) obj;

      if (getMsgID()
          != other.getMsgID()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + MSGID_FIELD_NUMBER;
      hash = (53 * hash) + getMsgID();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.proto.TcpMessage.ReqTcpQuitAgentGameMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ReqTcpQuitAgentGameMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ReqTcpQuitAgentGameMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ReqTcpQuitAgentGameMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ReqTcpQuitAgentGameMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ReqTcpQuitAgentGameMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ReqTcpQuitAgentGameMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ReqTcpQuitAgentGameMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.TcpMessage.ReqTcpQuitAgentGameMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ReqTcpQuitAgentGameMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.TcpMessage.ReqTcpQuitAgentGameMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ReqTcpQuitAgentGameMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.proto.TcpMessage.ReqTcpQuitAgentGameMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *请求退出三方游戏
     * </pre>
     *
     * Protobuf type {@code ProtoMessage.ReqTcpQuitAgentGameMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ProtoMessage.ReqTcpQuitAgentGameMessage)
        com.proto.TcpMessage.ReqTcpQuitAgentGameMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ReqTcpQuitAgentGameMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ReqTcpQuitAgentGameMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.proto.TcpMessage.ReqTcpQuitAgentGameMessage.class, com.proto.TcpMessage.ReqTcpQuitAgentGameMessage.Builder.class);
      }

      // Construct using com.proto.TcpMessage.ReqTcpQuitAgentGameMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        msgID_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ReqTcpQuitAgentGameMessage_descriptor;
      }

      @java.lang.Override
      public com.proto.TcpMessage.ReqTcpQuitAgentGameMessage getDefaultInstanceForType() {
        return com.proto.TcpMessage.ReqTcpQuitAgentGameMessage.getDefaultInstance();
      }

      @java.lang.Override
      public com.proto.TcpMessage.ReqTcpQuitAgentGameMessage build() {
        com.proto.TcpMessage.ReqTcpQuitAgentGameMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.proto.TcpMessage.ReqTcpQuitAgentGameMessage buildPartial() {
        com.proto.TcpMessage.ReqTcpQuitAgentGameMessage result = new com.proto.TcpMessage.ReqTcpQuitAgentGameMessage(this);
        result.msgID_ = msgID_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.proto.TcpMessage.ReqTcpQuitAgentGameMessage) {
          return mergeFrom((com.proto.TcpMessage.ReqTcpQuitAgentGameMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.proto.TcpMessage.ReqTcpQuitAgentGameMessage other) {
        if (other == com.proto.TcpMessage.ReqTcpQuitAgentGameMessage.getDefaultInstance()) return this;
        if (other.getMsgID() != 0) {
          setMsgID(other.getMsgID());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.proto.TcpMessage.ReqTcpQuitAgentGameMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.proto.TcpMessage.ReqTcpQuitAgentGameMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int msgID_ ;
      /**
       * <code>int32 msgID = 1;</code>
       * @return The msgID.
       */
      @java.lang.Override
      public int getMsgID() {
        return msgID_;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @param value The msgID to set.
       * @return This builder for chaining.
       */
      public Builder setMsgID(int value) {
        
        msgID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgID() {
        
        msgID_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ProtoMessage.ReqTcpQuitAgentGameMessage)
    }

    // @@protoc_insertion_point(class_scope:ProtoMessage.ReqTcpQuitAgentGameMessage)
    private static final com.proto.TcpMessage.ReqTcpQuitAgentGameMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.proto.TcpMessage.ReqTcpQuitAgentGameMessage();
    }

    public static com.proto.TcpMessage.ReqTcpQuitAgentGameMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqTcpQuitAgentGameMessage>
        PARSER = new com.google.protobuf.AbstractParser<ReqTcpQuitAgentGameMessage>() {
      @java.lang.Override
      public ReqTcpQuitAgentGameMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqTcpQuitAgentGameMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqTcpQuitAgentGameMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqTcpQuitAgentGameMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.proto.TcpMessage.ReqTcpQuitAgentGameMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResTcpQuitAgentGameMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ProtoMessage.ResTcpQuitAgentGameMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    int getMsgID();

    /**
     * <pre>
     *错误码
     * </pre>
     *
     * <code>int32 error = 2;</code>
     * @return The error.
     */
    int getError();
  }
  /**
   * <pre>
   *返回退出三方游戏
   * </pre>
   *
   * Protobuf type {@code ProtoMessage.ResTcpQuitAgentGameMessage}
   */
  public static final class ResTcpQuitAgentGameMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ProtoMessage.ResTcpQuitAgentGameMessage)
      ResTcpQuitAgentGameMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResTcpQuitAgentGameMessage.newBuilder() to construct.
    private ResTcpQuitAgentGameMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResTcpQuitAgentGameMessage() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResTcpQuitAgentGameMessage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResTcpQuitAgentGameMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              msgID_ = input.readInt32();
              break;
            }
            case 16: {

              error_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpQuitAgentGameMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpQuitAgentGameMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.proto.TcpMessage.ResTcpQuitAgentGameMessage.class, com.proto.TcpMessage.ResTcpQuitAgentGameMessage.Builder.class);
    }

    public static final int MSGID_FIELD_NUMBER = 1;
    private int msgID_;
    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    @java.lang.Override
    public int getMsgID() {
      return msgID_;
    }

    public static final int ERROR_FIELD_NUMBER = 2;
    private int error_;
    /**
     * <pre>
     *错误码
     * </pre>
     *
     * <code>int32 error = 2;</code>
     * @return The error.
     */
    @java.lang.Override
    public int getError() {
      return error_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (msgID_ != 0) {
        output.writeInt32(1, msgID_);
      }
      if (error_ != 0) {
        output.writeInt32(2, error_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (msgID_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, msgID_);
      }
      if (error_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, error_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.proto.TcpMessage.ResTcpQuitAgentGameMessage)) {
        return super.equals(obj);
      }
      com.proto.TcpMessage.ResTcpQuitAgentGameMessage other = (com.proto.TcpMessage.ResTcpQuitAgentGameMessage) obj;

      if (getMsgID()
          != other.getMsgID()) return false;
      if (getError()
          != other.getError()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + MSGID_FIELD_NUMBER;
      hash = (53 * hash) + getMsgID();
      hash = (37 * hash) + ERROR_FIELD_NUMBER;
      hash = (53 * hash) + getError();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.proto.TcpMessage.ResTcpQuitAgentGameMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ResTcpQuitAgentGameMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpQuitAgentGameMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ResTcpQuitAgentGameMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpQuitAgentGameMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ResTcpQuitAgentGameMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpQuitAgentGameMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ResTcpQuitAgentGameMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpQuitAgentGameMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ResTcpQuitAgentGameMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpQuitAgentGameMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ResTcpQuitAgentGameMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.proto.TcpMessage.ResTcpQuitAgentGameMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *返回退出三方游戏
     * </pre>
     *
     * Protobuf type {@code ProtoMessage.ResTcpQuitAgentGameMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ProtoMessage.ResTcpQuitAgentGameMessage)
        com.proto.TcpMessage.ResTcpQuitAgentGameMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpQuitAgentGameMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpQuitAgentGameMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.proto.TcpMessage.ResTcpQuitAgentGameMessage.class, com.proto.TcpMessage.ResTcpQuitAgentGameMessage.Builder.class);
      }

      // Construct using com.proto.TcpMessage.ResTcpQuitAgentGameMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        msgID_ = 0;

        error_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpQuitAgentGameMessage_descriptor;
      }

      @java.lang.Override
      public com.proto.TcpMessage.ResTcpQuitAgentGameMessage getDefaultInstanceForType() {
        return com.proto.TcpMessage.ResTcpQuitAgentGameMessage.getDefaultInstance();
      }

      @java.lang.Override
      public com.proto.TcpMessage.ResTcpQuitAgentGameMessage build() {
        com.proto.TcpMessage.ResTcpQuitAgentGameMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.proto.TcpMessage.ResTcpQuitAgentGameMessage buildPartial() {
        com.proto.TcpMessage.ResTcpQuitAgentGameMessage result = new com.proto.TcpMessage.ResTcpQuitAgentGameMessage(this);
        result.msgID_ = msgID_;
        result.error_ = error_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.proto.TcpMessage.ResTcpQuitAgentGameMessage) {
          return mergeFrom((com.proto.TcpMessage.ResTcpQuitAgentGameMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.proto.TcpMessage.ResTcpQuitAgentGameMessage other) {
        if (other == com.proto.TcpMessage.ResTcpQuitAgentGameMessage.getDefaultInstance()) return this;
        if (other.getMsgID() != 0) {
          setMsgID(other.getMsgID());
        }
        if (other.getError() != 0) {
          setError(other.getError());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.proto.TcpMessage.ResTcpQuitAgentGameMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.proto.TcpMessage.ResTcpQuitAgentGameMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int msgID_ ;
      /**
       * <code>int32 msgID = 1;</code>
       * @return The msgID.
       */
      @java.lang.Override
      public int getMsgID() {
        return msgID_;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @param value The msgID to set.
       * @return This builder for chaining.
       */
      public Builder setMsgID(int value) {
        
        msgID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgID() {
        
        msgID_ = 0;
        onChanged();
        return this;
      }

      private int error_ ;
      /**
       * <pre>
       *错误码
       * </pre>
       *
       * <code>int32 error = 2;</code>
       * @return The error.
       */
      @java.lang.Override
      public int getError() {
        return error_;
      }
      /**
       * <pre>
       *错误码
       * </pre>
       *
       * <code>int32 error = 2;</code>
       * @param value The error to set.
       * @return This builder for chaining.
       */
      public Builder setError(int value) {
        
        error_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *错误码
       * </pre>
       *
       * <code>int32 error = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearError() {
        
        error_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ProtoMessage.ResTcpQuitAgentGameMessage)
    }

    // @@protoc_insertion_point(class_scope:ProtoMessage.ResTcpQuitAgentGameMessage)
    private static final com.proto.TcpMessage.ResTcpQuitAgentGameMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.proto.TcpMessage.ResTcpQuitAgentGameMessage();
    }

    public static com.proto.TcpMessage.ResTcpQuitAgentGameMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResTcpQuitAgentGameMessage>
        PARSER = new com.google.protobuf.AbstractParser<ResTcpQuitAgentGameMessage>() {
      @java.lang.Override
      public ResTcpQuitAgentGameMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResTcpQuitAgentGameMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResTcpQuitAgentGameMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResTcpQuitAgentGameMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.proto.TcpMessage.ResTcpQuitAgentGameMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResTcpSysErrorMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ProtoMessage.ResTcpSysErrorMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    int getMsgID();

    /**
     * <pre>
     * 错误码
     * </pre>
     *
     * <code>int32 error = 2;</code>
     * @return The error.
     */
    int getError();

    /**
     * <pre>
     * 错误文本
     * </pre>
     *
     * <code>string context = 3;</code>
     * @return The context.
     */
    java.lang.String getContext();
    /**
     * <pre>
     * 错误文本
     * </pre>
     *
     * <code>string context = 3;</code>
     * @return The bytes for context.
     */
    com.google.protobuf.ByteString
        getContextBytes();
  }
  /**
   * <pre>
   *返回系统错误消息
   * </pre>
   *
   * Protobuf type {@code ProtoMessage.ResTcpSysErrorMessage}
   */
  public static final class ResTcpSysErrorMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ProtoMessage.ResTcpSysErrorMessage)
      ResTcpSysErrorMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResTcpSysErrorMessage.newBuilder() to construct.
    private ResTcpSysErrorMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResTcpSysErrorMessage() {
      context_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResTcpSysErrorMessage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResTcpSysErrorMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              msgID_ = input.readInt32();
              break;
            }
            case 16: {

              error_ = input.readInt32();
              break;
            }
            case 26: {
              java.lang.String s = input.readStringRequireUtf8();

              context_ = s;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpSysErrorMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpSysErrorMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.proto.TcpMessage.ResTcpSysErrorMessage.class, com.proto.TcpMessage.ResTcpSysErrorMessage.Builder.class);
    }

    public static final int MSGID_FIELD_NUMBER = 1;
    private int msgID_;
    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    @java.lang.Override
    public int getMsgID() {
      return msgID_;
    }

    public static final int ERROR_FIELD_NUMBER = 2;
    private int error_;
    /**
     * <pre>
     * 错误码
     * </pre>
     *
     * <code>int32 error = 2;</code>
     * @return The error.
     */
    @java.lang.Override
    public int getError() {
      return error_;
    }

    public static final int CONTEXT_FIELD_NUMBER = 3;
    private volatile java.lang.Object context_;
    /**
     * <pre>
     * 错误文本
     * </pre>
     *
     * <code>string context = 3;</code>
     * @return The context.
     */
    @java.lang.Override
    public java.lang.String getContext() {
      java.lang.Object ref = context_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        context_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 错误文本
     * </pre>
     *
     * <code>string context = 3;</code>
     * @return The bytes for context.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getContextBytes() {
      java.lang.Object ref = context_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        context_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (msgID_ != 0) {
        output.writeInt32(1, msgID_);
      }
      if (error_ != 0) {
        output.writeInt32(2, error_);
      }
      if (!getContextBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, context_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (msgID_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, msgID_);
      }
      if (error_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, error_);
      }
      if (!getContextBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, context_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.proto.TcpMessage.ResTcpSysErrorMessage)) {
        return super.equals(obj);
      }
      com.proto.TcpMessage.ResTcpSysErrorMessage other = (com.proto.TcpMessage.ResTcpSysErrorMessage) obj;

      if (getMsgID()
          != other.getMsgID()) return false;
      if (getError()
          != other.getError()) return false;
      if (!getContext()
          .equals(other.getContext())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + MSGID_FIELD_NUMBER;
      hash = (53 * hash) + getMsgID();
      hash = (37 * hash) + ERROR_FIELD_NUMBER;
      hash = (53 * hash) + getError();
      hash = (37 * hash) + CONTEXT_FIELD_NUMBER;
      hash = (53 * hash) + getContext().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.proto.TcpMessage.ResTcpSysErrorMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ResTcpSysErrorMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpSysErrorMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ResTcpSysErrorMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpSysErrorMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ResTcpSysErrorMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpSysErrorMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ResTcpSysErrorMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpSysErrorMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ResTcpSysErrorMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpSysErrorMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ResTcpSysErrorMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.proto.TcpMessage.ResTcpSysErrorMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *返回系统错误消息
     * </pre>
     *
     * Protobuf type {@code ProtoMessage.ResTcpSysErrorMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ProtoMessage.ResTcpSysErrorMessage)
        com.proto.TcpMessage.ResTcpSysErrorMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpSysErrorMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpSysErrorMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.proto.TcpMessage.ResTcpSysErrorMessage.class, com.proto.TcpMessage.ResTcpSysErrorMessage.Builder.class);
      }

      // Construct using com.proto.TcpMessage.ResTcpSysErrorMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        msgID_ = 0;

        error_ = 0;

        context_ = "";

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpSysErrorMessage_descriptor;
      }

      @java.lang.Override
      public com.proto.TcpMessage.ResTcpSysErrorMessage getDefaultInstanceForType() {
        return com.proto.TcpMessage.ResTcpSysErrorMessage.getDefaultInstance();
      }

      @java.lang.Override
      public com.proto.TcpMessage.ResTcpSysErrorMessage build() {
        com.proto.TcpMessage.ResTcpSysErrorMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.proto.TcpMessage.ResTcpSysErrorMessage buildPartial() {
        com.proto.TcpMessage.ResTcpSysErrorMessage result = new com.proto.TcpMessage.ResTcpSysErrorMessage(this);
        result.msgID_ = msgID_;
        result.error_ = error_;
        result.context_ = context_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.proto.TcpMessage.ResTcpSysErrorMessage) {
          return mergeFrom((com.proto.TcpMessage.ResTcpSysErrorMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.proto.TcpMessage.ResTcpSysErrorMessage other) {
        if (other == com.proto.TcpMessage.ResTcpSysErrorMessage.getDefaultInstance()) return this;
        if (other.getMsgID() != 0) {
          setMsgID(other.getMsgID());
        }
        if (other.getError() != 0) {
          setError(other.getError());
        }
        if (!other.getContext().isEmpty()) {
          context_ = other.context_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.proto.TcpMessage.ResTcpSysErrorMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.proto.TcpMessage.ResTcpSysErrorMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int msgID_ ;
      /**
       * <code>int32 msgID = 1;</code>
       * @return The msgID.
       */
      @java.lang.Override
      public int getMsgID() {
        return msgID_;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @param value The msgID to set.
       * @return This builder for chaining.
       */
      public Builder setMsgID(int value) {
        
        msgID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgID() {
        
        msgID_ = 0;
        onChanged();
        return this;
      }

      private int error_ ;
      /**
       * <pre>
       * 错误码
       * </pre>
       *
       * <code>int32 error = 2;</code>
       * @return The error.
       */
      @java.lang.Override
      public int getError() {
        return error_;
      }
      /**
       * <pre>
       * 错误码
       * </pre>
       *
       * <code>int32 error = 2;</code>
       * @param value The error to set.
       * @return This builder for chaining.
       */
      public Builder setError(int value) {
        
        error_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 错误码
       * </pre>
       *
       * <code>int32 error = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearError() {
        
        error_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object context_ = "";
      /**
       * <pre>
       * 错误文本
       * </pre>
       *
       * <code>string context = 3;</code>
       * @return The context.
       */
      public java.lang.String getContext() {
        java.lang.Object ref = context_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          context_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 错误文本
       * </pre>
       *
       * <code>string context = 3;</code>
       * @return The bytes for context.
       */
      public com.google.protobuf.ByteString
          getContextBytes() {
        java.lang.Object ref = context_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          context_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 错误文本
       * </pre>
       *
       * <code>string context = 3;</code>
       * @param value The context to set.
       * @return This builder for chaining.
       */
      public Builder setContext(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        context_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 错误文本
       * </pre>
       *
       * <code>string context = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearContext() {
        
        context_ = getDefaultInstance().getContext();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 错误文本
       * </pre>
       *
       * <code>string context = 3;</code>
       * @param value The bytes for context to set.
       * @return This builder for chaining.
       */
      public Builder setContextBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        context_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ProtoMessage.ResTcpSysErrorMessage)
    }

    // @@protoc_insertion_point(class_scope:ProtoMessage.ResTcpSysErrorMessage)
    private static final com.proto.TcpMessage.ResTcpSysErrorMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.proto.TcpMessage.ResTcpSysErrorMessage();
    }

    public static com.proto.TcpMessage.ResTcpSysErrorMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResTcpSysErrorMessage>
        PARSER = new com.google.protobuf.AbstractParser<ResTcpSysErrorMessage>() {
      @java.lang.Override
      public ResTcpSysErrorMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResTcpSysErrorMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResTcpSysErrorMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResTcpSysErrorMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.proto.TcpMessage.ResTcpSysErrorMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResTcpBulletinDataMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ProtoMessage.ResTcpBulletinDataMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    int getMsgID();

    /**
     * <pre>
     * 错误码
     * </pre>
     *
     * <code>int32 error = 2;</code>
     * @return The error.
     */
    int getError();

    /**
     * <pre>
     * （服务器使用）
     * </pre>
     *
     * <code>string business_no = 3;</code>
     * @return The businessNo.
     */
    java.lang.String getBusinessNo();
    /**
     * <pre>
     * （服务器使用）
     * </pre>
     *
     * <code>string business_no = 3;</code>
     * @return The bytes for businessNo.
     */
    com.google.protobuf.ByteString
        getBusinessNoBytes();

    /**
     * <pre>
     * 公告
     * </pre>
     *
     * <code>.ProtoMessage.MaintainNoticeInfo maintainNoticeInfo = 4;</code>
     * @return Whether the maintainNoticeInfo field is set.
     */
    boolean hasMaintainNoticeInfo();
    /**
     * <pre>
     * 公告
     * </pre>
     *
     * <code>.ProtoMessage.MaintainNoticeInfo maintainNoticeInfo = 4;</code>
     * @return The maintainNoticeInfo.
     */
    com.proto.BackStageMessage.MaintainNoticeInfo getMaintainNoticeInfo();
    /**
     * <pre>
     * 公告
     * </pre>
     *
     * <code>.ProtoMessage.MaintainNoticeInfo maintainNoticeInfo = 4;</code>
     */
    com.proto.BackStageMessage.MaintainNoticeInfoOrBuilder getMaintainNoticeInfoOrBuilder();
  }
  /**
   * <pre>
   *返回公告信息(广播消息)
   * </pre>
   *
   * Protobuf type {@code ProtoMessage.ResTcpBulletinDataMessage}
   */
  public static final class ResTcpBulletinDataMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ProtoMessage.ResTcpBulletinDataMessage)
      ResTcpBulletinDataMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResTcpBulletinDataMessage.newBuilder() to construct.
    private ResTcpBulletinDataMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResTcpBulletinDataMessage() {
      businessNo_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResTcpBulletinDataMessage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResTcpBulletinDataMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              msgID_ = input.readInt32();
              break;
            }
            case 16: {

              error_ = input.readInt32();
              break;
            }
            case 26: {
              java.lang.String s = input.readStringRequireUtf8();

              businessNo_ = s;
              break;
            }
            case 34: {
              com.proto.BackStageMessage.MaintainNoticeInfo.Builder subBuilder = null;
              if (maintainNoticeInfo_ != null) {
                subBuilder = maintainNoticeInfo_.toBuilder();
              }
              maintainNoticeInfo_ = input.readMessage(com.proto.BackStageMessage.MaintainNoticeInfo.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(maintainNoticeInfo_);
                maintainNoticeInfo_ = subBuilder.buildPartial();
              }

              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpBulletinDataMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpBulletinDataMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.proto.TcpMessage.ResTcpBulletinDataMessage.class, com.proto.TcpMessage.ResTcpBulletinDataMessage.Builder.class);
    }

    public static final int MSGID_FIELD_NUMBER = 1;
    private int msgID_;
    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    @java.lang.Override
    public int getMsgID() {
      return msgID_;
    }

    public static final int ERROR_FIELD_NUMBER = 2;
    private int error_;
    /**
     * <pre>
     * 错误码
     * </pre>
     *
     * <code>int32 error = 2;</code>
     * @return The error.
     */
    @java.lang.Override
    public int getError() {
      return error_;
    }

    public static final int BUSINESS_NO_FIELD_NUMBER = 3;
    private volatile java.lang.Object businessNo_;
    /**
     * <pre>
     * （服务器使用）
     * </pre>
     *
     * <code>string business_no = 3;</code>
     * @return The businessNo.
     */
    @java.lang.Override
    public java.lang.String getBusinessNo() {
      java.lang.Object ref = businessNo_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        businessNo_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * （服务器使用）
     * </pre>
     *
     * <code>string business_no = 3;</code>
     * @return The bytes for businessNo.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getBusinessNoBytes() {
      java.lang.Object ref = businessNo_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        businessNo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int MAINTAINNOTICEINFO_FIELD_NUMBER = 4;
    private com.proto.BackStageMessage.MaintainNoticeInfo maintainNoticeInfo_;
    /**
     * <pre>
     * 公告
     * </pre>
     *
     * <code>.ProtoMessage.MaintainNoticeInfo maintainNoticeInfo = 4;</code>
     * @return Whether the maintainNoticeInfo field is set.
     */
    @java.lang.Override
    public boolean hasMaintainNoticeInfo() {
      return maintainNoticeInfo_ != null;
    }
    /**
     * <pre>
     * 公告
     * </pre>
     *
     * <code>.ProtoMessage.MaintainNoticeInfo maintainNoticeInfo = 4;</code>
     * @return The maintainNoticeInfo.
     */
    @java.lang.Override
    public com.proto.BackStageMessage.MaintainNoticeInfo getMaintainNoticeInfo() {
      return maintainNoticeInfo_ == null ? com.proto.BackStageMessage.MaintainNoticeInfo.getDefaultInstance() : maintainNoticeInfo_;
    }
    /**
     * <pre>
     * 公告
     * </pre>
     *
     * <code>.ProtoMessage.MaintainNoticeInfo maintainNoticeInfo = 4;</code>
     */
    @java.lang.Override
    public com.proto.BackStageMessage.MaintainNoticeInfoOrBuilder getMaintainNoticeInfoOrBuilder() {
      return getMaintainNoticeInfo();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (msgID_ != 0) {
        output.writeInt32(1, msgID_);
      }
      if (error_ != 0) {
        output.writeInt32(2, error_);
      }
      if (!getBusinessNoBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, businessNo_);
      }
      if (maintainNoticeInfo_ != null) {
        output.writeMessage(4, getMaintainNoticeInfo());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (msgID_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, msgID_);
      }
      if (error_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, error_);
      }
      if (!getBusinessNoBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, businessNo_);
      }
      if (maintainNoticeInfo_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, getMaintainNoticeInfo());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.proto.TcpMessage.ResTcpBulletinDataMessage)) {
        return super.equals(obj);
      }
      com.proto.TcpMessage.ResTcpBulletinDataMessage other = (com.proto.TcpMessage.ResTcpBulletinDataMessage) obj;

      if (getMsgID()
          != other.getMsgID()) return false;
      if (getError()
          != other.getError()) return false;
      if (!getBusinessNo()
          .equals(other.getBusinessNo())) return false;
      if (hasMaintainNoticeInfo() != other.hasMaintainNoticeInfo()) return false;
      if (hasMaintainNoticeInfo()) {
        if (!getMaintainNoticeInfo()
            .equals(other.getMaintainNoticeInfo())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + MSGID_FIELD_NUMBER;
      hash = (53 * hash) + getMsgID();
      hash = (37 * hash) + ERROR_FIELD_NUMBER;
      hash = (53 * hash) + getError();
      hash = (37 * hash) + BUSINESS_NO_FIELD_NUMBER;
      hash = (53 * hash) + getBusinessNo().hashCode();
      if (hasMaintainNoticeInfo()) {
        hash = (37 * hash) + MAINTAINNOTICEINFO_FIELD_NUMBER;
        hash = (53 * hash) + getMaintainNoticeInfo().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.proto.TcpMessage.ResTcpBulletinDataMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ResTcpBulletinDataMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpBulletinDataMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ResTcpBulletinDataMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpBulletinDataMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ResTcpBulletinDataMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpBulletinDataMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ResTcpBulletinDataMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpBulletinDataMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ResTcpBulletinDataMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpBulletinDataMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ResTcpBulletinDataMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.proto.TcpMessage.ResTcpBulletinDataMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *返回公告信息(广播消息)
     * </pre>
     *
     * Protobuf type {@code ProtoMessage.ResTcpBulletinDataMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ProtoMessage.ResTcpBulletinDataMessage)
        com.proto.TcpMessage.ResTcpBulletinDataMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpBulletinDataMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpBulletinDataMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.proto.TcpMessage.ResTcpBulletinDataMessage.class, com.proto.TcpMessage.ResTcpBulletinDataMessage.Builder.class);
      }

      // Construct using com.proto.TcpMessage.ResTcpBulletinDataMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        msgID_ = 0;

        error_ = 0;

        businessNo_ = "";

        if (maintainNoticeInfoBuilder_ == null) {
          maintainNoticeInfo_ = null;
        } else {
          maintainNoticeInfo_ = null;
          maintainNoticeInfoBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpBulletinDataMessage_descriptor;
      }

      @java.lang.Override
      public com.proto.TcpMessage.ResTcpBulletinDataMessage getDefaultInstanceForType() {
        return com.proto.TcpMessage.ResTcpBulletinDataMessage.getDefaultInstance();
      }

      @java.lang.Override
      public com.proto.TcpMessage.ResTcpBulletinDataMessage build() {
        com.proto.TcpMessage.ResTcpBulletinDataMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.proto.TcpMessage.ResTcpBulletinDataMessage buildPartial() {
        com.proto.TcpMessage.ResTcpBulletinDataMessage result = new com.proto.TcpMessage.ResTcpBulletinDataMessage(this);
        result.msgID_ = msgID_;
        result.error_ = error_;
        result.businessNo_ = businessNo_;
        if (maintainNoticeInfoBuilder_ == null) {
          result.maintainNoticeInfo_ = maintainNoticeInfo_;
        } else {
          result.maintainNoticeInfo_ = maintainNoticeInfoBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.proto.TcpMessage.ResTcpBulletinDataMessage) {
          return mergeFrom((com.proto.TcpMessage.ResTcpBulletinDataMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.proto.TcpMessage.ResTcpBulletinDataMessage other) {
        if (other == com.proto.TcpMessage.ResTcpBulletinDataMessage.getDefaultInstance()) return this;
        if (other.getMsgID() != 0) {
          setMsgID(other.getMsgID());
        }
        if (other.getError() != 0) {
          setError(other.getError());
        }
        if (!other.getBusinessNo().isEmpty()) {
          businessNo_ = other.businessNo_;
          onChanged();
        }
        if (other.hasMaintainNoticeInfo()) {
          mergeMaintainNoticeInfo(other.getMaintainNoticeInfo());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.proto.TcpMessage.ResTcpBulletinDataMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.proto.TcpMessage.ResTcpBulletinDataMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int msgID_ ;
      /**
       * <code>int32 msgID = 1;</code>
       * @return The msgID.
       */
      @java.lang.Override
      public int getMsgID() {
        return msgID_;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @param value The msgID to set.
       * @return This builder for chaining.
       */
      public Builder setMsgID(int value) {
        
        msgID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgID() {
        
        msgID_ = 0;
        onChanged();
        return this;
      }

      private int error_ ;
      /**
       * <pre>
       * 错误码
       * </pre>
       *
       * <code>int32 error = 2;</code>
       * @return The error.
       */
      @java.lang.Override
      public int getError() {
        return error_;
      }
      /**
       * <pre>
       * 错误码
       * </pre>
       *
       * <code>int32 error = 2;</code>
       * @param value The error to set.
       * @return This builder for chaining.
       */
      public Builder setError(int value) {
        
        error_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 错误码
       * </pre>
       *
       * <code>int32 error = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearError() {
        
        error_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object businessNo_ = "";
      /**
       * <pre>
       * （服务器使用）
       * </pre>
       *
       * <code>string business_no = 3;</code>
       * @return The businessNo.
       */
      public java.lang.String getBusinessNo() {
        java.lang.Object ref = businessNo_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          businessNo_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * （服务器使用）
       * </pre>
       *
       * <code>string business_no = 3;</code>
       * @return The bytes for businessNo.
       */
      public com.google.protobuf.ByteString
          getBusinessNoBytes() {
        java.lang.Object ref = businessNo_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          businessNo_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * （服务器使用）
       * </pre>
       *
       * <code>string business_no = 3;</code>
       * @param value The businessNo to set.
       * @return This builder for chaining.
       */
      public Builder setBusinessNo(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        businessNo_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * （服务器使用）
       * </pre>
       *
       * <code>string business_no = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearBusinessNo() {
        
        businessNo_ = getDefaultInstance().getBusinessNo();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * （服务器使用）
       * </pre>
       *
       * <code>string business_no = 3;</code>
       * @param value The bytes for businessNo to set.
       * @return This builder for chaining.
       */
      public Builder setBusinessNoBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        businessNo_ = value;
        onChanged();
        return this;
      }

      private com.proto.BackStageMessage.MaintainNoticeInfo maintainNoticeInfo_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.proto.BackStageMessage.MaintainNoticeInfo, com.proto.BackStageMessage.MaintainNoticeInfo.Builder, com.proto.BackStageMessage.MaintainNoticeInfoOrBuilder> maintainNoticeInfoBuilder_;
      /**
       * <pre>
       * 公告
       * </pre>
       *
       * <code>.ProtoMessage.MaintainNoticeInfo maintainNoticeInfo = 4;</code>
       * @return Whether the maintainNoticeInfo field is set.
       */
      public boolean hasMaintainNoticeInfo() {
        return maintainNoticeInfoBuilder_ != null || maintainNoticeInfo_ != null;
      }
      /**
       * <pre>
       * 公告
       * </pre>
       *
       * <code>.ProtoMessage.MaintainNoticeInfo maintainNoticeInfo = 4;</code>
       * @return The maintainNoticeInfo.
       */
      public com.proto.BackStageMessage.MaintainNoticeInfo getMaintainNoticeInfo() {
        if (maintainNoticeInfoBuilder_ == null) {
          return maintainNoticeInfo_ == null ? com.proto.BackStageMessage.MaintainNoticeInfo.getDefaultInstance() : maintainNoticeInfo_;
        } else {
          return maintainNoticeInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 公告
       * </pre>
       *
       * <code>.ProtoMessage.MaintainNoticeInfo maintainNoticeInfo = 4;</code>
       */
      public Builder setMaintainNoticeInfo(com.proto.BackStageMessage.MaintainNoticeInfo value) {
        if (maintainNoticeInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          maintainNoticeInfo_ = value;
          onChanged();
        } else {
          maintainNoticeInfoBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       * 公告
       * </pre>
       *
       * <code>.ProtoMessage.MaintainNoticeInfo maintainNoticeInfo = 4;</code>
       */
      public Builder setMaintainNoticeInfo(
          com.proto.BackStageMessage.MaintainNoticeInfo.Builder builderForValue) {
        if (maintainNoticeInfoBuilder_ == null) {
          maintainNoticeInfo_ = builderForValue.build();
          onChanged();
        } else {
          maintainNoticeInfoBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       * 公告
       * </pre>
       *
       * <code>.ProtoMessage.MaintainNoticeInfo maintainNoticeInfo = 4;</code>
       */
      public Builder mergeMaintainNoticeInfo(com.proto.BackStageMessage.MaintainNoticeInfo value) {
        if (maintainNoticeInfoBuilder_ == null) {
          if (maintainNoticeInfo_ != null) {
            maintainNoticeInfo_ =
              com.proto.BackStageMessage.MaintainNoticeInfo.newBuilder(maintainNoticeInfo_).mergeFrom(value).buildPartial();
          } else {
            maintainNoticeInfo_ = value;
          }
          onChanged();
        } else {
          maintainNoticeInfoBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       * 公告
       * </pre>
       *
       * <code>.ProtoMessage.MaintainNoticeInfo maintainNoticeInfo = 4;</code>
       */
      public Builder clearMaintainNoticeInfo() {
        if (maintainNoticeInfoBuilder_ == null) {
          maintainNoticeInfo_ = null;
          onChanged();
        } else {
          maintainNoticeInfo_ = null;
          maintainNoticeInfoBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       * 公告
       * </pre>
       *
       * <code>.ProtoMessage.MaintainNoticeInfo maintainNoticeInfo = 4;</code>
       */
      public com.proto.BackStageMessage.MaintainNoticeInfo.Builder getMaintainNoticeInfoBuilder() {
        
        onChanged();
        return getMaintainNoticeInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 公告
       * </pre>
       *
       * <code>.ProtoMessage.MaintainNoticeInfo maintainNoticeInfo = 4;</code>
       */
      public com.proto.BackStageMessage.MaintainNoticeInfoOrBuilder getMaintainNoticeInfoOrBuilder() {
        if (maintainNoticeInfoBuilder_ != null) {
          return maintainNoticeInfoBuilder_.getMessageOrBuilder();
        } else {
          return maintainNoticeInfo_ == null ?
              com.proto.BackStageMessage.MaintainNoticeInfo.getDefaultInstance() : maintainNoticeInfo_;
        }
      }
      /**
       * <pre>
       * 公告
       * </pre>
       *
       * <code>.ProtoMessage.MaintainNoticeInfo maintainNoticeInfo = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.proto.BackStageMessage.MaintainNoticeInfo, com.proto.BackStageMessage.MaintainNoticeInfo.Builder, com.proto.BackStageMessage.MaintainNoticeInfoOrBuilder> 
          getMaintainNoticeInfoFieldBuilder() {
        if (maintainNoticeInfoBuilder_ == null) {
          maintainNoticeInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.proto.BackStageMessage.MaintainNoticeInfo, com.proto.BackStageMessage.MaintainNoticeInfo.Builder, com.proto.BackStageMessage.MaintainNoticeInfoOrBuilder>(
                  getMaintainNoticeInfo(),
                  getParentForChildren(),
                  isClean());
          maintainNoticeInfo_ = null;
        }
        return maintainNoticeInfoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ProtoMessage.ResTcpBulletinDataMessage)
    }

    // @@protoc_insertion_point(class_scope:ProtoMessage.ResTcpBulletinDataMessage)
    private static final com.proto.TcpMessage.ResTcpBulletinDataMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.proto.TcpMessage.ResTcpBulletinDataMessage();
    }

    public static com.proto.TcpMessage.ResTcpBulletinDataMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResTcpBulletinDataMessage>
        PARSER = new com.google.protobuf.AbstractParser<ResTcpBulletinDataMessage>() {
      @java.lang.Override
      public ResTcpBulletinDataMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResTcpBulletinDataMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResTcpBulletinDataMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResTcpBulletinDataMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.proto.TcpMessage.ResTcpBulletinDataMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResTcpCurrencyUpdateMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ProtoMessage.ResTcpCurrencyUpdateMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    int getMsgID();

    /**
     * <pre>
     *货币列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.DCurrencyItem cItem = 2;</code>
     */
    java.util.List<com.proto.CommonMessage.DCurrencyItem> 
        getCItemList();
    /**
     * <pre>
     *货币列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.DCurrencyItem cItem = 2;</code>
     */
    com.proto.CommonMessage.DCurrencyItem getCItem(int index);
    /**
     * <pre>
     *货币列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.DCurrencyItem cItem = 2;</code>
     */
    int getCItemCount();
    /**
     * <pre>
     *货币列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.DCurrencyItem cItem = 2;</code>
     */
    java.util.List<? extends com.proto.CommonMessage.DCurrencyItemOrBuilder> 
        getCItemOrBuilderList();
    /**
     * <pre>
     *货币列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.DCurrencyItem cItem = 2;</code>
     */
    com.proto.CommonMessage.DCurrencyItemOrBuilder getCItemOrBuilder(
        int index);

    /**
     * <pre>
     *原因 1.奖励 2.消耗
     * </pre>
     *
     * <code>int32 reason = 3;</code>
     * @return The reason.
     */
    int getReason();

    /**
     * <pre>
     *来源
     * </pre>
     *
     * <code>int32 source = 4;</code>
     * @return The source.
     */
    int getSource();
  }
  /**
   * <pre>
   *返回货币更新
   * </pre>
   *
   * Protobuf type {@code ProtoMessage.ResTcpCurrencyUpdateMessage}
   */
  public static final class ResTcpCurrencyUpdateMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ProtoMessage.ResTcpCurrencyUpdateMessage)
      ResTcpCurrencyUpdateMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResTcpCurrencyUpdateMessage.newBuilder() to construct.
    private ResTcpCurrencyUpdateMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResTcpCurrencyUpdateMessage() {
      cItem_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResTcpCurrencyUpdateMessage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResTcpCurrencyUpdateMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              msgID_ = input.readInt32();
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                cItem_ = new java.util.ArrayList<com.proto.CommonMessage.DCurrencyItem>();
                mutable_bitField0_ |= 0x00000001;
              }
              cItem_.add(
                  input.readMessage(com.proto.CommonMessage.DCurrencyItem.parser(), extensionRegistry));
              break;
            }
            case 24: {

              reason_ = input.readInt32();
              break;
            }
            case 32: {

              source_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          cItem_ = java.util.Collections.unmodifiableList(cItem_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpCurrencyUpdateMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpCurrencyUpdateMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.proto.TcpMessage.ResTcpCurrencyUpdateMessage.class, com.proto.TcpMessage.ResTcpCurrencyUpdateMessage.Builder.class);
    }

    public static final int MSGID_FIELD_NUMBER = 1;
    private int msgID_;
    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    @java.lang.Override
    public int getMsgID() {
      return msgID_;
    }

    public static final int CITEM_FIELD_NUMBER = 2;
    private java.util.List<com.proto.CommonMessage.DCurrencyItem> cItem_;
    /**
     * <pre>
     *货币列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.DCurrencyItem cItem = 2;</code>
     */
    @java.lang.Override
    public java.util.List<com.proto.CommonMessage.DCurrencyItem> getCItemList() {
      return cItem_;
    }
    /**
     * <pre>
     *货币列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.DCurrencyItem cItem = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.proto.CommonMessage.DCurrencyItemOrBuilder> 
        getCItemOrBuilderList() {
      return cItem_;
    }
    /**
     * <pre>
     *货币列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.DCurrencyItem cItem = 2;</code>
     */
    @java.lang.Override
    public int getCItemCount() {
      return cItem_.size();
    }
    /**
     * <pre>
     *货币列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.DCurrencyItem cItem = 2;</code>
     */
    @java.lang.Override
    public com.proto.CommonMessage.DCurrencyItem getCItem(int index) {
      return cItem_.get(index);
    }
    /**
     * <pre>
     *货币列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.DCurrencyItem cItem = 2;</code>
     */
    @java.lang.Override
    public com.proto.CommonMessage.DCurrencyItemOrBuilder getCItemOrBuilder(
        int index) {
      return cItem_.get(index);
    }

    public static final int REASON_FIELD_NUMBER = 3;
    private int reason_;
    /**
     * <pre>
     *原因 1.奖励 2.消耗
     * </pre>
     *
     * <code>int32 reason = 3;</code>
     * @return The reason.
     */
    @java.lang.Override
    public int getReason() {
      return reason_;
    }

    public static final int SOURCE_FIELD_NUMBER = 4;
    private int source_;
    /**
     * <pre>
     *来源
     * </pre>
     *
     * <code>int32 source = 4;</code>
     * @return The source.
     */
    @java.lang.Override
    public int getSource() {
      return source_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (msgID_ != 0) {
        output.writeInt32(1, msgID_);
      }
      for (int i = 0; i < cItem_.size(); i++) {
        output.writeMessage(2, cItem_.get(i));
      }
      if (reason_ != 0) {
        output.writeInt32(3, reason_);
      }
      if (source_ != 0) {
        output.writeInt32(4, source_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (msgID_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, msgID_);
      }
      for (int i = 0; i < cItem_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, cItem_.get(i));
      }
      if (reason_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, reason_);
      }
      if (source_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, source_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.proto.TcpMessage.ResTcpCurrencyUpdateMessage)) {
        return super.equals(obj);
      }
      com.proto.TcpMessage.ResTcpCurrencyUpdateMessage other = (com.proto.TcpMessage.ResTcpCurrencyUpdateMessage) obj;

      if (getMsgID()
          != other.getMsgID()) return false;
      if (!getCItemList()
          .equals(other.getCItemList())) return false;
      if (getReason()
          != other.getReason()) return false;
      if (getSource()
          != other.getSource()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + MSGID_FIELD_NUMBER;
      hash = (53 * hash) + getMsgID();
      if (getCItemCount() > 0) {
        hash = (37 * hash) + CITEM_FIELD_NUMBER;
        hash = (53 * hash) + getCItemList().hashCode();
      }
      hash = (37 * hash) + REASON_FIELD_NUMBER;
      hash = (53 * hash) + getReason();
      hash = (37 * hash) + SOURCE_FIELD_NUMBER;
      hash = (53 * hash) + getSource();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.proto.TcpMessage.ResTcpCurrencyUpdateMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ResTcpCurrencyUpdateMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpCurrencyUpdateMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ResTcpCurrencyUpdateMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpCurrencyUpdateMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ResTcpCurrencyUpdateMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpCurrencyUpdateMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ResTcpCurrencyUpdateMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpCurrencyUpdateMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ResTcpCurrencyUpdateMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpCurrencyUpdateMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ResTcpCurrencyUpdateMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.proto.TcpMessage.ResTcpCurrencyUpdateMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *返回货币更新
     * </pre>
     *
     * Protobuf type {@code ProtoMessage.ResTcpCurrencyUpdateMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ProtoMessage.ResTcpCurrencyUpdateMessage)
        com.proto.TcpMessage.ResTcpCurrencyUpdateMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpCurrencyUpdateMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpCurrencyUpdateMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.proto.TcpMessage.ResTcpCurrencyUpdateMessage.class, com.proto.TcpMessage.ResTcpCurrencyUpdateMessage.Builder.class);
      }

      // Construct using com.proto.TcpMessage.ResTcpCurrencyUpdateMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getCItemFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        msgID_ = 0;

        if (cItemBuilder_ == null) {
          cItem_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          cItemBuilder_.clear();
        }
        reason_ = 0;

        source_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpCurrencyUpdateMessage_descriptor;
      }

      @java.lang.Override
      public com.proto.TcpMessage.ResTcpCurrencyUpdateMessage getDefaultInstanceForType() {
        return com.proto.TcpMessage.ResTcpCurrencyUpdateMessage.getDefaultInstance();
      }

      @java.lang.Override
      public com.proto.TcpMessage.ResTcpCurrencyUpdateMessage build() {
        com.proto.TcpMessage.ResTcpCurrencyUpdateMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.proto.TcpMessage.ResTcpCurrencyUpdateMessage buildPartial() {
        com.proto.TcpMessage.ResTcpCurrencyUpdateMessage result = new com.proto.TcpMessage.ResTcpCurrencyUpdateMessage(this);
        int from_bitField0_ = bitField0_;
        result.msgID_ = msgID_;
        if (cItemBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            cItem_ = java.util.Collections.unmodifiableList(cItem_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.cItem_ = cItem_;
        } else {
          result.cItem_ = cItemBuilder_.build();
        }
        result.reason_ = reason_;
        result.source_ = source_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.proto.TcpMessage.ResTcpCurrencyUpdateMessage) {
          return mergeFrom((com.proto.TcpMessage.ResTcpCurrencyUpdateMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.proto.TcpMessage.ResTcpCurrencyUpdateMessage other) {
        if (other == com.proto.TcpMessage.ResTcpCurrencyUpdateMessage.getDefaultInstance()) return this;
        if (other.getMsgID() != 0) {
          setMsgID(other.getMsgID());
        }
        if (cItemBuilder_ == null) {
          if (!other.cItem_.isEmpty()) {
            if (cItem_.isEmpty()) {
              cItem_ = other.cItem_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureCItemIsMutable();
              cItem_.addAll(other.cItem_);
            }
            onChanged();
          }
        } else {
          if (!other.cItem_.isEmpty()) {
            if (cItemBuilder_.isEmpty()) {
              cItemBuilder_.dispose();
              cItemBuilder_ = null;
              cItem_ = other.cItem_;
              bitField0_ = (bitField0_ & ~0x00000001);
              cItemBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getCItemFieldBuilder() : null;
            } else {
              cItemBuilder_.addAllMessages(other.cItem_);
            }
          }
        }
        if (other.getReason() != 0) {
          setReason(other.getReason());
        }
        if (other.getSource() != 0) {
          setSource(other.getSource());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.proto.TcpMessage.ResTcpCurrencyUpdateMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.proto.TcpMessage.ResTcpCurrencyUpdateMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int msgID_ ;
      /**
       * <code>int32 msgID = 1;</code>
       * @return The msgID.
       */
      @java.lang.Override
      public int getMsgID() {
        return msgID_;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @param value The msgID to set.
       * @return This builder for chaining.
       */
      public Builder setMsgID(int value) {
        
        msgID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgID() {
        
        msgID_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.proto.CommonMessage.DCurrencyItem> cItem_ =
        java.util.Collections.emptyList();
      private void ensureCItemIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          cItem_ = new java.util.ArrayList<com.proto.CommonMessage.DCurrencyItem>(cItem_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.proto.CommonMessage.DCurrencyItem, com.proto.CommonMessage.DCurrencyItem.Builder, com.proto.CommonMessage.DCurrencyItemOrBuilder> cItemBuilder_;

      /**
       * <pre>
       *货币列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.DCurrencyItem cItem = 2;</code>
       */
      public java.util.List<com.proto.CommonMessage.DCurrencyItem> getCItemList() {
        if (cItemBuilder_ == null) {
          return java.util.Collections.unmodifiableList(cItem_);
        } else {
          return cItemBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       *货币列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.DCurrencyItem cItem = 2;</code>
       */
      public int getCItemCount() {
        if (cItemBuilder_ == null) {
          return cItem_.size();
        } else {
          return cItemBuilder_.getCount();
        }
      }
      /**
       * <pre>
       *货币列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.DCurrencyItem cItem = 2;</code>
       */
      public com.proto.CommonMessage.DCurrencyItem getCItem(int index) {
        if (cItemBuilder_ == null) {
          return cItem_.get(index);
        } else {
          return cItemBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       *货币列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.DCurrencyItem cItem = 2;</code>
       */
      public Builder setCItem(
          int index, com.proto.CommonMessage.DCurrencyItem value) {
        if (cItemBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureCItemIsMutable();
          cItem_.set(index, value);
          onChanged();
        } else {
          cItemBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *货币列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.DCurrencyItem cItem = 2;</code>
       */
      public Builder setCItem(
          int index, com.proto.CommonMessage.DCurrencyItem.Builder builderForValue) {
        if (cItemBuilder_ == null) {
          ensureCItemIsMutable();
          cItem_.set(index, builderForValue.build());
          onChanged();
        } else {
          cItemBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *货币列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.DCurrencyItem cItem = 2;</code>
       */
      public Builder addCItem(com.proto.CommonMessage.DCurrencyItem value) {
        if (cItemBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureCItemIsMutable();
          cItem_.add(value);
          onChanged();
        } else {
          cItemBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       *货币列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.DCurrencyItem cItem = 2;</code>
       */
      public Builder addCItem(
          int index, com.proto.CommonMessage.DCurrencyItem value) {
        if (cItemBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureCItemIsMutable();
          cItem_.add(index, value);
          onChanged();
        } else {
          cItemBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *货币列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.DCurrencyItem cItem = 2;</code>
       */
      public Builder addCItem(
          com.proto.CommonMessage.DCurrencyItem.Builder builderForValue) {
        if (cItemBuilder_ == null) {
          ensureCItemIsMutable();
          cItem_.add(builderForValue.build());
          onChanged();
        } else {
          cItemBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *货币列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.DCurrencyItem cItem = 2;</code>
       */
      public Builder addCItem(
          int index, com.proto.CommonMessage.DCurrencyItem.Builder builderForValue) {
        if (cItemBuilder_ == null) {
          ensureCItemIsMutable();
          cItem_.add(index, builderForValue.build());
          onChanged();
        } else {
          cItemBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *货币列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.DCurrencyItem cItem = 2;</code>
       */
      public Builder addAllCItem(
          java.lang.Iterable<? extends com.proto.CommonMessage.DCurrencyItem> values) {
        if (cItemBuilder_ == null) {
          ensureCItemIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, cItem_);
          onChanged();
        } else {
          cItemBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       *货币列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.DCurrencyItem cItem = 2;</code>
       */
      public Builder clearCItem() {
        if (cItemBuilder_ == null) {
          cItem_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          cItemBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       *货币列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.DCurrencyItem cItem = 2;</code>
       */
      public Builder removeCItem(int index) {
        if (cItemBuilder_ == null) {
          ensureCItemIsMutable();
          cItem_.remove(index);
          onChanged();
        } else {
          cItemBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       *货币列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.DCurrencyItem cItem = 2;</code>
       */
      public com.proto.CommonMessage.DCurrencyItem.Builder getCItemBuilder(
          int index) {
        return getCItemFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       *货币列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.DCurrencyItem cItem = 2;</code>
       */
      public com.proto.CommonMessage.DCurrencyItemOrBuilder getCItemOrBuilder(
          int index) {
        if (cItemBuilder_ == null) {
          return cItem_.get(index);  } else {
          return cItemBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       *货币列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.DCurrencyItem cItem = 2;</code>
       */
      public java.util.List<? extends com.proto.CommonMessage.DCurrencyItemOrBuilder> 
           getCItemOrBuilderList() {
        if (cItemBuilder_ != null) {
          return cItemBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(cItem_);
        }
      }
      /**
       * <pre>
       *货币列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.DCurrencyItem cItem = 2;</code>
       */
      public com.proto.CommonMessage.DCurrencyItem.Builder addCItemBuilder() {
        return getCItemFieldBuilder().addBuilder(
            com.proto.CommonMessage.DCurrencyItem.getDefaultInstance());
      }
      /**
       * <pre>
       *货币列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.DCurrencyItem cItem = 2;</code>
       */
      public com.proto.CommonMessage.DCurrencyItem.Builder addCItemBuilder(
          int index) {
        return getCItemFieldBuilder().addBuilder(
            index, com.proto.CommonMessage.DCurrencyItem.getDefaultInstance());
      }
      /**
       * <pre>
       *货币列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.DCurrencyItem cItem = 2;</code>
       */
      public java.util.List<com.proto.CommonMessage.DCurrencyItem.Builder> 
           getCItemBuilderList() {
        return getCItemFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.proto.CommonMessage.DCurrencyItem, com.proto.CommonMessage.DCurrencyItem.Builder, com.proto.CommonMessage.DCurrencyItemOrBuilder> 
          getCItemFieldBuilder() {
        if (cItemBuilder_ == null) {
          cItemBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.proto.CommonMessage.DCurrencyItem, com.proto.CommonMessage.DCurrencyItem.Builder, com.proto.CommonMessage.DCurrencyItemOrBuilder>(
                  cItem_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          cItem_ = null;
        }
        return cItemBuilder_;
      }

      private int reason_ ;
      /**
       * <pre>
       *原因 1.奖励 2.消耗
       * </pre>
       *
       * <code>int32 reason = 3;</code>
       * @return The reason.
       */
      @java.lang.Override
      public int getReason() {
        return reason_;
      }
      /**
       * <pre>
       *原因 1.奖励 2.消耗
       * </pre>
       *
       * <code>int32 reason = 3;</code>
       * @param value The reason to set.
       * @return This builder for chaining.
       */
      public Builder setReason(int value) {
        
        reason_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *原因 1.奖励 2.消耗
       * </pre>
       *
       * <code>int32 reason = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearReason() {
        
        reason_ = 0;
        onChanged();
        return this;
      }

      private int source_ ;
      /**
       * <pre>
       *来源
       * </pre>
       *
       * <code>int32 source = 4;</code>
       * @return The source.
       */
      @java.lang.Override
      public int getSource() {
        return source_;
      }
      /**
       * <pre>
       *来源
       * </pre>
       *
       * <code>int32 source = 4;</code>
       * @param value The source to set.
       * @return This builder for chaining.
       */
      public Builder setSource(int value) {
        
        source_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *来源
       * </pre>
       *
       * <code>int32 source = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearSource() {
        
        source_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ProtoMessage.ResTcpCurrencyUpdateMessage)
    }

    // @@protoc_insertion_point(class_scope:ProtoMessage.ResTcpCurrencyUpdateMessage)
    private static final com.proto.TcpMessage.ResTcpCurrencyUpdateMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.proto.TcpMessage.ResTcpCurrencyUpdateMessage();
    }

    public static com.proto.TcpMessage.ResTcpCurrencyUpdateMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResTcpCurrencyUpdateMessage>
        PARSER = new com.google.protobuf.AbstractParser<ResTcpCurrencyUpdateMessage>() {
      @java.lang.Override
      public ResTcpCurrencyUpdateMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResTcpCurrencyUpdateMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResTcpCurrencyUpdateMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResTcpCurrencyUpdateMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.proto.TcpMessage.ResTcpCurrencyUpdateMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResTcpReceiveInboxMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ProtoMessage.ResTcpReceiveInboxMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    int getMsgID();

    /**
     * <pre>
     *邮件列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.InboxInfo inboxList = 2;</code>
     */
    java.util.List<com.proto.CommonMessage.InboxInfo> 
        getInboxListList();
    /**
     * <pre>
     *邮件列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.InboxInfo inboxList = 2;</code>
     */
    com.proto.CommonMessage.InboxInfo getInboxList(int index);
    /**
     * <pre>
     *邮件列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.InboxInfo inboxList = 2;</code>
     */
    int getInboxListCount();
    /**
     * <pre>
     *邮件列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.InboxInfo inboxList = 2;</code>
     */
    java.util.List<? extends com.proto.CommonMessage.InboxInfoOrBuilder> 
        getInboxListOrBuilderList();
    /**
     * <pre>
     *邮件列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.InboxInfo inboxList = 2;</code>
     */
    com.proto.CommonMessage.InboxInfoOrBuilder getInboxListOrBuilder(
        int index);
  }
  /**
   * <pre>
   *返回接收邮件
   * </pre>
   *
   * Protobuf type {@code ProtoMessage.ResTcpReceiveInboxMessage}
   */
  public static final class ResTcpReceiveInboxMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ProtoMessage.ResTcpReceiveInboxMessage)
      ResTcpReceiveInboxMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResTcpReceiveInboxMessage.newBuilder() to construct.
    private ResTcpReceiveInboxMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResTcpReceiveInboxMessage() {
      inboxList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResTcpReceiveInboxMessage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResTcpReceiveInboxMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              msgID_ = input.readInt32();
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                inboxList_ = new java.util.ArrayList<com.proto.CommonMessage.InboxInfo>();
                mutable_bitField0_ |= 0x00000001;
              }
              inboxList_.add(
                  input.readMessage(com.proto.CommonMessage.InboxInfo.parser(), extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          inboxList_ = java.util.Collections.unmodifiableList(inboxList_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpReceiveInboxMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpReceiveInboxMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.proto.TcpMessage.ResTcpReceiveInboxMessage.class, com.proto.TcpMessage.ResTcpReceiveInboxMessage.Builder.class);
    }

    public static final int MSGID_FIELD_NUMBER = 1;
    private int msgID_;
    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    @java.lang.Override
    public int getMsgID() {
      return msgID_;
    }

    public static final int INBOXLIST_FIELD_NUMBER = 2;
    private java.util.List<com.proto.CommonMessage.InboxInfo> inboxList_;
    /**
     * <pre>
     *邮件列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.InboxInfo inboxList = 2;</code>
     */
    @java.lang.Override
    public java.util.List<com.proto.CommonMessage.InboxInfo> getInboxListList() {
      return inboxList_;
    }
    /**
     * <pre>
     *邮件列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.InboxInfo inboxList = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.proto.CommonMessage.InboxInfoOrBuilder> 
        getInboxListOrBuilderList() {
      return inboxList_;
    }
    /**
     * <pre>
     *邮件列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.InboxInfo inboxList = 2;</code>
     */
    @java.lang.Override
    public int getInboxListCount() {
      return inboxList_.size();
    }
    /**
     * <pre>
     *邮件列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.InboxInfo inboxList = 2;</code>
     */
    @java.lang.Override
    public com.proto.CommonMessage.InboxInfo getInboxList(int index) {
      return inboxList_.get(index);
    }
    /**
     * <pre>
     *邮件列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.InboxInfo inboxList = 2;</code>
     */
    @java.lang.Override
    public com.proto.CommonMessage.InboxInfoOrBuilder getInboxListOrBuilder(
        int index) {
      return inboxList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (msgID_ != 0) {
        output.writeInt32(1, msgID_);
      }
      for (int i = 0; i < inboxList_.size(); i++) {
        output.writeMessage(2, inboxList_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (msgID_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, msgID_);
      }
      for (int i = 0; i < inboxList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, inboxList_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.proto.TcpMessage.ResTcpReceiveInboxMessage)) {
        return super.equals(obj);
      }
      com.proto.TcpMessage.ResTcpReceiveInboxMessage other = (com.proto.TcpMessage.ResTcpReceiveInboxMessage) obj;

      if (getMsgID()
          != other.getMsgID()) return false;
      if (!getInboxListList()
          .equals(other.getInboxListList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + MSGID_FIELD_NUMBER;
      hash = (53 * hash) + getMsgID();
      if (getInboxListCount() > 0) {
        hash = (37 * hash) + INBOXLIST_FIELD_NUMBER;
        hash = (53 * hash) + getInboxListList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.proto.TcpMessage.ResTcpReceiveInboxMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ResTcpReceiveInboxMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpReceiveInboxMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ResTcpReceiveInboxMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpReceiveInboxMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ResTcpReceiveInboxMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpReceiveInboxMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ResTcpReceiveInboxMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpReceiveInboxMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ResTcpReceiveInboxMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpReceiveInboxMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ResTcpReceiveInboxMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.proto.TcpMessage.ResTcpReceiveInboxMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *返回接收邮件
     * </pre>
     *
     * Protobuf type {@code ProtoMessage.ResTcpReceiveInboxMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ProtoMessage.ResTcpReceiveInboxMessage)
        com.proto.TcpMessage.ResTcpReceiveInboxMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpReceiveInboxMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpReceiveInboxMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.proto.TcpMessage.ResTcpReceiveInboxMessage.class, com.proto.TcpMessage.ResTcpReceiveInboxMessage.Builder.class);
      }

      // Construct using com.proto.TcpMessage.ResTcpReceiveInboxMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getInboxListFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        msgID_ = 0;

        if (inboxListBuilder_ == null) {
          inboxList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          inboxListBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpReceiveInboxMessage_descriptor;
      }

      @java.lang.Override
      public com.proto.TcpMessage.ResTcpReceiveInboxMessage getDefaultInstanceForType() {
        return com.proto.TcpMessage.ResTcpReceiveInboxMessage.getDefaultInstance();
      }

      @java.lang.Override
      public com.proto.TcpMessage.ResTcpReceiveInboxMessage build() {
        com.proto.TcpMessage.ResTcpReceiveInboxMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.proto.TcpMessage.ResTcpReceiveInboxMessage buildPartial() {
        com.proto.TcpMessage.ResTcpReceiveInboxMessage result = new com.proto.TcpMessage.ResTcpReceiveInboxMessage(this);
        int from_bitField0_ = bitField0_;
        result.msgID_ = msgID_;
        if (inboxListBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            inboxList_ = java.util.Collections.unmodifiableList(inboxList_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.inboxList_ = inboxList_;
        } else {
          result.inboxList_ = inboxListBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.proto.TcpMessage.ResTcpReceiveInboxMessage) {
          return mergeFrom((com.proto.TcpMessage.ResTcpReceiveInboxMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.proto.TcpMessage.ResTcpReceiveInboxMessage other) {
        if (other == com.proto.TcpMessage.ResTcpReceiveInboxMessage.getDefaultInstance()) return this;
        if (other.getMsgID() != 0) {
          setMsgID(other.getMsgID());
        }
        if (inboxListBuilder_ == null) {
          if (!other.inboxList_.isEmpty()) {
            if (inboxList_.isEmpty()) {
              inboxList_ = other.inboxList_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureInboxListIsMutable();
              inboxList_.addAll(other.inboxList_);
            }
            onChanged();
          }
        } else {
          if (!other.inboxList_.isEmpty()) {
            if (inboxListBuilder_.isEmpty()) {
              inboxListBuilder_.dispose();
              inboxListBuilder_ = null;
              inboxList_ = other.inboxList_;
              bitField0_ = (bitField0_ & ~0x00000001);
              inboxListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getInboxListFieldBuilder() : null;
            } else {
              inboxListBuilder_.addAllMessages(other.inboxList_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.proto.TcpMessage.ResTcpReceiveInboxMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.proto.TcpMessage.ResTcpReceiveInboxMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int msgID_ ;
      /**
       * <code>int32 msgID = 1;</code>
       * @return The msgID.
       */
      @java.lang.Override
      public int getMsgID() {
        return msgID_;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @param value The msgID to set.
       * @return This builder for chaining.
       */
      public Builder setMsgID(int value) {
        
        msgID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgID() {
        
        msgID_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.proto.CommonMessage.InboxInfo> inboxList_ =
        java.util.Collections.emptyList();
      private void ensureInboxListIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          inboxList_ = new java.util.ArrayList<com.proto.CommonMessage.InboxInfo>(inboxList_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.proto.CommonMessage.InboxInfo, com.proto.CommonMessage.InboxInfo.Builder, com.proto.CommonMessage.InboxInfoOrBuilder> inboxListBuilder_;

      /**
       * <pre>
       *邮件列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.InboxInfo inboxList = 2;</code>
       */
      public java.util.List<com.proto.CommonMessage.InboxInfo> getInboxListList() {
        if (inboxListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(inboxList_);
        } else {
          return inboxListBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       *邮件列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.InboxInfo inboxList = 2;</code>
       */
      public int getInboxListCount() {
        if (inboxListBuilder_ == null) {
          return inboxList_.size();
        } else {
          return inboxListBuilder_.getCount();
        }
      }
      /**
       * <pre>
       *邮件列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.InboxInfo inboxList = 2;</code>
       */
      public com.proto.CommonMessage.InboxInfo getInboxList(int index) {
        if (inboxListBuilder_ == null) {
          return inboxList_.get(index);
        } else {
          return inboxListBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       *邮件列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.InboxInfo inboxList = 2;</code>
       */
      public Builder setInboxList(
          int index, com.proto.CommonMessage.InboxInfo value) {
        if (inboxListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureInboxListIsMutable();
          inboxList_.set(index, value);
          onChanged();
        } else {
          inboxListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *邮件列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.InboxInfo inboxList = 2;</code>
       */
      public Builder setInboxList(
          int index, com.proto.CommonMessage.InboxInfo.Builder builderForValue) {
        if (inboxListBuilder_ == null) {
          ensureInboxListIsMutable();
          inboxList_.set(index, builderForValue.build());
          onChanged();
        } else {
          inboxListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *邮件列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.InboxInfo inboxList = 2;</code>
       */
      public Builder addInboxList(com.proto.CommonMessage.InboxInfo value) {
        if (inboxListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureInboxListIsMutable();
          inboxList_.add(value);
          onChanged();
        } else {
          inboxListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       *邮件列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.InboxInfo inboxList = 2;</code>
       */
      public Builder addInboxList(
          int index, com.proto.CommonMessage.InboxInfo value) {
        if (inboxListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureInboxListIsMutable();
          inboxList_.add(index, value);
          onChanged();
        } else {
          inboxListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *邮件列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.InboxInfo inboxList = 2;</code>
       */
      public Builder addInboxList(
          com.proto.CommonMessage.InboxInfo.Builder builderForValue) {
        if (inboxListBuilder_ == null) {
          ensureInboxListIsMutable();
          inboxList_.add(builderForValue.build());
          onChanged();
        } else {
          inboxListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *邮件列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.InboxInfo inboxList = 2;</code>
       */
      public Builder addInboxList(
          int index, com.proto.CommonMessage.InboxInfo.Builder builderForValue) {
        if (inboxListBuilder_ == null) {
          ensureInboxListIsMutable();
          inboxList_.add(index, builderForValue.build());
          onChanged();
        } else {
          inboxListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *邮件列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.InboxInfo inboxList = 2;</code>
       */
      public Builder addAllInboxList(
          java.lang.Iterable<? extends com.proto.CommonMessage.InboxInfo> values) {
        if (inboxListBuilder_ == null) {
          ensureInboxListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, inboxList_);
          onChanged();
        } else {
          inboxListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       *邮件列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.InboxInfo inboxList = 2;</code>
       */
      public Builder clearInboxList() {
        if (inboxListBuilder_ == null) {
          inboxList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          inboxListBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       *邮件列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.InboxInfo inboxList = 2;</code>
       */
      public Builder removeInboxList(int index) {
        if (inboxListBuilder_ == null) {
          ensureInboxListIsMutable();
          inboxList_.remove(index);
          onChanged();
        } else {
          inboxListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       *邮件列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.InboxInfo inboxList = 2;</code>
       */
      public com.proto.CommonMessage.InboxInfo.Builder getInboxListBuilder(
          int index) {
        return getInboxListFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       *邮件列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.InboxInfo inboxList = 2;</code>
       */
      public com.proto.CommonMessage.InboxInfoOrBuilder getInboxListOrBuilder(
          int index) {
        if (inboxListBuilder_ == null) {
          return inboxList_.get(index);  } else {
          return inboxListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       *邮件列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.InboxInfo inboxList = 2;</code>
       */
      public java.util.List<? extends com.proto.CommonMessage.InboxInfoOrBuilder> 
           getInboxListOrBuilderList() {
        if (inboxListBuilder_ != null) {
          return inboxListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(inboxList_);
        }
      }
      /**
       * <pre>
       *邮件列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.InboxInfo inboxList = 2;</code>
       */
      public com.proto.CommonMessage.InboxInfo.Builder addInboxListBuilder() {
        return getInboxListFieldBuilder().addBuilder(
            com.proto.CommonMessage.InboxInfo.getDefaultInstance());
      }
      /**
       * <pre>
       *邮件列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.InboxInfo inboxList = 2;</code>
       */
      public com.proto.CommonMessage.InboxInfo.Builder addInboxListBuilder(
          int index) {
        return getInboxListFieldBuilder().addBuilder(
            index, com.proto.CommonMessage.InboxInfo.getDefaultInstance());
      }
      /**
       * <pre>
       *邮件列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.InboxInfo inboxList = 2;</code>
       */
      public java.util.List<com.proto.CommonMessage.InboxInfo.Builder> 
           getInboxListBuilderList() {
        return getInboxListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.proto.CommonMessage.InboxInfo, com.proto.CommonMessage.InboxInfo.Builder, com.proto.CommonMessage.InboxInfoOrBuilder> 
          getInboxListFieldBuilder() {
        if (inboxListBuilder_ == null) {
          inboxListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.proto.CommonMessage.InboxInfo, com.proto.CommonMessage.InboxInfo.Builder, com.proto.CommonMessage.InboxInfoOrBuilder>(
                  inboxList_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          inboxList_ = null;
        }
        return inboxListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ProtoMessage.ResTcpReceiveInboxMessage)
    }

    // @@protoc_insertion_point(class_scope:ProtoMessage.ResTcpReceiveInboxMessage)
    private static final com.proto.TcpMessage.ResTcpReceiveInboxMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.proto.TcpMessage.ResTcpReceiveInboxMessage();
    }

    public static com.proto.TcpMessage.ResTcpReceiveInboxMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResTcpReceiveInboxMessage>
        PARSER = new com.google.protobuf.AbstractParser<ResTcpReceiveInboxMessage>() {
      @java.lang.Override
      public ResTcpReceiveInboxMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResTcpReceiveInboxMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResTcpReceiveInboxMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResTcpReceiveInboxMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.proto.TcpMessage.ResTcpReceiveInboxMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResTcpVipClubExpChangeMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ProtoMessage.ResTcpVipClubExpChangeMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    int getMsgID();

    /**
     * <pre>
     *当前经验
     * </pre>
     *
     * <code>double curExp = 2;</code>
     * @return The curExp.
     */
    double getCurExp();

    /**
     * <pre>
     *需要经验
     * </pre>
     *
     * <code>double needExp = 3;</code>
     * @return The needExp.
     */
    double getNeedExp();

    /**
     * <pre>
     *等级
     * </pre>
     *
     * <code>int32 vipLevel = 4;</code>
     * @return The vipLevel.
     */
    int getVipLevel();

    /**
     * <pre>
     *当前充值
     * </pre>
     *
     * <code>double curRecharge = 5;</code>
     * @return The curRecharge.
     */
    double getCurRecharge();

    /**
     * <pre>
     *需要充值
     * </pre>
     *
     * <code>double needRecharge = 6;</code>
     * @return The needRecharge.
     */
    double getNeedRecharge();
  }
  /**
   * <pre>
   *返回vip经验变化
   * </pre>
   *
   * Protobuf type {@code ProtoMessage.ResTcpVipClubExpChangeMessage}
   */
  public static final class ResTcpVipClubExpChangeMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ProtoMessage.ResTcpVipClubExpChangeMessage)
      ResTcpVipClubExpChangeMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResTcpVipClubExpChangeMessage.newBuilder() to construct.
    private ResTcpVipClubExpChangeMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResTcpVipClubExpChangeMessage() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResTcpVipClubExpChangeMessage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResTcpVipClubExpChangeMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              msgID_ = input.readInt32();
              break;
            }
            case 17: {

              curExp_ = input.readDouble();
              break;
            }
            case 25: {

              needExp_ = input.readDouble();
              break;
            }
            case 32: {

              vipLevel_ = input.readInt32();
              break;
            }
            case 41: {

              curRecharge_ = input.readDouble();
              break;
            }
            case 49: {

              needRecharge_ = input.readDouble();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpVipClubExpChangeMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpVipClubExpChangeMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.proto.TcpMessage.ResTcpVipClubExpChangeMessage.class, com.proto.TcpMessage.ResTcpVipClubExpChangeMessage.Builder.class);
    }

    public static final int MSGID_FIELD_NUMBER = 1;
    private int msgID_;
    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    @java.lang.Override
    public int getMsgID() {
      return msgID_;
    }

    public static final int CUREXP_FIELD_NUMBER = 2;
    private double curExp_;
    /**
     * <pre>
     *当前经验
     * </pre>
     *
     * <code>double curExp = 2;</code>
     * @return The curExp.
     */
    @java.lang.Override
    public double getCurExp() {
      return curExp_;
    }

    public static final int NEEDEXP_FIELD_NUMBER = 3;
    private double needExp_;
    /**
     * <pre>
     *需要经验
     * </pre>
     *
     * <code>double needExp = 3;</code>
     * @return The needExp.
     */
    @java.lang.Override
    public double getNeedExp() {
      return needExp_;
    }

    public static final int VIPLEVEL_FIELD_NUMBER = 4;
    private int vipLevel_;
    /**
     * <pre>
     *等级
     * </pre>
     *
     * <code>int32 vipLevel = 4;</code>
     * @return The vipLevel.
     */
    @java.lang.Override
    public int getVipLevel() {
      return vipLevel_;
    }

    public static final int CURRECHARGE_FIELD_NUMBER = 5;
    private double curRecharge_;
    /**
     * <pre>
     *当前充值
     * </pre>
     *
     * <code>double curRecharge = 5;</code>
     * @return The curRecharge.
     */
    @java.lang.Override
    public double getCurRecharge() {
      return curRecharge_;
    }

    public static final int NEEDRECHARGE_FIELD_NUMBER = 6;
    private double needRecharge_;
    /**
     * <pre>
     *需要充值
     * </pre>
     *
     * <code>double needRecharge = 6;</code>
     * @return The needRecharge.
     */
    @java.lang.Override
    public double getNeedRecharge() {
      return needRecharge_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (msgID_ != 0) {
        output.writeInt32(1, msgID_);
      }
      if (curExp_ != 0D) {
        output.writeDouble(2, curExp_);
      }
      if (needExp_ != 0D) {
        output.writeDouble(3, needExp_);
      }
      if (vipLevel_ != 0) {
        output.writeInt32(4, vipLevel_);
      }
      if (curRecharge_ != 0D) {
        output.writeDouble(5, curRecharge_);
      }
      if (needRecharge_ != 0D) {
        output.writeDouble(6, needRecharge_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (msgID_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, msgID_);
      }
      if (curExp_ != 0D) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(2, curExp_);
      }
      if (needExp_ != 0D) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(3, needExp_);
      }
      if (vipLevel_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, vipLevel_);
      }
      if (curRecharge_ != 0D) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(5, curRecharge_);
      }
      if (needRecharge_ != 0D) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(6, needRecharge_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.proto.TcpMessage.ResTcpVipClubExpChangeMessage)) {
        return super.equals(obj);
      }
      com.proto.TcpMessage.ResTcpVipClubExpChangeMessage other = (com.proto.TcpMessage.ResTcpVipClubExpChangeMessage) obj;

      if (getMsgID()
          != other.getMsgID()) return false;
      if (java.lang.Double.doubleToLongBits(getCurExp())
          != java.lang.Double.doubleToLongBits(
              other.getCurExp())) return false;
      if (java.lang.Double.doubleToLongBits(getNeedExp())
          != java.lang.Double.doubleToLongBits(
              other.getNeedExp())) return false;
      if (getVipLevel()
          != other.getVipLevel()) return false;
      if (java.lang.Double.doubleToLongBits(getCurRecharge())
          != java.lang.Double.doubleToLongBits(
              other.getCurRecharge())) return false;
      if (java.lang.Double.doubleToLongBits(getNeedRecharge())
          != java.lang.Double.doubleToLongBits(
              other.getNeedRecharge())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + MSGID_FIELD_NUMBER;
      hash = (53 * hash) + getMsgID();
      hash = (37 * hash) + CUREXP_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          java.lang.Double.doubleToLongBits(getCurExp()));
      hash = (37 * hash) + NEEDEXP_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          java.lang.Double.doubleToLongBits(getNeedExp()));
      hash = (37 * hash) + VIPLEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getVipLevel();
      hash = (37 * hash) + CURRECHARGE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          java.lang.Double.doubleToLongBits(getCurRecharge()));
      hash = (37 * hash) + NEEDRECHARGE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          java.lang.Double.doubleToLongBits(getNeedRecharge()));
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.proto.TcpMessage.ResTcpVipClubExpChangeMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ResTcpVipClubExpChangeMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpVipClubExpChangeMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ResTcpVipClubExpChangeMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpVipClubExpChangeMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ResTcpVipClubExpChangeMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpVipClubExpChangeMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ResTcpVipClubExpChangeMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpVipClubExpChangeMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ResTcpVipClubExpChangeMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpVipClubExpChangeMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ResTcpVipClubExpChangeMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.proto.TcpMessage.ResTcpVipClubExpChangeMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *返回vip经验变化
     * </pre>
     *
     * Protobuf type {@code ProtoMessage.ResTcpVipClubExpChangeMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ProtoMessage.ResTcpVipClubExpChangeMessage)
        com.proto.TcpMessage.ResTcpVipClubExpChangeMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpVipClubExpChangeMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpVipClubExpChangeMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.proto.TcpMessage.ResTcpVipClubExpChangeMessage.class, com.proto.TcpMessage.ResTcpVipClubExpChangeMessage.Builder.class);
      }

      // Construct using com.proto.TcpMessage.ResTcpVipClubExpChangeMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        msgID_ = 0;

        curExp_ = 0D;

        needExp_ = 0D;

        vipLevel_ = 0;

        curRecharge_ = 0D;

        needRecharge_ = 0D;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpVipClubExpChangeMessage_descriptor;
      }

      @java.lang.Override
      public com.proto.TcpMessage.ResTcpVipClubExpChangeMessage getDefaultInstanceForType() {
        return com.proto.TcpMessage.ResTcpVipClubExpChangeMessage.getDefaultInstance();
      }

      @java.lang.Override
      public com.proto.TcpMessage.ResTcpVipClubExpChangeMessage build() {
        com.proto.TcpMessage.ResTcpVipClubExpChangeMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.proto.TcpMessage.ResTcpVipClubExpChangeMessage buildPartial() {
        com.proto.TcpMessage.ResTcpVipClubExpChangeMessage result = new com.proto.TcpMessage.ResTcpVipClubExpChangeMessage(this);
        result.msgID_ = msgID_;
        result.curExp_ = curExp_;
        result.needExp_ = needExp_;
        result.vipLevel_ = vipLevel_;
        result.curRecharge_ = curRecharge_;
        result.needRecharge_ = needRecharge_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.proto.TcpMessage.ResTcpVipClubExpChangeMessage) {
          return mergeFrom((com.proto.TcpMessage.ResTcpVipClubExpChangeMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.proto.TcpMessage.ResTcpVipClubExpChangeMessage other) {
        if (other == com.proto.TcpMessage.ResTcpVipClubExpChangeMessage.getDefaultInstance()) return this;
        if (other.getMsgID() != 0) {
          setMsgID(other.getMsgID());
        }
        if (other.getCurExp() != 0D) {
          setCurExp(other.getCurExp());
        }
        if (other.getNeedExp() != 0D) {
          setNeedExp(other.getNeedExp());
        }
        if (other.getVipLevel() != 0) {
          setVipLevel(other.getVipLevel());
        }
        if (other.getCurRecharge() != 0D) {
          setCurRecharge(other.getCurRecharge());
        }
        if (other.getNeedRecharge() != 0D) {
          setNeedRecharge(other.getNeedRecharge());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.proto.TcpMessage.ResTcpVipClubExpChangeMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.proto.TcpMessage.ResTcpVipClubExpChangeMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int msgID_ ;
      /**
       * <code>int32 msgID = 1;</code>
       * @return The msgID.
       */
      @java.lang.Override
      public int getMsgID() {
        return msgID_;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @param value The msgID to set.
       * @return This builder for chaining.
       */
      public Builder setMsgID(int value) {
        
        msgID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgID() {
        
        msgID_ = 0;
        onChanged();
        return this;
      }

      private double curExp_ ;
      /**
       * <pre>
       *当前经验
       * </pre>
       *
       * <code>double curExp = 2;</code>
       * @return The curExp.
       */
      @java.lang.Override
      public double getCurExp() {
        return curExp_;
      }
      /**
       * <pre>
       *当前经验
       * </pre>
       *
       * <code>double curExp = 2;</code>
       * @param value The curExp to set.
       * @return This builder for chaining.
       */
      public Builder setCurExp(double value) {
        
        curExp_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *当前经验
       * </pre>
       *
       * <code>double curExp = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearCurExp() {
        
        curExp_ = 0D;
        onChanged();
        return this;
      }

      private double needExp_ ;
      /**
       * <pre>
       *需要经验
       * </pre>
       *
       * <code>double needExp = 3;</code>
       * @return The needExp.
       */
      @java.lang.Override
      public double getNeedExp() {
        return needExp_;
      }
      /**
       * <pre>
       *需要经验
       * </pre>
       *
       * <code>double needExp = 3;</code>
       * @param value The needExp to set.
       * @return This builder for chaining.
       */
      public Builder setNeedExp(double value) {
        
        needExp_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *需要经验
       * </pre>
       *
       * <code>double needExp = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearNeedExp() {
        
        needExp_ = 0D;
        onChanged();
        return this;
      }

      private int vipLevel_ ;
      /**
       * <pre>
       *等级
       * </pre>
       *
       * <code>int32 vipLevel = 4;</code>
       * @return The vipLevel.
       */
      @java.lang.Override
      public int getVipLevel() {
        return vipLevel_;
      }
      /**
       * <pre>
       *等级
       * </pre>
       *
       * <code>int32 vipLevel = 4;</code>
       * @param value The vipLevel to set.
       * @return This builder for chaining.
       */
      public Builder setVipLevel(int value) {
        
        vipLevel_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *等级
       * </pre>
       *
       * <code>int32 vipLevel = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearVipLevel() {
        
        vipLevel_ = 0;
        onChanged();
        return this;
      }

      private double curRecharge_ ;
      /**
       * <pre>
       *当前充值
       * </pre>
       *
       * <code>double curRecharge = 5;</code>
       * @return The curRecharge.
       */
      @java.lang.Override
      public double getCurRecharge() {
        return curRecharge_;
      }
      /**
       * <pre>
       *当前充值
       * </pre>
       *
       * <code>double curRecharge = 5;</code>
       * @param value The curRecharge to set.
       * @return This builder for chaining.
       */
      public Builder setCurRecharge(double value) {
        
        curRecharge_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *当前充值
       * </pre>
       *
       * <code>double curRecharge = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearCurRecharge() {
        
        curRecharge_ = 0D;
        onChanged();
        return this;
      }

      private double needRecharge_ ;
      /**
       * <pre>
       *需要充值
       * </pre>
       *
       * <code>double needRecharge = 6;</code>
       * @return The needRecharge.
       */
      @java.lang.Override
      public double getNeedRecharge() {
        return needRecharge_;
      }
      /**
       * <pre>
       *需要充值
       * </pre>
       *
       * <code>double needRecharge = 6;</code>
       * @param value The needRecharge to set.
       * @return This builder for chaining.
       */
      public Builder setNeedRecharge(double value) {
        
        needRecharge_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *需要充值
       * </pre>
       *
       * <code>double needRecharge = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearNeedRecharge() {
        
        needRecharge_ = 0D;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ProtoMessage.ResTcpVipClubExpChangeMessage)
    }

    // @@protoc_insertion_point(class_scope:ProtoMessage.ResTcpVipClubExpChangeMessage)
    private static final com.proto.TcpMessage.ResTcpVipClubExpChangeMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.proto.TcpMessage.ResTcpVipClubExpChangeMessage();
    }

    public static com.proto.TcpMessage.ResTcpVipClubExpChangeMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResTcpVipClubExpChangeMessage>
        PARSER = new com.google.protobuf.AbstractParser<ResTcpVipClubExpChangeMessage>() {
      @java.lang.Override
      public ResTcpVipClubExpChangeMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResTcpVipClubExpChangeMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResTcpVipClubExpChangeMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResTcpVipClubExpChangeMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.proto.TcpMessage.ResTcpVipClubExpChangeMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResTcpGameNoteDataMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ProtoMessage.ResTcpGameNoteDataMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    int getMsgID();

    /**
     * <pre>
     *商户（服务器使用）
     * </pre>
     *
     * <code>string business_no = 2;</code>
     * @return The businessNo.
     */
    java.lang.String getBusinessNo();
    /**
     * <pre>
     *商户（服务器使用）
     * </pre>
     *
     * <code>string business_no = 2;</code>
     * @return The bytes for businessNo.
     */
    com.google.protobuf.ByteString
        getBusinessNoBytes();

    /**
     * <pre>
     *下注列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo allBet = 3;</code>
     */
    java.util.List<com.proto.CommonMessage.BetInfo> 
        getAllBetList();
    /**
     * <pre>
     *下注列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo allBet = 3;</code>
     */
    com.proto.CommonMessage.BetInfo getAllBet(int index);
    /**
     * <pre>
     *下注列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo allBet = 3;</code>
     */
    int getAllBetCount();
    /**
     * <pre>
     *下注列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo allBet = 3;</code>
     */
    java.util.List<? extends com.proto.CommonMessage.BetInfoOrBuilder> 
        getAllBetOrBuilderList();
    /**
     * <pre>
     *下注列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo allBet = 3;</code>
     */
    com.proto.CommonMessage.BetInfoOrBuilder getAllBetOrBuilder(
        int index);

    /**
     * <pre>
     *高手
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo highRollers = 4;</code>
     */
    java.util.List<com.proto.CommonMessage.BetInfo> 
        getHighRollersList();
    /**
     * <pre>
     *高手
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo highRollers = 4;</code>
     */
    com.proto.CommonMessage.BetInfo getHighRollers(int index);
    /**
     * <pre>
     *高手
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo highRollers = 4;</code>
     */
    int getHighRollersCount();
    /**
     * <pre>
     *高手
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo highRollers = 4;</code>
     */
    java.util.List<? extends com.proto.CommonMessage.BetInfoOrBuilder> 
        getHighRollersOrBuilderList();
    /**
     * <pre>
     *高手
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo highRollers = 4;</code>
     */
    com.proto.CommonMessage.BetInfoOrBuilder getHighRollersOrBuilder(
        int index);

    /**
     * <pre>
     *幸运
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo luckyBet = 5;</code>
     */
    java.util.List<com.proto.CommonMessage.BetInfo> 
        getLuckyBetList();
    /**
     * <pre>
     *幸运
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo luckyBet = 5;</code>
     */
    com.proto.CommonMessage.BetInfo getLuckyBet(int index);
    /**
     * <pre>
     *幸运
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo luckyBet = 5;</code>
     */
    int getLuckyBetCount();
    /**
     * <pre>
     *幸运
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo luckyBet = 5;</code>
     */
    java.util.List<? extends com.proto.CommonMessage.BetInfoOrBuilder> 
        getLuckyBetOrBuilderList();
    /**
     * <pre>
     *幸运
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo luckyBet = 5;</code>
     */
    com.proto.CommonMessage.BetInfoOrBuilder getLuckyBetOrBuilder(
        int index);
  }
  /**
   * <pre>
   *返回游戏下注
   * </pre>
   *
   * Protobuf type {@code ProtoMessage.ResTcpGameNoteDataMessage}
   */
  public static final class ResTcpGameNoteDataMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ProtoMessage.ResTcpGameNoteDataMessage)
      ResTcpGameNoteDataMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResTcpGameNoteDataMessage.newBuilder() to construct.
    private ResTcpGameNoteDataMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResTcpGameNoteDataMessage() {
      businessNo_ = "";
      allBet_ = java.util.Collections.emptyList();
      highRollers_ = java.util.Collections.emptyList();
      luckyBet_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResTcpGameNoteDataMessage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResTcpGameNoteDataMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              msgID_ = input.readInt32();
              break;
            }
            case 18: {
              java.lang.String s = input.readStringRequireUtf8();

              businessNo_ = s;
              break;
            }
            case 26: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                allBet_ = new java.util.ArrayList<com.proto.CommonMessage.BetInfo>();
                mutable_bitField0_ |= 0x00000001;
              }
              allBet_.add(
                  input.readMessage(com.proto.CommonMessage.BetInfo.parser(), extensionRegistry));
              break;
            }
            case 34: {
              if (!((mutable_bitField0_ & 0x00000002) != 0)) {
                highRollers_ = new java.util.ArrayList<com.proto.CommonMessage.BetInfo>();
                mutable_bitField0_ |= 0x00000002;
              }
              highRollers_.add(
                  input.readMessage(com.proto.CommonMessage.BetInfo.parser(), extensionRegistry));
              break;
            }
            case 42: {
              if (!((mutable_bitField0_ & 0x00000004) != 0)) {
                luckyBet_ = new java.util.ArrayList<com.proto.CommonMessage.BetInfo>();
                mutable_bitField0_ |= 0x00000004;
              }
              luckyBet_.add(
                  input.readMessage(com.proto.CommonMessage.BetInfo.parser(), extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          allBet_ = java.util.Collections.unmodifiableList(allBet_);
        }
        if (((mutable_bitField0_ & 0x00000002) != 0)) {
          highRollers_ = java.util.Collections.unmodifiableList(highRollers_);
        }
        if (((mutable_bitField0_ & 0x00000004) != 0)) {
          luckyBet_ = java.util.Collections.unmodifiableList(luckyBet_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpGameNoteDataMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpGameNoteDataMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.proto.TcpMessage.ResTcpGameNoteDataMessage.class, com.proto.TcpMessage.ResTcpGameNoteDataMessage.Builder.class);
    }

    public static final int MSGID_FIELD_NUMBER = 1;
    private int msgID_;
    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    @java.lang.Override
    public int getMsgID() {
      return msgID_;
    }

    public static final int BUSINESS_NO_FIELD_NUMBER = 2;
    private volatile java.lang.Object businessNo_;
    /**
     * <pre>
     *商户（服务器使用）
     * </pre>
     *
     * <code>string business_no = 2;</code>
     * @return The businessNo.
     */
    @java.lang.Override
    public java.lang.String getBusinessNo() {
      java.lang.Object ref = businessNo_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        businessNo_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *商户（服务器使用）
     * </pre>
     *
     * <code>string business_no = 2;</code>
     * @return The bytes for businessNo.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getBusinessNoBytes() {
      java.lang.Object ref = businessNo_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        businessNo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ALLBET_FIELD_NUMBER = 3;
    private java.util.List<com.proto.CommonMessage.BetInfo> allBet_;
    /**
     * <pre>
     *下注列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo allBet = 3;</code>
     */
    @java.lang.Override
    public java.util.List<com.proto.CommonMessage.BetInfo> getAllBetList() {
      return allBet_;
    }
    /**
     * <pre>
     *下注列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo allBet = 3;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.proto.CommonMessage.BetInfoOrBuilder> 
        getAllBetOrBuilderList() {
      return allBet_;
    }
    /**
     * <pre>
     *下注列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo allBet = 3;</code>
     */
    @java.lang.Override
    public int getAllBetCount() {
      return allBet_.size();
    }
    /**
     * <pre>
     *下注列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo allBet = 3;</code>
     */
    @java.lang.Override
    public com.proto.CommonMessage.BetInfo getAllBet(int index) {
      return allBet_.get(index);
    }
    /**
     * <pre>
     *下注列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo allBet = 3;</code>
     */
    @java.lang.Override
    public com.proto.CommonMessage.BetInfoOrBuilder getAllBetOrBuilder(
        int index) {
      return allBet_.get(index);
    }

    public static final int HIGHROLLERS_FIELD_NUMBER = 4;
    private java.util.List<com.proto.CommonMessage.BetInfo> highRollers_;
    /**
     * <pre>
     *高手
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo highRollers = 4;</code>
     */
    @java.lang.Override
    public java.util.List<com.proto.CommonMessage.BetInfo> getHighRollersList() {
      return highRollers_;
    }
    /**
     * <pre>
     *高手
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo highRollers = 4;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.proto.CommonMessage.BetInfoOrBuilder> 
        getHighRollersOrBuilderList() {
      return highRollers_;
    }
    /**
     * <pre>
     *高手
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo highRollers = 4;</code>
     */
    @java.lang.Override
    public int getHighRollersCount() {
      return highRollers_.size();
    }
    /**
     * <pre>
     *高手
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo highRollers = 4;</code>
     */
    @java.lang.Override
    public com.proto.CommonMessage.BetInfo getHighRollers(int index) {
      return highRollers_.get(index);
    }
    /**
     * <pre>
     *高手
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo highRollers = 4;</code>
     */
    @java.lang.Override
    public com.proto.CommonMessage.BetInfoOrBuilder getHighRollersOrBuilder(
        int index) {
      return highRollers_.get(index);
    }

    public static final int LUCKYBET_FIELD_NUMBER = 5;
    private java.util.List<com.proto.CommonMessage.BetInfo> luckyBet_;
    /**
     * <pre>
     *幸运
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo luckyBet = 5;</code>
     */
    @java.lang.Override
    public java.util.List<com.proto.CommonMessage.BetInfo> getLuckyBetList() {
      return luckyBet_;
    }
    /**
     * <pre>
     *幸运
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo luckyBet = 5;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.proto.CommonMessage.BetInfoOrBuilder> 
        getLuckyBetOrBuilderList() {
      return luckyBet_;
    }
    /**
     * <pre>
     *幸运
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo luckyBet = 5;</code>
     */
    @java.lang.Override
    public int getLuckyBetCount() {
      return luckyBet_.size();
    }
    /**
     * <pre>
     *幸运
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo luckyBet = 5;</code>
     */
    @java.lang.Override
    public com.proto.CommonMessage.BetInfo getLuckyBet(int index) {
      return luckyBet_.get(index);
    }
    /**
     * <pre>
     *幸运
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo luckyBet = 5;</code>
     */
    @java.lang.Override
    public com.proto.CommonMessage.BetInfoOrBuilder getLuckyBetOrBuilder(
        int index) {
      return luckyBet_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (msgID_ != 0) {
        output.writeInt32(1, msgID_);
      }
      if (!getBusinessNoBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, businessNo_);
      }
      for (int i = 0; i < allBet_.size(); i++) {
        output.writeMessage(3, allBet_.get(i));
      }
      for (int i = 0; i < highRollers_.size(); i++) {
        output.writeMessage(4, highRollers_.get(i));
      }
      for (int i = 0; i < luckyBet_.size(); i++) {
        output.writeMessage(5, luckyBet_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (msgID_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, msgID_);
      }
      if (!getBusinessNoBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, businessNo_);
      }
      for (int i = 0; i < allBet_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, allBet_.get(i));
      }
      for (int i = 0; i < highRollers_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, highRollers_.get(i));
      }
      for (int i = 0; i < luckyBet_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, luckyBet_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.proto.TcpMessage.ResTcpGameNoteDataMessage)) {
        return super.equals(obj);
      }
      com.proto.TcpMessage.ResTcpGameNoteDataMessage other = (com.proto.TcpMessage.ResTcpGameNoteDataMessage) obj;

      if (getMsgID()
          != other.getMsgID()) return false;
      if (!getBusinessNo()
          .equals(other.getBusinessNo())) return false;
      if (!getAllBetList()
          .equals(other.getAllBetList())) return false;
      if (!getHighRollersList()
          .equals(other.getHighRollersList())) return false;
      if (!getLuckyBetList()
          .equals(other.getLuckyBetList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + MSGID_FIELD_NUMBER;
      hash = (53 * hash) + getMsgID();
      hash = (37 * hash) + BUSINESS_NO_FIELD_NUMBER;
      hash = (53 * hash) + getBusinessNo().hashCode();
      if (getAllBetCount() > 0) {
        hash = (37 * hash) + ALLBET_FIELD_NUMBER;
        hash = (53 * hash) + getAllBetList().hashCode();
      }
      if (getHighRollersCount() > 0) {
        hash = (37 * hash) + HIGHROLLERS_FIELD_NUMBER;
        hash = (53 * hash) + getHighRollersList().hashCode();
      }
      if (getLuckyBetCount() > 0) {
        hash = (37 * hash) + LUCKYBET_FIELD_NUMBER;
        hash = (53 * hash) + getLuckyBetList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.proto.TcpMessage.ResTcpGameNoteDataMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ResTcpGameNoteDataMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpGameNoteDataMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ResTcpGameNoteDataMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpGameNoteDataMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ResTcpGameNoteDataMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpGameNoteDataMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ResTcpGameNoteDataMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpGameNoteDataMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ResTcpGameNoteDataMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpGameNoteDataMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ResTcpGameNoteDataMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.proto.TcpMessage.ResTcpGameNoteDataMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *返回游戏下注
     * </pre>
     *
     * Protobuf type {@code ProtoMessage.ResTcpGameNoteDataMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ProtoMessage.ResTcpGameNoteDataMessage)
        com.proto.TcpMessage.ResTcpGameNoteDataMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpGameNoteDataMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpGameNoteDataMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.proto.TcpMessage.ResTcpGameNoteDataMessage.class, com.proto.TcpMessage.ResTcpGameNoteDataMessage.Builder.class);
      }

      // Construct using com.proto.TcpMessage.ResTcpGameNoteDataMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getAllBetFieldBuilder();
          getHighRollersFieldBuilder();
          getLuckyBetFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        msgID_ = 0;

        businessNo_ = "";

        if (allBetBuilder_ == null) {
          allBet_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          allBetBuilder_.clear();
        }
        if (highRollersBuilder_ == null) {
          highRollers_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
        } else {
          highRollersBuilder_.clear();
        }
        if (luckyBetBuilder_ == null) {
          luckyBet_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
        } else {
          luckyBetBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpGameNoteDataMessage_descriptor;
      }

      @java.lang.Override
      public com.proto.TcpMessage.ResTcpGameNoteDataMessage getDefaultInstanceForType() {
        return com.proto.TcpMessage.ResTcpGameNoteDataMessage.getDefaultInstance();
      }

      @java.lang.Override
      public com.proto.TcpMessage.ResTcpGameNoteDataMessage build() {
        com.proto.TcpMessage.ResTcpGameNoteDataMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.proto.TcpMessage.ResTcpGameNoteDataMessage buildPartial() {
        com.proto.TcpMessage.ResTcpGameNoteDataMessage result = new com.proto.TcpMessage.ResTcpGameNoteDataMessage(this);
        int from_bitField0_ = bitField0_;
        result.msgID_ = msgID_;
        result.businessNo_ = businessNo_;
        if (allBetBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            allBet_ = java.util.Collections.unmodifiableList(allBet_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.allBet_ = allBet_;
        } else {
          result.allBet_ = allBetBuilder_.build();
        }
        if (highRollersBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0)) {
            highRollers_ = java.util.Collections.unmodifiableList(highRollers_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.highRollers_ = highRollers_;
        } else {
          result.highRollers_ = highRollersBuilder_.build();
        }
        if (luckyBetBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0)) {
            luckyBet_ = java.util.Collections.unmodifiableList(luckyBet_);
            bitField0_ = (bitField0_ & ~0x00000004);
          }
          result.luckyBet_ = luckyBet_;
        } else {
          result.luckyBet_ = luckyBetBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.proto.TcpMessage.ResTcpGameNoteDataMessage) {
          return mergeFrom((com.proto.TcpMessage.ResTcpGameNoteDataMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.proto.TcpMessage.ResTcpGameNoteDataMessage other) {
        if (other == com.proto.TcpMessage.ResTcpGameNoteDataMessage.getDefaultInstance()) return this;
        if (other.getMsgID() != 0) {
          setMsgID(other.getMsgID());
        }
        if (!other.getBusinessNo().isEmpty()) {
          businessNo_ = other.businessNo_;
          onChanged();
        }
        if (allBetBuilder_ == null) {
          if (!other.allBet_.isEmpty()) {
            if (allBet_.isEmpty()) {
              allBet_ = other.allBet_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureAllBetIsMutable();
              allBet_.addAll(other.allBet_);
            }
            onChanged();
          }
        } else {
          if (!other.allBet_.isEmpty()) {
            if (allBetBuilder_.isEmpty()) {
              allBetBuilder_.dispose();
              allBetBuilder_ = null;
              allBet_ = other.allBet_;
              bitField0_ = (bitField0_ & ~0x00000001);
              allBetBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getAllBetFieldBuilder() : null;
            } else {
              allBetBuilder_.addAllMessages(other.allBet_);
            }
          }
        }
        if (highRollersBuilder_ == null) {
          if (!other.highRollers_.isEmpty()) {
            if (highRollers_.isEmpty()) {
              highRollers_ = other.highRollers_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureHighRollersIsMutable();
              highRollers_.addAll(other.highRollers_);
            }
            onChanged();
          }
        } else {
          if (!other.highRollers_.isEmpty()) {
            if (highRollersBuilder_.isEmpty()) {
              highRollersBuilder_.dispose();
              highRollersBuilder_ = null;
              highRollers_ = other.highRollers_;
              bitField0_ = (bitField0_ & ~0x00000002);
              highRollersBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getHighRollersFieldBuilder() : null;
            } else {
              highRollersBuilder_.addAllMessages(other.highRollers_);
            }
          }
        }
        if (luckyBetBuilder_ == null) {
          if (!other.luckyBet_.isEmpty()) {
            if (luckyBet_.isEmpty()) {
              luckyBet_ = other.luckyBet_;
              bitField0_ = (bitField0_ & ~0x00000004);
            } else {
              ensureLuckyBetIsMutable();
              luckyBet_.addAll(other.luckyBet_);
            }
            onChanged();
          }
        } else {
          if (!other.luckyBet_.isEmpty()) {
            if (luckyBetBuilder_.isEmpty()) {
              luckyBetBuilder_.dispose();
              luckyBetBuilder_ = null;
              luckyBet_ = other.luckyBet_;
              bitField0_ = (bitField0_ & ~0x00000004);
              luckyBetBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getLuckyBetFieldBuilder() : null;
            } else {
              luckyBetBuilder_.addAllMessages(other.luckyBet_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.proto.TcpMessage.ResTcpGameNoteDataMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.proto.TcpMessage.ResTcpGameNoteDataMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int msgID_ ;
      /**
       * <code>int32 msgID = 1;</code>
       * @return The msgID.
       */
      @java.lang.Override
      public int getMsgID() {
        return msgID_;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @param value The msgID to set.
       * @return This builder for chaining.
       */
      public Builder setMsgID(int value) {
        
        msgID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgID() {
        
        msgID_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object businessNo_ = "";
      /**
       * <pre>
       *商户（服务器使用）
       * </pre>
       *
       * <code>string business_no = 2;</code>
       * @return The businessNo.
       */
      public java.lang.String getBusinessNo() {
        java.lang.Object ref = businessNo_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          businessNo_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *商户（服务器使用）
       * </pre>
       *
       * <code>string business_no = 2;</code>
       * @return The bytes for businessNo.
       */
      public com.google.protobuf.ByteString
          getBusinessNoBytes() {
        java.lang.Object ref = businessNo_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          businessNo_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *商户（服务器使用）
       * </pre>
       *
       * <code>string business_no = 2;</code>
       * @param value The businessNo to set.
       * @return This builder for chaining.
       */
      public Builder setBusinessNo(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        businessNo_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *商户（服务器使用）
       * </pre>
       *
       * <code>string business_no = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearBusinessNo() {
        
        businessNo_ = getDefaultInstance().getBusinessNo();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *商户（服务器使用）
       * </pre>
       *
       * <code>string business_no = 2;</code>
       * @param value The bytes for businessNo to set.
       * @return This builder for chaining.
       */
      public Builder setBusinessNoBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        businessNo_ = value;
        onChanged();
        return this;
      }

      private java.util.List<com.proto.CommonMessage.BetInfo> allBet_ =
        java.util.Collections.emptyList();
      private void ensureAllBetIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          allBet_ = new java.util.ArrayList<com.proto.CommonMessage.BetInfo>(allBet_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.proto.CommonMessage.BetInfo, com.proto.CommonMessage.BetInfo.Builder, com.proto.CommonMessage.BetInfoOrBuilder> allBetBuilder_;

      /**
       * <pre>
       *下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo allBet = 3;</code>
       */
      public java.util.List<com.proto.CommonMessage.BetInfo> getAllBetList() {
        if (allBetBuilder_ == null) {
          return java.util.Collections.unmodifiableList(allBet_);
        } else {
          return allBetBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       *下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo allBet = 3;</code>
       */
      public int getAllBetCount() {
        if (allBetBuilder_ == null) {
          return allBet_.size();
        } else {
          return allBetBuilder_.getCount();
        }
      }
      /**
       * <pre>
       *下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo allBet = 3;</code>
       */
      public com.proto.CommonMessage.BetInfo getAllBet(int index) {
        if (allBetBuilder_ == null) {
          return allBet_.get(index);
        } else {
          return allBetBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       *下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo allBet = 3;</code>
       */
      public Builder setAllBet(
          int index, com.proto.CommonMessage.BetInfo value) {
        if (allBetBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureAllBetIsMutable();
          allBet_.set(index, value);
          onChanged();
        } else {
          allBetBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo allBet = 3;</code>
       */
      public Builder setAllBet(
          int index, com.proto.CommonMessage.BetInfo.Builder builderForValue) {
        if (allBetBuilder_ == null) {
          ensureAllBetIsMutable();
          allBet_.set(index, builderForValue.build());
          onChanged();
        } else {
          allBetBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo allBet = 3;</code>
       */
      public Builder addAllBet(com.proto.CommonMessage.BetInfo value) {
        if (allBetBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureAllBetIsMutable();
          allBet_.add(value);
          onChanged();
        } else {
          allBetBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       *下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo allBet = 3;</code>
       */
      public Builder addAllBet(
          int index, com.proto.CommonMessage.BetInfo value) {
        if (allBetBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureAllBetIsMutable();
          allBet_.add(index, value);
          onChanged();
        } else {
          allBetBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo allBet = 3;</code>
       */
      public Builder addAllBet(
          com.proto.CommonMessage.BetInfo.Builder builderForValue) {
        if (allBetBuilder_ == null) {
          ensureAllBetIsMutable();
          allBet_.add(builderForValue.build());
          onChanged();
        } else {
          allBetBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo allBet = 3;</code>
       */
      public Builder addAllBet(
          int index, com.proto.CommonMessage.BetInfo.Builder builderForValue) {
        if (allBetBuilder_ == null) {
          ensureAllBetIsMutable();
          allBet_.add(index, builderForValue.build());
          onChanged();
        } else {
          allBetBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo allBet = 3;</code>
       */
      public Builder addAllAllBet(
          java.lang.Iterable<? extends com.proto.CommonMessage.BetInfo> values) {
        if (allBetBuilder_ == null) {
          ensureAllBetIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, allBet_);
          onChanged();
        } else {
          allBetBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       *下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo allBet = 3;</code>
       */
      public Builder clearAllBet() {
        if (allBetBuilder_ == null) {
          allBet_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          allBetBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       *下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo allBet = 3;</code>
       */
      public Builder removeAllBet(int index) {
        if (allBetBuilder_ == null) {
          ensureAllBetIsMutable();
          allBet_.remove(index);
          onChanged();
        } else {
          allBetBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       *下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo allBet = 3;</code>
       */
      public com.proto.CommonMessage.BetInfo.Builder getAllBetBuilder(
          int index) {
        return getAllBetFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       *下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo allBet = 3;</code>
       */
      public com.proto.CommonMessage.BetInfoOrBuilder getAllBetOrBuilder(
          int index) {
        if (allBetBuilder_ == null) {
          return allBet_.get(index);  } else {
          return allBetBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       *下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo allBet = 3;</code>
       */
      public java.util.List<? extends com.proto.CommonMessage.BetInfoOrBuilder> 
           getAllBetOrBuilderList() {
        if (allBetBuilder_ != null) {
          return allBetBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(allBet_);
        }
      }
      /**
       * <pre>
       *下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo allBet = 3;</code>
       */
      public com.proto.CommonMessage.BetInfo.Builder addAllBetBuilder() {
        return getAllBetFieldBuilder().addBuilder(
            com.proto.CommonMessage.BetInfo.getDefaultInstance());
      }
      /**
       * <pre>
       *下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo allBet = 3;</code>
       */
      public com.proto.CommonMessage.BetInfo.Builder addAllBetBuilder(
          int index) {
        return getAllBetFieldBuilder().addBuilder(
            index, com.proto.CommonMessage.BetInfo.getDefaultInstance());
      }
      /**
       * <pre>
       *下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo allBet = 3;</code>
       */
      public java.util.List<com.proto.CommonMessage.BetInfo.Builder> 
           getAllBetBuilderList() {
        return getAllBetFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.proto.CommonMessage.BetInfo, com.proto.CommonMessage.BetInfo.Builder, com.proto.CommonMessage.BetInfoOrBuilder> 
          getAllBetFieldBuilder() {
        if (allBetBuilder_ == null) {
          allBetBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.proto.CommonMessage.BetInfo, com.proto.CommonMessage.BetInfo.Builder, com.proto.CommonMessage.BetInfoOrBuilder>(
                  allBet_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          allBet_ = null;
        }
        return allBetBuilder_;
      }

      private java.util.List<com.proto.CommonMessage.BetInfo> highRollers_ =
        java.util.Collections.emptyList();
      private void ensureHighRollersIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          highRollers_ = new java.util.ArrayList<com.proto.CommonMessage.BetInfo>(highRollers_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.proto.CommonMessage.BetInfo, com.proto.CommonMessage.BetInfo.Builder, com.proto.CommonMessage.BetInfoOrBuilder> highRollersBuilder_;

      /**
       * <pre>
       *高手
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo highRollers = 4;</code>
       */
      public java.util.List<com.proto.CommonMessage.BetInfo> getHighRollersList() {
        if (highRollersBuilder_ == null) {
          return java.util.Collections.unmodifiableList(highRollers_);
        } else {
          return highRollersBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       *高手
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo highRollers = 4;</code>
       */
      public int getHighRollersCount() {
        if (highRollersBuilder_ == null) {
          return highRollers_.size();
        } else {
          return highRollersBuilder_.getCount();
        }
      }
      /**
       * <pre>
       *高手
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo highRollers = 4;</code>
       */
      public com.proto.CommonMessage.BetInfo getHighRollers(int index) {
        if (highRollersBuilder_ == null) {
          return highRollers_.get(index);
        } else {
          return highRollersBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       *高手
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo highRollers = 4;</code>
       */
      public Builder setHighRollers(
          int index, com.proto.CommonMessage.BetInfo value) {
        if (highRollersBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureHighRollersIsMutable();
          highRollers_.set(index, value);
          onChanged();
        } else {
          highRollersBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *高手
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo highRollers = 4;</code>
       */
      public Builder setHighRollers(
          int index, com.proto.CommonMessage.BetInfo.Builder builderForValue) {
        if (highRollersBuilder_ == null) {
          ensureHighRollersIsMutable();
          highRollers_.set(index, builderForValue.build());
          onChanged();
        } else {
          highRollersBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *高手
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo highRollers = 4;</code>
       */
      public Builder addHighRollers(com.proto.CommonMessage.BetInfo value) {
        if (highRollersBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureHighRollersIsMutable();
          highRollers_.add(value);
          onChanged();
        } else {
          highRollersBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       *高手
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo highRollers = 4;</code>
       */
      public Builder addHighRollers(
          int index, com.proto.CommonMessage.BetInfo value) {
        if (highRollersBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureHighRollersIsMutable();
          highRollers_.add(index, value);
          onChanged();
        } else {
          highRollersBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *高手
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo highRollers = 4;</code>
       */
      public Builder addHighRollers(
          com.proto.CommonMessage.BetInfo.Builder builderForValue) {
        if (highRollersBuilder_ == null) {
          ensureHighRollersIsMutable();
          highRollers_.add(builderForValue.build());
          onChanged();
        } else {
          highRollersBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *高手
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo highRollers = 4;</code>
       */
      public Builder addHighRollers(
          int index, com.proto.CommonMessage.BetInfo.Builder builderForValue) {
        if (highRollersBuilder_ == null) {
          ensureHighRollersIsMutable();
          highRollers_.add(index, builderForValue.build());
          onChanged();
        } else {
          highRollersBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *高手
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo highRollers = 4;</code>
       */
      public Builder addAllHighRollers(
          java.lang.Iterable<? extends com.proto.CommonMessage.BetInfo> values) {
        if (highRollersBuilder_ == null) {
          ensureHighRollersIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, highRollers_);
          onChanged();
        } else {
          highRollersBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       *高手
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo highRollers = 4;</code>
       */
      public Builder clearHighRollers() {
        if (highRollersBuilder_ == null) {
          highRollers_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          highRollersBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       *高手
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo highRollers = 4;</code>
       */
      public Builder removeHighRollers(int index) {
        if (highRollersBuilder_ == null) {
          ensureHighRollersIsMutable();
          highRollers_.remove(index);
          onChanged();
        } else {
          highRollersBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       *高手
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo highRollers = 4;</code>
       */
      public com.proto.CommonMessage.BetInfo.Builder getHighRollersBuilder(
          int index) {
        return getHighRollersFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       *高手
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo highRollers = 4;</code>
       */
      public com.proto.CommonMessage.BetInfoOrBuilder getHighRollersOrBuilder(
          int index) {
        if (highRollersBuilder_ == null) {
          return highRollers_.get(index);  } else {
          return highRollersBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       *高手
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo highRollers = 4;</code>
       */
      public java.util.List<? extends com.proto.CommonMessage.BetInfoOrBuilder> 
           getHighRollersOrBuilderList() {
        if (highRollersBuilder_ != null) {
          return highRollersBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(highRollers_);
        }
      }
      /**
       * <pre>
       *高手
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo highRollers = 4;</code>
       */
      public com.proto.CommonMessage.BetInfo.Builder addHighRollersBuilder() {
        return getHighRollersFieldBuilder().addBuilder(
            com.proto.CommonMessage.BetInfo.getDefaultInstance());
      }
      /**
       * <pre>
       *高手
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo highRollers = 4;</code>
       */
      public com.proto.CommonMessage.BetInfo.Builder addHighRollersBuilder(
          int index) {
        return getHighRollersFieldBuilder().addBuilder(
            index, com.proto.CommonMessage.BetInfo.getDefaultInstance());
      }
      /**
       * <pre>
       *高手
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo highRollers = 4;</code>
       */
      public java.util.List<com.proto.CommonMessage.BetInfo.Builder> 
           getHighRollersBuilderList() {
        return getHighRollersFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.proto.CommonMessage.BetInfo, com.proto.CommonMessage.BetInfo.Builder, com.proto.CommonMessage.BetInfoOrBuilder> 
          getHighRollersFieldBuilder() {
        if (highRollersBuilder_ == null) {
          highRollersBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.proto.CommonMessage.BetInfo, com.proto.CommonMessage.BetInfo.Builder, com.proto.CommonMessage.BetInfoOrBuilder>(
                  highRollers_,
                  ((bitField0_ & 0x00000002) != 0),
                  getParentForChildren(),
                  isClean());
          highRollers_ = null;
        }
        return highRollersBuilder_;
      }

      private java.util.List<com.proto.CommonMessage.BetInfo> luckyBet_ =
        java.util.Collections.emptyList();
      private void ensureLuckyBetIsMutable() {
        if (!((bitField0_ & 0x00000004) != 0)) {
          luckyBet_ = new java.util.ArrayList<com.proto.CommonMessage.BetInfo>(luckyBet_);
          bitField0_ |= 0x00000004;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.proto.CommonMessage.BetInfo, com.proto.CommonMessage.BetInfo.Builder, com.proto.CommonMessage.BetInfoOrBuilder> luckyBetBuilder_;

      /**
       * <pre>
       *幸运
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo luckyBet = 5;</code>
       */
      public java.util.List<com.proto.CommonMessage.BetInfo> getLuckyBetList() {
        if (luckyBetBuilder_ == null) {
          return java.util.Collections.unmodifiableList(luckyBet_);
        } else {
          return luckyBetBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       *幸运
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo luckyBet = 5;</code>
       */
      public int getLuckyBetCount() {
        if (luckyBetBuilder_ == null) {
          return luckyBet_.size();
        } else {
          return luckyBetBuilder_.getCount();
        }
      }
      /**
       * <pre>
       *幸运
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo luckyBet = 5;</code>
       */
      public com.proto.CommonMessage.BetInfo getLuckyBet(int index) {
        if (luckyBetBuilder_ == null) {
          return luckyBet_.get(index);
        } else {
          return luckyBetBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       *幸运
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo luckyBet = 5;</code>
       */
      public Builder setLuckyBet(
          int index, com.proto.CommonMessage.BetInfo value) {
        if (luckyBetBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureLuckyBetIsMutable();
          luckyBet_.set(index, value);
          onChanged();
        } else {
          luckyBetBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *幸运
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo luckyBet = 5;</code>
       */
      public Builder setLuckyBet(
          int index, com.proto.CommonMessage.BetInfo.Builder builderForValue) {
        if (luckyBetBuilder_ == null) {
          ensureLuckyBetIsMutable();
          luckyBet_.set(index, builderForValue.build());
          onChanged();
        } else {
          luckyBetBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *幸运
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo luckyBet = 5;</code>
       */
      public Builder addLuckyBet(com.proto.CommonMessage.BetInfo value) {
        if (luckyBetBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureLuckyBetIsMutable();
          luckyBet_.add(value);
          onChanged();
        } else {
          luckyBetBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       *幸运
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo luckyBet = 5;</code>
       */
      public Builder addLuckyBet(
          int index, com.proto.CommonMessage.BetInfo value) {
        if (luckyBetBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureLuckyBetIsMutable();
          luckyBet_.add(index, value);
          onChanged();
        } else {
          luckyBetBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *幸运
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo luckyBet = 5;</code>
       */
      public Builder addLuckyBet(
          com.proto.CommonMessage.BetInfo.Builder builderForValue) {
        if (luckyBetBuilder_ == null) {
          ensureLuckyBetIsMutable();
          luckyBet_.add(builderForValue.build());
          onChanged();
        } else {
          luckyBetBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *幸运
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo luckyBet = 5;</code>
       */
      public Builder addLuckyBet(
          int index, com.proto.CommonMessage.BetInfo.Builder builderForValue) {
        if (luckyBetBuilder_ == null) {
          ensureLuckyBetIsMutable();
          luckyBet_.add(index, builderForValue.build());
          onChanged();
        } else {
          luckyBetBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *幸运
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo luckyBet = 5;</code>
       */
      public Builder addAllLuckyBet(
          java.lang.Iterable<? extends com.proto.CommonMessage.BetInfo> values) {
        if (luckyBetBuilder_ == null) {
          ensureLuckyBetIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, luckyBet_);
          onChanged();
        } else {
          luckyBetBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       *幸运
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo luckyBet = 5;</code>
       */
      public Builder clearLuckyBet() {
        if (luckyBetBuilder_ == null) {
          luckyBet_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
          onChanged();
        } else {
          luckyBetBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       *幸运
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo luckyBet = 5;</code>
       */
      public Builder removeLuckyBet(int index) {
        if (luckyBetBuilder_ == null) {
          ensureLuckyBetIsMutable();
          luckyBet_.remove(index);
          onChanged();
        } else {
          luckyBetBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       *幸运
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo luckyBet = 5;</code>
       */
      public com.proto.CommonMessage.BetInfo.Builder getLuckyBetBuilder(
          int index) {
        return getLuckyBetFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       *幸运
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo luckyBet = 5;</code>
       */
      public com.proto.CommonMessage.BetInfoOrBuilder getLuckyBetOrBuilder(
          int index) {
        if (luckyBetBuilder_ == null) {
          return luckyBet_.get(index);  } else {
          return luckyBetBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       *幸运
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo luckyBet = 5;</code>
       */
      public java.util.List<? extends com.proto.CommonMessage.BetInfoOrBuilder> 
           getLuckyBetOrBuilderList() {
        if (luckyBetBuilder_ != null) {
          return luckyBetBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(luckyBet_);
        }
      }
      /**
       * <pre>
       *幸运
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo luckyBet = 5;</code>
       */
      public com.proto.CommonMessage.BetInfo.Builder addLuckyBetBuilder() {
        return getLuckyBetFieldBuilder().addBuilder(
            com.proto.CommonMessage.BetInfo.getDefaultInstance());
      }
      /**
       * <pre>
       *幸运
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo luckyBet = 5;</code>
       */
      public com.proto.CommonMessage.BetInfo.Builder addLuckyBetBuilder(
          int index) {
        return getLuckyBetFieldBuilder().addBuilder(
            index, com.proto.CommonMessage.BetInfo.getDefaultInstance());
      }
      /**
       * <pre>
       *幸运
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo luckyBet = 5;</code>
       */
      public java.util.List<com.proto.CommonMessage.BetInfo.Builder> 
           getLuckyBetBuilderList() {
        return getLuckyBetFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.proto.CommonMessage.BetInfo, com.proto.CommonMessage.BetInfo.Builder, com.proto.CommonMessage.BetInfoOrBuilder> 
          getLuckyBetFieldBuilder() {
        if (luckyBetBuilder_ == null) {
          luckyBetBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.proto.CommonMessage.BetInfo, com.proto.CommonMessage.BetInfo.Builder, com.proto.CommonMessage.BetInfoOrBuilder>(
                  luckyBet_,
                  ((bitField0_ & 0x00000004) != 0),
                  getParentForChildren(),
                  isClean());
          luckyBet_ = null;
        }
        return luckyBetBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ProtoMessage.ResTcpGameNoteDataMessage)
    }

    // @@protoc_insertion_point(class_scope:ProtoMessage.ResTcpGameNoteDataMessage)
    private static final com.proto.TcpMessage.ResTcpGameNoteDataMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.proto.TcpMessage.ResTcpGameNoteDataMessage();
    }

    public static com.proto.TcpMessage.ResTcpGameNoteDataMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResTcpGameNoteDataMessage>
        PARSER = new com.google.protobuf.AbstractParser<ResTcpGameNoteDataMessage>() {
      @java.lang.Override
      public ResTcpGameNoteDataMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResTcpGameNoteDataMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResTcpGameNoteDataMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResTcpGameNoteDataMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.proto.TcpMessage.ResTcpGameNoteDataMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResTcpMyBetDataMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ProtoMessage.ResTcpMyBetDataMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    int getMsgID();

    /**
     * <pre>
     *我的下注列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo myBetList = 2;</code>
     */
    java.util.List<com.proto.CommonMessage.BetInfo> 
        getMyBetListList();
    /**
     * <pre>
     *我的下注列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo myBetList = 2;</code>
     */
    com.proto.CommonMessage.BetInfo getMyBetList(int index);
    /**
     * <pre>
     *我的下注列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo myBetList = 2;</code>
     */
    int getMyBetListCount();
    /**
     * <pre>
     *我的下注列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo myBetList = 2;</code>
     */
    java.util.List<? extends com.proto.CommonMessage.BetInfoOrBuilder> 
        getMyBetListOrBuilderList();
    /**
     * <pre>
     *我的下注列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo myBetList = 2;</code>
     */
    com.proto.CommonMessage.BetInfoOrBuilder getMyBetListOrBuilder(
        int index);
  }
  /**
   * <pre>
   *返回myBet
   * </pre>
   *
   * Protobuf type {@code ProtoMessage.ResTcpMyBetDataMessage}
   */
  public static final class ResTcpMyBetDataMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ProtoMessage.ResTcpMyBetDataMessage)
      ResTcpMyBetDataMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResTcpMyBetDataMessage.newBuilder() to construct.
    private ResTcpMyBetDataMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResTcpMyBetDataMessage() {
      myBetList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResTcpMyBetDataMessage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResTcpMyBetDataMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              msgID_ = input.readInt32();
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                myBetList_ = new java.util.ArrayList<com.proto.CommonMessage.BetInfo>();
                mutable_bitField0_ |= 0x00000001;
              }
              myBetList_.add(
                  input.readMessage(com.proto.CommonMessage.BetInfo.parser(), extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          myBetList_ = java.util.Collections.unmodifiableList(myBetList_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpMyBetDataMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpMyBetDataMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.proto.TcpMessage.ResTcpMyBetDataMessage.class, com.proto.TcpMessage.ResTcpMyBetDataMessage.Builder.class);
    }

    public static final int MSGID_FIELD_NUMBER = 1;
    private int msgID_;
    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    @java.lang.Override
    public int getMsgID() {
      return msgID_;
    }

    public static final int MYBETLIST_FIELD_NUMBER = 2;
    private java.util.List<com.proto.CommonMessage.BetInfo> myBetList_;
    /**
     * <pre>
     *我的下注列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo myBetList = 2;</code>
     */
    @java.lang.Override
    public java.util.List<com.proto.CommonMessage.BetInfo> getMyBetListList() {
      return myBetList_;
    }
    /**
     * <pre>
     *我的下注列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo myBetList = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.proto.CommonMessage.BetInfoOrBuilder> 
        getMyBetListOrBuilderList() {
      return myBetList_;
    }
    /**
     * <pre>
     *我的下注列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo myBetList = 2;</code>
     */
    @java.lang.Override
    public int getMyBetListCount() {
      return myBetList_.size();
    }
    /**
     * <pre>
     *我的下注列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo myBetList = 2;</code>
     */
    @java.lang.Override
    public com.proto.CommonMessage.BetInfo getMyBetList(int index) {
      return myBetList_.get(index);
    }
    /**
     * <pre>
     *我的下注列表
     * </pre>
     *
     * <code>repeated .ProtoMessage.BetInfo myBetList = 2;</code>
     */
    @java.lang.Override
    public com.proto.CommonMessage.BetInfoOrBuilder getMyBetListOrBuilder(
        int index) {
      return myBetList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (msgID_ != 0) {
        output.writeInt32(1, msgID_);
      }
      for (int i = 0; i < myBetList_.size(); i++) {
        output.writeMessage(2, myBetList_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (msgID_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, msgID_);
      }
      for (int i = 0; i < myBetList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, myBetList_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.proto.TcpMessage.ResTcpMyBetDataMessage)) {
        return super.equals(obj);
      }
      com.proto.TcpMessage.ResTcpMyBetDataMessage other = (com.proto.TcpMessage.ResTcpMyBetDataMessage) obj;

      if (getMsgID()
          != other.getMsgID()) return false;
      if (!getMyBetListList()
          .equals(other.getMyBetListList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + MSGID_FIELD_NUMBER;
      hash = (53 * hash) + getMsgID();
      if (getMyBetListCount() > 0) {
        hash = (37 * hash) + MYBETLIST_FIELD_NUMBER;
        hash = (53 * hash) + getMyBetListList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.proto.TcpMessage.ResTcpMyBetDataMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ResTcpMyBetDataMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpMyBetDataMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ResTcpMyBetDataMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpMyBetDataMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ResTcpMyBetDataMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpMyBetDataMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ResTcpMyBetDataMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpMyBetDataMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ResTcpMyBetDataMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpMyBetDataMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ResTcpMyBetDataMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.proto.TcpMessage.ResTcpMyBetDataMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *返回myBet
     * </pre>
     *
     * Protobuf type {@code ProtoMessage.ResTcpMyBetDataMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ProtoMessage.ResTcpMyBetDataMessage)
        com.proto.TcpMessage.ResTcpMyBetDataMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpMyBetDataMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpMyBetDataMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.proto.TcpMessage.ResTcpMyBetDataMessage.class, com.proto.TcpMessage.ResTcpMyBetDataMessage.Builder.class);
      }

      // Construct using com.proto.TcpMessage.ResTcpMyBetDataMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getMyBetListFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        msgID_ = 0;

        if (myBetListBuilder_ == null) {
          myBetList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          myBetListBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpMyBetDataMessage_descriptor;
      }

      @java.lang.Override
      public com.proto.TcpMessage.ResTcpMyBetDataMessage getDefaultInstanceForType() {
        return com.proto.TcpMessage.ResTcpMyBetDataMessage.getDefaultInstance();
      }

      @java.lang.Override
      public com.proto.TcpMessage.ResTcpMyBetDataMessage build() {
        com.proto.TcpMessage.ResTcpMyBetDataMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.proto.TcpMessage.ResTcpMyBetDataMessage buildPartial() {
        com.proto.TcpMessage.ResTcpMyBetDataMessage result = new com.proto.TcpMessage.ResTcpMyBetDataMessage(this);
        int from_bitField0_ = bitField0_;
        result.msgID_ = msgID_;
        if (myBetListBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            myBetList_ = java.util.Collections.unmodifiableList(myBetList_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.myBetList_ = myBetList_;
        } else {
          result.myBetList_ = myBetListBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.proto.TcpMessage.ResTcpMyBetDataMessage) {
          return mergeFrom((com.proto.TcpMessage.ResTcpMyBetDataMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.proto.TcpMessage.ResTcpMyBetDataMessage other) {
        if (other == com.proto.TcpMessage.ResTcpMyBetDataMessage.getDefaultInstance()) return this;
        if (other.getMsgID() != 0) {
          setMsgID(other.getMsgID());
        }
        if (myBetListBuilder_ == null) {
          if (!other.myBetList_.isEmpty()) {
            if (myBetList_.isEmpty()) {
              myBetList_ = other.myBetList_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureMyBetListIsMutable();
              myBetList_.addAll(other.myBetList_);
            }
            onChanged();
          }
        } else {
          if (!other.myBetList_.isEmpty()) {
            if (myBetListBuilder_.isEmpty()) {
              myBetListBuilder_.dispose();
              myBetListBuilder_ = null;
              myBetList_ = other.myBetList_;
              bitField0_ = (bitField0_ & ~0x00000001);
              myBetListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getMyBetListFieldBuilder() : null;
            } else {
              myBetListBuilder_.addAllMessages(other.myBetList_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.proto.TcpMessage.ResTcpMyBetDataMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.proto.TcpMessage.ResTcpMyBetDataMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int msgID_ ;
      /**
       * <code>int32 msgID = 1;</code>
       * @return The msgID.
       */
      @java.lang.Override
      public int getMsgID() {
        return msgID_;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @param value The msgID to set.
       * @return This builder for chaining.
       */
      public Builder setMsgID(int value) {
        
        msgID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgID() {
        
        msgID_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.proto.CommonMessage.BetInfo> myBetList_ =
        java.util.Collections.emptyList();
      private void ensureMyBetListIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          myBetList_ = new java.util.ArrayList<com.proto.CommonMessage.BetInfo>(myBetList_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.proto.CommonMessage.BetInfo, com.proto.CommonMessage.BetInfo.Builder, com.proto.CommonMessage.BetInfoOrBuilder> myBetListBuilder_;

      /**
       * <pre>
       *我的下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo myBetList = 2;</code>
       */
      public java.util.List<com.proto.CommonMessage.BetInfo> getMyBetListList() {
        if (myBetListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(myBetList_);
        } else {
          return myBetListBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       *我的下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo myBetList = 2;</code>
       */
      public int getMyBetListCount() {
        if (myBetListBuilder_ == null) {
          return myBetList_.size();
        } else {
          return myBetListBuilder_.getCount();
        }
      }
      /**
       * <pre>
       *我的下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo myBetList = 2;</code>
       */
      public com.proto.CommonMessage.BetInfo getMyBetList(int index) {
        if (myBetListBuilder_ == null) {
          return myBetList_.get(index);
        } else {
          return myBetListBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       *我的下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo myBetList = 2;</code>
       */
      public Builder setMyBetList(
          int index, com.proto.CommonMessage.BetInfo value) {
        if (myBetListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureMyBetListIsMutable();
          myBetList_.set(index, value);
          onChanged();
        } else {
          myBetListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *我的下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo myBetList = 2;</code>
       */
      public Builder setMyBetList(
          int index, com.proto.CommonMessage.BetInfo.Builder builderForValue) {
        if (myBetListBuilder_ == null) {
          ensureMyBetListIsMutable();
          myBetList_.set(index, builderForValue.build());
          onChanged();
        } else {
          myBetListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *我的下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo myBetList = 2;</code>
       */
      public Builder addMyBetList(com.proto.CommonMessage.BetInfo value) {
        if (myBetListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureMyBetListIsMutable();
          myBetList_.add(value);
          onChanged();
        } else {
          myBetListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       *我的下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo myBetList = 2;</code>
       */
      public Builder addMyBetList(
          int index, com.proto.CommonMessage.BetInfo value) {
        if (myBetListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureMyBetListIsMutable();
          myBetList_.add(index, value);
          onChanged();
        } else {
          myBetListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *我的下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo myBetList = 2;</code>
       */
      public Builder addMyBetList(
          com.proto.CommonMessage.BetInfo.Builder builderForValue) {
        if (myBetListBuilder_ == null) {
          ensureMyBetListIsMutable();
          myBetList_.add(builderForValue.build());
          onChanged();
        } else {
          myBetListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *我的下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo myBetList = 2;</code>
       */
      public Builder addMyBetList(
          int index, com.proto.CommonMessage.BetInfo.Builder builderForValue) {
        if (myBetListBuilder_ == null) {
          ensureMyBetListIsMutable();
          myBetList_.add(index, builderForValue.build());
          onChanged();
        } else {
          myBetListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *我的下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo myBetList = 2;</code>
       */
      public Builder addAllMyBetList(
          java.lang.Iterable<? extends com.proto.CommonMessage.BetInfo> values) {
        if (myBetListBuilder_ == null) {
          ensureMyBetListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, myBetList_);
          onChanged();
        } else {
          myBetListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       *我的下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo myBetList = 2;</code>
       */
      public Builder clearMyBetList() {
        if (myBetListBuilder_ == null) {
          myBetList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          myBetListBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       *我的下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo myBetList = 2;</code>
       */
      public Builder removeMyBetList(int index) {
        if (myBetListBuilder_ == null) {
          ensureMyBetListIsMutable();
          myBetList_.remove(index);
          onChanged();
        } else {
          myBetListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       *我的下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo myBetList = 2;</code>
       */
      public com.proto.CommonMessage.BetInfo.Builder getMyBetListBuilder(
          int index) {
        return getMyBetListFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       *我的下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo myBetList = 2;</code>
       */
      public com.proto.CommonMessage.BetInfoOrBuilder getMyBetListOrBuilder(
          int index) {
        if (myBetListBuilder_ == null) {
          return myBetList_.get(index);  } else {
          return myBetListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       *我的下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo myBetList = 2;</code>
       */
      public java.util.List<? extends com.proto.CommonMessage.BetInfoOrBuilder> 
           getMyBetListOrBuilderList() {
        if (myBetListBuilder_ != null) {
          return myBetListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(myBetList_);
        }
      }
      /**
       * <pre>
       *我的下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo myBetList = 2;</code>
       */
      public com.proto.CommonMessage.BetInfo.Builder addMyBetListBuilder() {
        return getMyBetListFieldBuilder().addBuilder(
            com.proto.CommonMessage.BetInfo.getDefaultInstance());
      }
      /**
       * <pre>
       *我的下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo myBetList = 2;</code>
       */
      public com.proto.CommonMessage.BetInfo.Builder addMyBetListBuilder(
          int index) {
        return getMyBetListFieldBuilder().addBuilder(
            index, com.proto.CommonMessage.BetInfo.getDefaultInstance());
      }
      /**
       * <pre>
       *我的下注列表
       * </pre>
       *
       * <code>repeated .ProtoMessage.BetInfo myBetList = 2;</code>
       */
      public java.util.List<com.proto.CommonMessage.BetInfo.Builder> 
           getMyBetListBuilderList() {
        return getMyBetListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.proto.CommonMessage.BetInfo, com.proto.CommonMessage.BetInfo.Builder, com.proto.CommonMessage.BetInfoOrBuilder> 
          getMyBetListFieldBuilder() {
        if (myBetListBuilder_ == null) {
          myBetListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.proto.CommonMessage.BetInfo, com.proto.CommonMessage.BetInfo.Builder, com.proto.CommonMessage.BetInfoOrBuilder>(
                  myBetList_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          myBetList_ = null;
        }
        return myBetListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ProtoMessage.ResTcpMyBetDataMessage)
    }

    // @@protoc_insertion_point(class_scope:ProtoMessage.ResTcpMyBetDataMessage)
    private static final com.proto.TcpMessage.ResTcpMyBetDataMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.proto.TcpMessage.ResTcpMyBetDataMessage();
    }

    public static com.proto.TcpMessage.ResTcpMyBetDataMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResTcpMyBetDataMessage>
        PARSER = new com.google.protobuf.AbstractParser<ResTcpMyBetDataMessage>() {
      @java.lang.Override
      public ResTcpMyBetDataMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResTcpMyBetDataMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResTcpMyBetDataMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResTcpMyBetDataMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.proto.TcpMessage.ResTcpMyBetDataMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResTcpDailyContestPrizePoolMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ProtoMessage.ResTcpDailyContestPrizePoolMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    int getMsgID();

    /**
     * <pre>
     *商户（服务器使用）
     * </pre>
     *
     * <code>string business_no = 2;</code>
     * @return The businessNo.
     */
    java.lang.String getBusinessNo();
    /**
     * <pre>
     *商户（服务器使用）
     * </pre>
     *
     * <code>string business_no = 2;</code>
     * @return The bytes for businessNo.
     */
    com.google.protobuf.ByteString
        getBusinessNoBytes();

    /**
     * <pre>
     *奖池（usd）
     * </pre>
     *
     * <code>double prizePool = 3;</code>
     * @return The prizePool.
     */
    double getPrizePool();
  }
  /**
   * <pre>
   *返回每日竞赛奖池
   * </pre>
   *
   * Protobuf type {@code ProtoMessage.ResTcpDailyContestPrizePoolMessage}
   */
  public static final class ResTcpDailyContestPrizePoolMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ProtoMessage.ResTcpDailyContestPrizePoolMessage)
      ResTcpDailyContestPrizePoolMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResTcpDailyContestPrizePoolMessage.newBuilder() to construct.
    private ResTcpDailyContestPrizePoolMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResTcpDailyContestPrizePoolMessage() {
      businessNo_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResTcpDailyContestPrizePoolMessage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResTcpDailyContestPrizePoolMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              msgID_ = input.readInt32();
              break;
            }
            case 18: {
              java.lang.String s = input.readStringRequireUtf8();

              businessNo_ = s;
              break;
            }
            case 25: {

              prizePool_ = input.readDouble();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpDailyContestPrizePoolMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpDailyContestPrizePoolMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.proto.TcpMessage.ResTcpDailyContestPrizePoolMessage.class, com.proto.TcpMessage.ResTcpDailyContestPrizePoolMessage.Builder.class);
    }

    public static final int MSGID_FIELD_NUMBER = 1;
    private int msgID_;
    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    @java.lang.Override
    public int getMsgID() {
      return msgID_;
    }

    public static final int BUSINESS_NO_FIELD_NUMBER = 2;
    private volatile java.lang.Object businessNo_;
    /**
     * <pre>
     *商户（服务器使用）
     * </pre>
     *
     * <code>string business_no = 2;</code>
     * @return The businessNo.
     */
    @java.lang.Override
    public java.lang.String getBusinessNo() {
      java.lang.Object ref = businessNo_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        businessNo_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *商户（服务器使用）
     * </pre>
     *
     * <code>string business_no = 2;</code>
     * @return The bytes for businessNo.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getBusinessNoBytes() {
      java.lang.Object ref = businessNo_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        businessNo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int PRIZEPOOL_FIELD_NUMBER = 3;
    private double prizePool_;
    /**
     * <pre>
     *奖池（usd）
     * </pre>
     *
     * <code>double prizePool = 3;</code>
     * @return The prizePool.
     */
    @java.lang.Override
    public double getPrizePool() {
      return prizePool_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (msgID_ != 0) {
        output.writeInt32(1, msgID_);
      }
      if (!getBusinessNoBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, businessNo_);
      }
      if (prizePool_ != 0D) {
        output.writeDouble(3, prizePool_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (msgID_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, msgID_);
      }
      if (!getBusinessNoBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, businessNo_);
      }
      if (prizePool_ != 0D) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(3, prizePool_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.proto.TcpMessage.ResTcpDailyContestPrizePoolMessage)) {
        return super.equals(obj);
      }
      com.proto.TcpMessage.ResTcpDailyContestPrizePoolMessage other = (com.proto.TcpMessage.ResTcpDailyContestPrizePoolMessage) obj;

      if (getMsgID()
          != other.getMsgID()) return false;
      if (!getBusinessNo()
          .equals(other.getBusinessNo())) return false;
      if (java.lang.Double.doubleToLongBits(getPrizePool())
          != java.lang.Double.doubleToLongBits(
              other.getPrizePool())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + MSGID_FIELD_NUMBER;
      hash = (53 * hash) + getMsgID();
      hash = (37 * hash) + BUSINESS_NO_FIELD_NUMBER;
      hash = (53 * hash) + getBusinessNo().hashCode();
      hash = (37 * hash) + PRIZEPOOL_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          java.lang.Double.doubleToLongBits(getPrizePool()));
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.proto.TcpMessage.ResTcpDailyContestPrizePoolMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ResTcpDailyContestPrizePoolMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpDailyContestPrizePoolMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ResTcpDailyContestPrizePoolMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpDailyContestPrizePoolMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ResTcpDailyContestPrizePoolMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpDailyContestPrizePoolMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ResTcpDailyContestPrizePoolMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpDailyContestPrizePoolMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ResTcpDailyContestPrizePoolMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpDailyContestPrizePoolMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ResTcpDailyContestPrizePoolMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.proto.TcpMessage.ResTcpDailyContestPrizePoolMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *返回每日竞赛奖池
     * </pre>
     *
     * Protobuf type {@code ProtoMessage.ResTcpDailyContestPrizePoolMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ProtoMessage.ResTcpDailyContestPrizePoolMessage)
        com.proto.TcpMessage.ResTcpDailyContestPrizePoolMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpDailyContestPrizePoolMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpDailyContestPrizePoolMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.proto.TcpMessage.ResTcpDailyContestPrizePoolMessage.class, com.proto.TcpMessage.ResTcpDailyContestPrizePoolMessage.Builder.class);
      }

      // Construct using com.proto.TcpMessage.ResTcpDailyContestPrizePoolMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        msgID_ = 0;

        businessNo_ = "";

        prizePool_ = 0D;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpDailyContestPrizePoolMessage_descriptor;
      }

      @java.lang.Override
      public com.proto.TcpMessage.ResTcpDailyContestPrizePoolMessage getDefaultInstanceForType() {
        return com.proto.TcpMessage.ResTcpDailyContestPrizePoolMessage.getDefaultInstance();
      }

      @java.lang.Override
      public com.proto.TcpMessage.ResTcpDailyContestPrizePoolMessage build() {
        com.proto.TcpMessage.ResTcpDailyContestPrizePoolMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.proto.TcpMessage.ResTcpDailyContestPrizePoolMessage buildPartial() {
        com.proto.TcpMessage.ResTcpDailyContestPrizePoolMessage result = new com.proto.TcpMessage.ResTcpDailyContestPrizePoolMessage(this);
        result.msgID_ = msgID_;
        result.businessNo_ = businessNo_;
        result.prizePool_ = prizePool_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.proto.TcpMessage.ResTcpDailyContestPrizePoolMessage) {
          return mergeFrom((com.proto.TcpMessage.ResTcpDailyContestPrizePoolMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.proto.TcpMessage.ResTcpDailyContestPrizePoolMessage other) {
        if (other == com.proto.TcpMessage.ResTcpDailyContestPrizePoolMessage.getDefaultInstance()) return this;
        if (other.getMsgID() != 0) {
          setMsgID(other.getMsgID());
        }
        if (!other.getBusinessNo().isEmpty()) {
          businessNo_ = other.businessNo_;
          onChanged();
        }
        if (other.getPrizePool() != 0D) {
          setPrizePool(other.getPrizePool());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.proto.TcpMessage.ResTcpDailyContestPrizePoolMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.proto.TcpMessage.ResTcpDailyContestPrizePoolMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int msgID_ ;
      /**
       * <code>int32 msgID = 1;</code>
       * @return The msgID.
       */
      @java.lang.Override
      public int getMsgID() {
        return msgID_;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @param value The msgID to set.
       * @return This builder for chaining.
       */
      public Builder setMsgID(int value) {
        
        msgID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgID() {
        
        msgID_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object businessNo_ = "";
      /**
       * <pre>
       *商户（服务器使用）
       * </pre>
       *
       * <code>string business_no = 2;</code>
       * @return The businessNo.
       */
      public java.lang.String getBusinessNo() {
        java.lang.Object ref = businessNo_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          businessNo_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *商户（服务器使用）
       * </pre>
       *
       * <code>string business_no = 2;</code>
       * @return The bytes for businessNo.
       */
      public com.google.protobuf.ByteString
          getBusinessNoBytes() {
        java.lang.Object ref = businessNo_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          businessNo_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *商户（服务器使用）
       * </pre>
       *
       * <code>string business_no = 2;</code>
       * @param value The businessNo to set.
       * @return This builder for chaining.
       */
      public Builder setBusinessNo(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        businessNo_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *商户（服务器使用）
       * </pre>
       *
       * <code>string business_no = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearBusinessNo() {
        
        businessNo_ = getDefaultInstance().getBusinessNo();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *商户（服务器使用）
       * </pre>
       *
       * <code>string business_no = 2;</code>
       * @param value The bytes for businessNo to set.
       * @return This builder for chaining.
       */
      public Builder setBusinessNoBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        businessNo_ = value;
        onChanged();
        return this;
      }

      private double prizePool_ ;
      /**
       * <pre>
       *奖池（usd）
       * </pre>
       *
       * <code>double prizePool = 3;</code>
       * @return The prizePool.
       */
      @java.lang.Override
      public double getPrizePool() {
        return prizePool_;
      }
      /**
       * <pre>
       *奖池（usd）
       * </pre>
       *
       * <code>double prizePool = 3;</code>
       * @param value The prizePool to set.
       * @return This builder for chaining.
       */
      public Builder setPrizePool(double value) {
        
        prizePool_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *奖池（usd）
       * </pre>
       *
       * <code>double prizePool = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearPrizePool() {
        
        prizePool_ = 0D;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ProtoMessage.ResTcpDailyContestPrizePoolMessage)
    }

    // @@protoc_insertion_point(class_scope:ProtoMessage.ResTcpDailyContestPrizePoolMessage)
    private static final com.proto.TcpMessage.ResTcpDailyContestPrizePoolMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.proto.TcpMessage.ResTcpDailyContestPrizePoolMessage();
    }

    public static com.proto.TcpMessage.ResTcpDailyContestPrizePoolMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResTcpDailyContestPrizePoolMessage>
        PARSER = new com.google.protobuf.AbstractParser<ResTcpDailyContestPrizePoolMessage>() {
      @java.lang.Override
      public ResTcpDailyContestPrizePoolMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResTcpDailyContestPrizePoolMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResTcpDailyContestPrizePoolMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResTcpDailyContestPrizePoolMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.proto.TcpMessage.ResTcpDailyContestPrizePoolMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResTcpKickOutPlayerMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ProtoMessage.ResTcpKickOutPlayerMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    int getMsgID();

    /**
     * <code>int32 reason = 2;</code>
     * @return The reason.
     */
    int getReason();
  }
  /**
   * <pre>
   *返回踢出玩家
   * </pre>
   *
   * Protobuf type {@code ProtoMessage.ResTcpKickOutPlayerMessage}
   */
  public static final class ResTcpKickOutPlayerMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ProtoMessage.ResTcpKickOutPlayerMessage)
      ResTcpKickOutPlayerMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResTcpKickOutPlayerMessage.newBuilder() to construct.
    private ResTcpKickOutPlayerMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResTcpKickOutPlayerMessage() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResTcpKickOutPlayerMessage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResTcpKickOutPlayerMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              msgID_ = input.readInt32();
              break;
            }
            case 16: {

              reason_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpKickOutPlayerMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpKickOutPlayerMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.proto.TcpMessage.ResTcpKickOutPlayerMessage.class, com.proto.TcpMessage.ResTcpKickOutPlayerMessage.Builder.class);
    }

    public static final int MSGID_FIELD_NUMBER = 1;
    private int msgID_;
    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    @java.lang.Override
    public int getMsgID() {
      return msgID_;
    }

    public static final int REASON_FIELD_NUMBER = 2;
    private int reason_;
    /**
     * <code>int32 reason = 2;</code>
     * @return The reason.
     */
    @java.lang.Override
    public int getReason() {
      return reason_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (msgID_ != 0) {
        output.writeInt32(1, msgID_);
      }
      if (reason_ != 0) {
        output.writeInt32(2, reason_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (msgID_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, msgID_);
      }
      if (reason_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, reason_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.proto.TcpMessage.ResTcpKickOutPlayerMessage)) {
        return super.equals(obj);
      }
      com.proto.TcpMessage.ResTcpKickOutPlayerMessage other = (com.proto.TcpMessage.ResTcpKickOutPlayerMessage) obj;

      if (getMsgID()
          != other.getMsgID()) return false;
      if (getReason()
          != other.getReason()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + MSGID_FIELD_NUMBER;
      hash = (53 * hash) + getMsgID();
      hash = (37 * hash) + REASON_FIELD_NUMBER;
      hash = (53 * hash) + getReason();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.proto.TcpMessage.ResTcpKickOutPlayerMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ResTcpKickOutPlayerMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpKickOutPlayerMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ResTcpKickOutPlayerMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpKickOutPlayerMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ResTcpKickOutPlayerMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpKickOutPlayerMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ResTcpKickOutPlayerMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpKickOutPlayerMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ResTcpKickOutPlayerMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpKickOutPlayerMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ResTcpKickOutPlayerMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.proto.TcpMessage.ResTcpKickOutPlayerMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *返回踢出玩家
     * </pre>
     *
     * Protobuf type {@code ProtoMessage.ResTcpKickOutPlayerMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ProtoMessage.ResTcpKickOutPlayerMessage)
        com.proto.TcpMessage.ResTcpKickOutPlayerMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpKickOutPlayerMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpKickOutPlayerMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.proto.TcpMessage.ResTcpKickOutPlayerMessage.class, com.proto.TcpMessage.ResTcpKickOutPlayerMessage.Builder.class);
      }

      // Construct using com.proto.TcpMessage.ResTcpKickOutPlayerMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        msgID_ = 0;

        reason_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpKickOutPlayerMessage_descriptor;
      }

      @java.lang.Override
      public com.proto.TcpMessage.ResTcpKickOutPlayerMessage getDefaultInstanceForType() {
        return com.proto.TcpMessage.ResTcpKickOutPlayerMessage.getDefaultInstance();
      }

      @java.lang.Override
      public com.proto.TcpMessage.ResTcpKickOutPlayerMessage build() {
        com.proto.TcpMessage.ResTcpKickOutPlayerMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.proto.TcpMessage.ResTcpKickOutPlayerMessage buildPartial() {
        com.proto.TcpMessage.ResTcpKickOutPlayerMessage result = new com.proto.TcpMessage.ResTcpKickOutPlayerMessage(this);
        result.msgID_ = msgID_;
        result.reason_ = reason_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.proto.TcpMessage.ResTcpKickOutPlayerMessage) {
          return mergeFrom((com.proto.TcpMessage.ResTcpKickOutPlayerMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.proto.TcpMessage.ResTcpKickOutPlayerMessage other) {
        if (other == com.proto.TcpMessage.ResTcpKickOutPlayerMessage.getDefaultInstance()) return this;
        if (other.getMsgID() != 0) {
          setMsgID(other.getMsgID());
        }
        if (other.getReason() != 0) {
          setReason(other.getReason());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.proto.TcpMessage.ResTcpKickOutPlayerMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.proto.TcpMessage.ResTcpKickOutPlayerMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int msgID_ ;
      /**
       * <code>int32 msgID = 1;</code>
       * @return The msgID.
       */
      @java.lang.Override
      public int getMsgID() {
        return msgID_;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @param value The msgID to set.
       * @return This builder for chaining.
       */
      public Builder setMsgID(int value) {
        
        msgID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgID() {
        
        msgID_ = 0;
        onChanged();
        return this;
      }

      private int reason_ ;
      /**
       * <code>int32 reason = 2;</code>
       * @return The reason.
       */
      @java.lang.Override
      public int getReason() {
        return reason_;
      }
      /**
       * <code>int32 reason = 2;</code>
       * @param value The reason to set.
       * @return This builder for chaining.
       */
      public Builder setReason(int value) {
        
        reason_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 reason = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearReason() {
        
        reason_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ProtoMessage.ResTcpKickOutPlayerMessage)
    }

    // @@protoc_insertion_point(class_scope:ProtoMessage.ResTcpKickOutPlayerMessage)
    private static final com.proto.TcpMessage.ResTcpKickOutPlayerMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.proto.TcpMessage.ResTcpKickOutPlayerMessage();
    }

    public static com.proto.TcpMessage.ResTcpKickOutPlayerMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResTcpKickOutPlayerMessage>
        PARSER = new com.google.protobuf.AbstractParser<ResTcpKickOutPlayerMessage>() {
      @java.lang.Override
      public ResTcpKickOutPlayerMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResTcpKickOutPlayerMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResTcpKickOutPlayerMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResTcpKickOutPlayerMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.proto.TcpMessage.ResTcpKickOutPlayerMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResTcpRechargeSuccessMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ProtoMessage.ResTcpRechargeSuccessMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    int getMsgID();

    /**
     * <code>int64 playerId = 2;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <pre>
     *充值次数
     * </pre>
     *
     * <code>int32 rechargeTimes = 3;</code>
     * @return The rechargeTimes.
     */
    int getRechargeTimes();

    /**
     * <pre>
     *币种
     * </pre>
     *
     * <code>int32 currencyId = 4;</code>
     * @return The currencyId.
     */
    int getCurrencyId();

    /**
     * <pre>
     *当前金额
     * </pre>
     *
     * <code>double currRecharge = 5;</code>
     * @return The currRecharge.
     */
    double getCurrRecharge();

    /**
     * <pre>
     *总金额
     * </pre>
     *
     * <code>double totalRecharge = 6;</code>
     * @return The totalRecharge.
     */
    double getTotalRecharge();

    /**
     * <pre>
     *订单id
     * </pre>
     *
     * <code>int64 orderId = 7;</code>
     * @return The orderId.
     */
    long getOrderId();

    /**
     * <pre>
     *1.complete 2.processing 3.failed 4.canceled
     * </pre>
     *
     * <code>int32 status = 8;</code>
     * @return The status.
     */
    int getStatus();

    /**
     * <pre>
     *额外
     * </pre>
     *
     * <code>double extra = 9;</code>
     * @return The extra.
     */
    double getExtra();
  }
  /**
   * <pre>
   *返回充值成功
   * </pre>
   *
   * Protobuf type {@code ProtoMessage.ResTcpRechargeSuccessMessage}
   */
  public static final class ResTcpRechargeSuccessMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ProtoMessage.ResTcpRechargeSuccessMessage)
      ResTcpRechargeSuccessMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResTcpRechargeSuccessMessage.newBuilder() to construct.
    private ResTcpRechargeSuccessMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResTcpRechargeSuccessMessage() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResTcpRechargeSuccessMessage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResTcpRechargeSuccessMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              msgID_ = input.readInt32();
              break;
            }
            case 16: {

              playerId_ = input.readInt64();
              break;
            }
            case 24: {

              rechargeTimes_ = input.readInt32();
              break;
            }
            case 32: {

              currencyId_ = input.readInt32();
              break;
            }
            case 41: {

              currRecharge_ = input.readDouble();
              break;
            }
            case 49: {

              totalRecharge_ = input.readDouble();
              break;
            }
            case 56: {

              orderId_ = input.readInt64();
              break;
            }
            case 64: {

              status_ = input.readInt32();
              break;
            }
            case 73: {

              extra_ = input.readDouble();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpRechargeSuccessMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpRechargeSuccessMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.proto.TcpMessage.ResTcpRechargeSuccessMessage.class, com.proto.TcpMessage.ResTcpRechargeSuccessMessage.Builder.class);
    }

    public static final int MSGID_FIELD_NUMBER = 1;
    private int msgID_;
    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    @java.lang.Override
    public int getMsgID() {
      return msgID_;
    }

    public static final int PLAYERID_FIELD_NUMBER = 2;
    private long playerId_;
    /**
     * <code>int64 playerId = 2;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int RECHARGETIMES_FIELD_NUMBER = 3;
    private int rechargeTimes_;
    /**
     * <pre>
     *充值次数
     * </pre>
     *
     * <code>int32 rechargeTimes = 3;</code>
     * @return The rechargeTimes.
     */
    @java.lang.Override
    public int getRechargeTimes() {
      return rechargeTimes_;
    }

    public static final int CURRENCYID_FIELD_NUMBER = 4;
    private int currencyId_;
    /**
     * <pre>
     *币种
     * </pre>
     *
     * <code>int32 currencyId = 4;</code>
     * @return The currencyId.
     */
    @java.lang.Override
    public int getCurrencyId() {
      return currencyId_;
    }

    public static final int CURRRECHARGE_FIELD_NUMBER = 5;
    private double currRecharge_;
    /**
     * <pre>
     *当前金额
     * </pre>
     *
     * <code>double currRecharge = 5;</code>
     * @return The currRecharge.
     */
    @java.lang.Override
    public double getCurrRecharge() {
      return currRecharge_;
    }

    public static final int TOTALRECHARGE_FIELD_NUMBER = 6;
    private double totalRecharge_;
    /**
     * <pre>
     *总金额
     * </pre>
     *
     * <code>double totalRecharge = 6;</code>
     * @return The totalRecharge.
     */
    @java.lang.Override
    public double getTotalRecharge() {
      return totalRecharge_;
    }

    public static final int ORDERID_FIELD_NUMBER = 7;
    private long orderId_;
    /**
     * <pre>
     *订单id
     * </pre>
     *
     * <code>int64 orderId = 7;</code>
     * @return The orderId.
     */
    @java.lang.Override
    public long getOrderId() {
      return orderId_;
    }

    public static final int STATUS_FIELD_NUMBER = 8;
    private int status_;
    /**
     * <pre>
     *1.complete 2.processing 3.failed 4.canceled
     * </pre>
     *
     * <code>int32 status = 8;</code>
     * @return The status.
     */
    @java.lang.Override
    public int getStatus() {
      return status_;
    }

    public static final int EXTRA_FIELD_NUMBER = 9;
    private double extra_;
    /**
     * <pre>
     *额外
     * </pre>
     *
     * <code>double extra = 9;</code>
     * @return The extra.
     */
    @java.lang.Override
    public double getExtra() {
      return extra_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (msgID_ != 0) {
        output.writeInt32(1, msgID_);
      }
      if (playerId_ != 0L) {
        output.writeInt64(2, playerId_);
      }
      if (rechargeTimes_ != 0) {
        output.writeInt32(3, rechargeTimes_);
      }
      if (currencyId_ != 0) {
        output.writeInt32(4, currencyId_);
      }
      if (currRecharge_ != 0D) {
        output.writeDouble(5, currRecharge_);
      }
      if (totalRecharge_ != 0D) {
        output.writeDouble(6, totalRecharge_);
      }
      if (orderId_ != 0L) {
        output.writeInt64(7, orderId_);
      }
      if (status_ != 0) {
        output.writeInt32(8, status_);
      }
      if (extra_ != 0D) {
        output.writeDouble(9, extra_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (msgID_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, msgID_);
      }
      if (playerId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, playerId_);
      }
      if (rechargeTimes_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, rechargeTimes_);
      }
      if (currencyId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, currencyId_);
      }
      if (currRecharge_ != 0D) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(5, currRecharge_);
      }
      if (totalRecharge_ != 0D) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(6, totalRecharge_);
      }
      if (orderId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(7, orderId_);
      }
      if (status_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(8, status_);
      }
      if (extra_ != 0D) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(9, extra_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.proto.TcpMessage.ResTcpRechargeSuccessMessage)) {
        return super.equals(obj);
      }
      com.proto.TcpMessage.ResTcpRechargeSuccessMessage other = (com.proto.TcpMessage.ResTcpRechargeSuccessMessage) obj;

      if (getMsgID()
          != other.getMsgID()) return false;
      if (getPlayerId()
          != other.getPlayerId()) return false;
      if (getRechargeTimes()
          != other.getRechargeTimes()) return false;
      if (getCurrencyId()
          != other.getCurrencyId()) return false;
      if (java.lang.Double.doubleToLongBits(getCurrRecharge())
          != java.lang.Double.doubleToLongBits(
              other.getCurrRecharge())) return false;
      if (java.lang.Double.doubleToLongBits(getTotalRecharge())
          != java.lang.Double.doubleToLongBits(
              other.getTotalRecharge())) return false;
      if (getOrderId()
          != other.getOrderId()) return false;
      if (getStatus()
          != other.getStatus()) return false;
      if (java.lang.Double.doubleToLongBits(getExtra())
          != java.lang.Double.doubleToLongBits(
              other.getExtra())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + MSGID_FIELD_NUMBER;
      hash = (53 * hash) + getMsgID();
      hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getPlayerId());
      hash = (37 * hash) + RECHARGETIMES_FIELD_NUMBER;
      hash = (53 * hash) + getRechargeTimes();
      hash = (37 * hash) + CURRENCYID_FIELD_NUMBER;
      hash = (53 * hash) + getCurrencyId();
      hash = (37 * hash) + CURRRECHARGE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          java.lang.Double.doubleToLongBits(getCurrRecharge()));
      hash = (37 * hash) + TOTALRECHARGE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          java.lang.Double.doubleToLongBits(getTotalRecharge()));
      hash = (37 * hash) + ORDERID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getOrderId());
      hash = (37 * hash) + STATUS_FIELD_NUMBER;
      hash = (53 * hash) + getStatus();
      hash = (37 * hash) + EXTRA_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          java.lang.Double.doubleToLongBits(getExtra()));
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.proto.TcpMessage.ResTcpRechargeSuccessMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ResTcpRechargeSuccessMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpRechargeSuccessMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ResTcpRechargeSuccessMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpRechargeSuccessMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ResTcpRechargeSuccessMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpRechargeSuccessMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ResTcpRechargeSuccessMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpRechargeSuccessMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ResTcpRechargeSuccessMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpRechargeSuccessMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ResTcpRechargeSuccessMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.proto.TcpMessage.ResTcpRechargeSuccessMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *返回充值成功
     * </pre>
     *
     * Protobuf type {@code ProtoMessage.ResTcpRechargeSuccessMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ProtoMessage.ResTcpRechargeSuccessMessage)
        com.proto.TcpMessage.ResTcpRechargeSuccessMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpRechargeSuccessMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpRechargeSuccessMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.proto.TcpMessage.ResTcpRechargeSuccessMessage.class, com.proto.TcpMessage.ResTcpRechargeSuccessMessage.Builder.class);
      }

      // Construct using com.proto.TcpMessage.ResTcpRechargeSuccessMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        msgID_ = 0;

        playerId_ = 0L;

        rechargeTimes_ = 0;

        currencyId_ = 0;

        currRecharge_ = 0D;

        totalRecharge_ = 0D;

        orderId_ = 0L;

        status_ = 0;

        extra_ = 0D;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpRechargeSuccessMessage_descriptor;
      }

      @java.lang.Override
      public com.proto.TcpMessage.ResTcpRechargeSuccessMessage getDefaultInstanceForType() {
        return com.proto.TcpMessage.ResTcpRechargeSuccessMessage.getDefaultInstance();
      }

      @java.lang.Override
      public com.proto.TcpMessage.ResTcpRechargeSuccessMessage build() {
        com.proto.TcpMessage.ResTcpRechargeSuccessMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.proto.TcpMessage.ResTcpRechargeSuccessMessage buildPartial() {
        com.proto.TcpMessage.ResTcpRechargeSuccessMessage result = new com.proto.TcpMessage.ResTcpRechargeSuccessMessage(this);
        result.msgID_ = msgID_;
        result.playerId_ = playerId_;
        result.rechargeTimes_ = rechargeTimes_;
        result.currencyId_ = currencyId_;
        result.currRecharge_ = currRecharge_;
        result.totalRecharge_ = totalRecharge_;
        result.orderId_ = orderId_;
        result.status_ = status_;
        result.extra_ = extra_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.proto.TcpMessage.ResTcpRechargeSuccessMessage) {
          return mergeFrom((com.proto.TcpMessage.ResTcpRechargeSuccessMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.proto.TcpMessage.ResTcpRechargeSuccessMessage other) {
        if (other == com.proto.TcpMessage.ResTcpRechargeSuccessMessage.getDefaultInstance()) return this;
        if (other.getMsgID() != 0) {
          setMsgID(other.getMsgID());
        }
        if (other.getPlayerId() != 0L) {
          setPlayerId(other.getPlayerId());
        }
        if (other.getRechargeTimes() != 0) {
          setRechargeTimes(other.getRechargeTimes());
        }
        if (other.getCurrencyId() != 0) {
          setCurrencyId(other.getCurrencyId());
        }
        if (other.getCurrRecharge() != 0D) {
          setCurrRecharge(other.getCurrRecharge());
        }
        if (other.getTotalRecharge() != 0D) {
          setTotalRecharge(other.getTotalRecharge());
        }
        if (other.getOrderId() != 0L) {
          setOrderId(other.getOrderId());
        }
        if (other.getStatus() != 0) {
          setStatus(other.getStatus());
        }
        if (other.getExtra() != 0D) {
          setExtra(other.getExtra());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.proto.TcpMessage.ResTcpRechargeSuccessMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.proto.TcpMessage.ResTcpRechargeSuccessMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int msgID_ ;
      /**
       * <code>int32 msgID = 1;</code>
       * @return The msgID.
       */
      @java.lang.Override
      public int getMsgID() {
        return msgID_;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @param value The msgID to set.
       * @return This builder for chaining.
       */
      public Builder setMsgID(int value) {
        
        msgID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgID() {
        
        msgID_ = 0;
        onChanged();
        return this;
      }

      private long playerId_ ;
      /**
       * <code>int64 playerId = 2;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>int64 playerId = 2;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int64 playerId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private int rechargeTimes_ ;
      /**
       * <pre>
       *充值次数
       * </pre>
       *
       * <code>int32 rechargeTimes = 3;</code>
       * @return The rechargeTimes.
       */
      @java.lang.Override
      public int getRechargeTimes() {
        return rechargeTimes_;
      }
      /**
       * <pre>
       *充值次数
       * </pre>
       *
       * <code>int32 rechargeTimes = 3;</code>
       * @param value The rechargeTimes to set.
       * @return This builder for chaining.
       */
      public Builder setRechargeTimes(int value) {
        
        rechargeTimes_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *充值次数
       * </pre>
       *
       * <code>int32 rechargeTimes = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearRechargeTimes() {
        
        rechargeTimes_ = 0;
        onChanged();
        return this;
      }

      private int currencyId_ ;
      /**
       * <pre>
       *币种
       * </pre>
       *
       * <code>int32 currencyId = 4;</code>
       * @return The currencyId.
       */
      @java.lang.Override
      public int getCurrencyId() {
        return currencyId_;
      }
      /**
       * <pre>
       *币种
       * </pre>
       *
       * <code>int32 currencyId = 4;</code>
       * @param value The currencyId to set.
       * @return This builder for chaining.
       */
      public Builder setCurrencyId(int value) {
        
        currencyId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *币种
       * </pre>
       *
       * <code>int32 currencyId = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearCurrencyId() {
        
        currencyId_ = 0;
        onChanged();
        return this;
      }

      private double currRecharge_ ;
      /**
       * <pre>
       *当前金额
       * </pre>
       *
       * <code>double currRecharge = 5;</code>
       * @return The currRecharge.
       */
      @java.lang.Override
      public double getCurrRecharge() {
        return currRecharge_;
      }
      /**
       * <pre>
       *当前金额
       * </pre>
       *
       * <code>double currRecharge = 5;</code>
       * @param value The currRecharge to set.
       * @return This builder for chaining.
       */
      public Builder setCurrRecharge(double value) {
        
        currRecharge_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *当前金额
       * </pre>
       *
       * <code>double currRecharge = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearCurrRecharge() {
        
        currRecharge_ = 0D;
        onChanged();
        return this;
      }

      private double totalRecharge_ ;
      /**
       * <pre>
       *总金额
       * </pre>
       *
       * <code>double totalRecharge = 6;</code>
       * @return The totalRecharge.
       */
      @java.lang.Override
      public double getTotalRecharge() {
        return totalRecharge_;
      }
      /**
       * <pre>
       *总金额
       * </pre>
       *
       * <code>double totalRecharge = 6;</code>
       * @param value The totalRecharge to set.
       * @return This builder for chaining.
       */
      public Builder setTotalRecharge(double value) {
        
        totalRecharge_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *总金额
       * </pre>
       *
       * <code>double totalRecharge = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearTotalRecharge() {
        
        totalRecharge_ = 0D;
        onChanged();
        return this;
      }

      private long orderId_ ;
      /**
       * <pre>
       *订单id
       * </pre>
       *
       * <code>int64 orderId = 7;</code>
       * @return The orderId.
       */
      @java.lang.Override
      public long getOrderId() {
        return orderId_;
      }
      /**
       * <pre>
       *订单id
       * </pre>
       *
       * <code>int64 orderId = 7;</code>
       * @param value The orderId to set.
       * @return This builder for chaining.
       */
      public Builder setOrderId(long value) {
        
        orderId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *订单id
       * </pre>
       *
       * <code>int64 orderId = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearOrderId() {
        
        orderId_ = 0L;
        onChanged();
        return this;
      }

      private int status_ ;
      /**
       * <pre>
       *1.complete 2.processing 3.failed 4.canceled
       * </pre>
       *
       * <code>int32 status = 8;</code>
       * @return The status.
       */
      @java.lang.Override
      public int getStatus() {
        return status_;
      }
      /**
       * <pre>
       *1.complete 2.processing 3.failed 4.canceled
       * </pre>
       *
       * <code>int32 status = 8;</code>
       * @param value The status to set.
       * @return This builder for chaining.
       */
      public Builder setStatus(int value) {
        
        status_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *1.complete 2.processing 3.failed 4.canceled
       * </pre>
       *
       * <code>int32 status = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearStatus() {
        
        status_ = 0;
        onChanged();
        return this;
      }

      private double extra_ ;
      /**
       * <pre>
       *额外
       * </pre>
       *
       * <code>double extra = 9;</code>
       * @return The extra.
       */
      @java.lang.Override
      public double getExtra() {
        return extra_;
      }
      /**
       * <pre>
       *额外
       * </pre>
       *
       * <code>double extra = 9;</code>
       * @param value The extra to set.
       * @return This builder for chaining.
       */
      public Builder setExtra(double value) {
        
        extra_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *额外
       * </pre>
       *
       * <code>double extra = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearExtra() {
        
        extra_ = 0D;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ProtoMessage.ResTcpRechargeSuccessMessage)
    }

    // @@protoc_insertion_point(class_scope:ProtoMessage.ResTcpRechargeSuccessMessage)
    private static final com.proto.TcpMessage.ResTcpRechargeSuccessMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.proto.TcpMessage.ResTcpRechargeSuccessMessage();
    }

    public static com.proto.TcpMessage.ResTcpRechargeSuccessMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResTcpRechargeSuccessMessage>
        PARSER = new com.google.protobuf.AbstractParser<ResTcpRechargeSuccessMessage>() {
      @java.lang.Override
      public ResTcpRechargeSuccessMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResTcpRechargeSuccessMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResTcpRechargeSuccessMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResTcpRechargeSuccessMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.proto.TcpMessage.ResTcpRechargeSuccessMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResTcpWithdrawNotifyMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ProtoMessage.ResTcpWithdrawNotifyMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    int getMsgID();

    /**
     * <code>int64 playerId = 2;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <pre>
     *提现次数
     * </pre>
     *
     * <code>int32 withdrawTimes = 3;</code>
     * @return The withdrawTimes.
     */
    int getWithdrawTimes();

    /**
     * <pre>
     *币种
     * </pre>
     *
     * <code>int32 currencyId = 4;</code>
     * @return The currencyId.
     */
    int getCurrencyId();

    /**
     * <pre>
     *当前金额
     * </pre>
     *
     * <code>double amount = 5;</code>
     * @return The amount.
     */
    double getAmount();

    /**
     * <pre>
     *订单id
     * </pre>
     *
     * <code>int64 orderId = 6;</code>
     * @return The orderId.
     */
    long getOrderId();

    /**
     * <pre>
     *1.complete 2.processing 3.failed 4.canceled
     * </pre>
     *
     * <code>int32 status = 7;</code>
     * @return The status.
     */
    int getStatus();
  }
  /**
   * <pre>
   *返回提现通知
   * </pre>
   *
   * Protobuf type {@code ProtoMessage.ResTcpWithdrawNotifyMessage}
   */
  public static final class ResTcpWithdrawNotifyMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:ProtoMessage.ResTcpWithdrawNotifyMessage)
      ResTcpWithdrawNotifyMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResTcpWithdrawNotifyMessage.newBuilder() to construct.
    private ResTcpWithdrawNotifyMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResTcpWithdrawNotifyMessage() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResTcpWithdrawNotifyMessage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResTcpWithdrawNotifyMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              msgID_ = input.readInt32();
              break;
            }
            case 16: {

              playerId_ = input.readInt64();
              break;
            }
            case 24: {

              withdrawTimes_ = input.readInt32();
              break;
            }
            case 32: {

              currencyId_ = input.readInt32();
              break;
            }
            case 41: {

              amount_ = input.readDouble();
              break;
            }
            case 48: {

              orderId_ = input.readInt64();
              break;
            }
            case 56: {

              status_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpWithdrawNotifyMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpWithdrawNotifyMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.proto.TcpMessage.ResTcpWithdrawNotifyMessage.class, com.proto.TcpMessage.ResTcpWithdrawNotifyMessage.Builder.class);
    }

    public static final int MSGID_FIELD_NUMBER = 1;
    private int msgID_;
    /**
     * <code>int32 msgID = 1;</code>
     * @return The msgID.
     */
    @java.lang.Override
    public int getMsgID() {
      return msgID_;
    }

    public static final int PLAYERID_FIELD_NUMBER = 2;
    private long playerId_;
    /**
     * <code>int64 playerId = 2;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int WITHDRAWTIMES_FIELD_NUMBER = 3;
    private int withdrawTimes_;
    /**
     * <pre>
     *提现次数
     * </pre>
     *
     * <code>int32 withdrawTimes = 3;</code>
     * @return The withdrawTimes.
     */
    @java.lang.Override
    public int getWithdrawTimes() {
      return withdrawTimes_;
    }

    public static final int CURRENCYID_FIELD_NUMBER = 4;
    private int currencyId_;
    /**
     * <pre>
     *币种
     * </pre>
     *
     * <code>int32 currencyId = 4;</code>
     * @return The currencyId.
     */
    @java.lang.Override
    public int getCurrencyId() {
      return currencyId_;
    }

    public static final int AMOUNT_FIELD_NUMBER = 5;
    private double amount_;
    /**
     * <pre>
     *当前金额
     * </pre>
     *
     * <code>double amount = 5;</code>
     * @return The amount.
     */
    @java.lang.Override
    public double getAmount() {
      return amount_;
    }

    public static final int ORDERID_FIELD_NUMBER = 6;
    private long orderId_;
    /**
     * <pre>
     *订单id
     * </pre>
     *
     * <code>int64 orderId = 6;</code>
     * @return The orderId.
     */
    @java.lang.Override
    public long getOrderId() {
      return orderId_;
    }

    public static final int STATUS_FIELD_NUMBER = 7;
    private int status_;
    /**
     * <pre>
     *1.complete 2.processing 3.failed 4.canceled
     * </pre>
     *
     * <code>int32 status = 7;</code>
     * @return The status.
     */
    @java.lang.Override
    public int getStatus() {
      return status_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (msgID_ != 0) {
        output.writeInt32(1, msgID_);
      }
      if (playerId_ != 0L) {
        output.writeInt64(2, playerId_);
      }
      if (withdrawTimes_ != 0) {
        output.writeInt32(3, withdrawTimes_);
      }
      if (currencyId_ != 0) {
        output.writeInt32(4, currencyId_);
      }
      if (amount_ != 0D) {
        output.writeDouble(5, amount_);
      }
      if (orderId_ != 0L) {
        output.writeInt64(6, orderId_);
      }
      if (status_ != 0) {
        output.writeInt32(7, status_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (msgID_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, msgID_);
      }
      if (playerId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, playerId_);
      }
      if (withdrawTimes_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, withdrawTimes_);
      }
      if (currencyId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, currencyId_);
      }
      if (amount_ != 0D) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(5, amount_);
      }
      if (orderId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(6, orderId_);
      }
      if (status_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(7, status_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.proto.TcpMessage.ResTcpWithdrawNotifyMessage)) {
        return super.equals(obj);
      }
      com.proto.TcpMessage.ResTcpWithdrawNotifyMessage other = (com.proto.TcpMessage.ResTcpWithdrawNotifyMessage) obj;

      if (getMsgID()
          != other.getMsgID()) return false;
      if (getPlayerId()
          != other.getPlayerId()) return false;
      if (getWithdrawTimes()
          != other.getWithdrawTimes()) return false;
      if (getCurrencyId()
          != other.getCurrencyId()) return false;
      if (java.lang.Double.doubleToLongBits(getAmount())
          != java.lang.Double.doubleToLongBits(
              other.getAmount())) return false;
      if (getOrderId()
          != other.getOrderId()) return false;
      if (getStatus()
          != other.getStatus()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + MSGID_FIELD_NUMBER;
      hash = (53 * hash) + getMsgID();
      hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getPlayerId());
      hash = (37 * hash) + WITHDRAWTIMES_FIELD_NUMBER;
      hash = (53 * hash) + getWithdrawTimes();
      hash = (37 * hash) + CURRENCYID_FIELD_NUMBER;
      hash = (53 * hash) + getCurrencyId();
      hash = (37 * hash) + AMOUNT_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          java.lang.Double.doubleToLongBits(getAmount()));
      hash = (37 * hash) + ORDERID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getOrderId());
      hash = (37 * hash) + STATUS_FIELD_NUMBER;
      hash = (53 * hash) + getStatus();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.proto.TcpMessage.ResTcpWithdrawNotifyMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ResTcpWithdrawNotifyMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpWithdrawNotifyMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ResTcpWithdrawNotifyMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpWithdrawNotifyMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.proto.TcpMessage.ResTcpWithdrawNotifyMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpWithdrawNotifyMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ResTcpWithdrawNotifyMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpWithdrawNotifyMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ResTcpWithdrawNotifyMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.proto.TcpMessage.ResTcpWithdrawNotifyMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.proto.TcpMessage.ResTcpWithdrawNotifyMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.proto.TcpMessage.ResTcpWithdrawNotifyMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *返回提现通知
     * </pre>
     *
     * Protobuf type {@code ProtoMessage.ResTcpWithdrawNotifyMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ProtoMessage.ResTcpWithdrawNotifyMessage)
        com.proto.TcpMessage.ResTcpWithdrawNotifyMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpWithdrawNotifyMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpWithdrawNotifyMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.proto.TcpMessage.ResTcpWithdrawNotifyMessage.class, com.proto.TcpMessage.ResTcpWithdrawNotifyMessage.Builder.class);
      }

      // Construct using com.proto.TcpMessage.ResTcpWithdrawNotifyMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        msgID_ = 0;

        playerId_ = 0L;

        withdrawTimes_ = 0;

        currencyId_ = 0;

        amount_ = 0D;

        orderId_ = 0L;

        status_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.proto.TcpMessage.internal_static_ProtoMessage_ResTcpWithdrawNotifyMessage_descriptor;
      }

      @java.lang.Override
      public com.proto.TcpMessage.ResTcpWithdrawNotifyMessage getDefaultInstanceForType() {
        return com.proto.TcpMessage.ResTcpWithdrawNotifyMessage.getDefaultInstance();
      }

      @java.lang.Override
      public com.proto.TcpMessage.ResTcpWithdrawNotifyMessage build() {
        com.proto.TcpMessage.ResTcpWithdrawNotifyMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.proto.TcpMessage.ResTcpWithdrawNotifyMessage buildPartial() {
        com.proto.TcpMessage.ResTcpWithdrawNotifyMessage result = new com.proto.TcpMessage.ResTcpWithdrawNotifyMessage(this);
        result.msgID_ = msgID_;
        result.playerId_ = playerId_;
        result.withdrawTimes_ = withdrawTimes_;
        result.currencyId_ = currencyId_;
        result.amount_ = amount_;
        result.orderId_ = orderId_;
        result.status_ = status_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.proto.TcpMessage.ResTcpWithdrawNotifyMessage) {
          return mergeFrom((com.proto.TcpMessage.ResTcpWithdrawNotifyMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.proto.TcpMessage.ResTcpWithdrawNotifyMessage other) {
        if (other == com.proto.TcpMessage.ResTcpWithdrawNotifyMessage.getDefaultInstance()) return this;
        if (other.getMsgID() != 0) {
          setMsgID(other.getMsgID());
        }
        if (other.getPlayerId() != 0L) {
          setPlayerId(other.getPlayerId());
        }
        if (other.getWithdrawTimes() != 0) {
          setWithdrawTimes(other.getWithdrawTimes());
        }
        if (other.getCurrencyId() != 0) {
          setCurrencyId(other.getCurrencyId());
        }
        if (other.getAmount() != 0D) {
          setAmount(other.getAmount());
        }
        if (other.getOrderId() != 0L) {
          setOrderId(other.getOrderId());
        }
        if (other.getStatus() != 0) {
          setStatus(other.getStatus());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.proto.TcpMessage.ResTcpWithdrawNotifyMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.proto.TcpMessage.ResTcpWithdrawNotifyMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int msgID_ ;
      /**
       * <code>int32 msgID = 1;</code>
       * @return The msgID.
       */
      @java.lang.Override
      public int getMsgID() {
        return msgID_;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @param value The msgID to set.
       * @return This builder for chaining.
       */
      public Builder setMsgID(int value) {
        
        msgID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 msgID = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgID() {
        
        msgID_ = 0;
        onChanged();
        return this;
      }

      private long playerId_ ;
      /**
       * <code>int64 playerId = 2;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>int64 playerId = 2;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int64 playerId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private int withdrawTimes_ ;
      /**
       * <pre>
       *提现次数
       * </pre>
       *
       * <code>int32 withdrawTimes = 3;</code>
       * @return The withdrawTimes.
       */
      @java.lang.Override
      public int getWithdrawTimes() {
        return withdrawTimes_;
      }
      /**
       * <pre>
       *提现次数
       * </pre>
       *
       * <code>int32 withdrawTimes = 3;</code>
       * @param value The withdrawTimes to set.
       * @return This builder for chaining.
       */
      public Builder setWithdrawTimes(int value) {
        
        withdrawTimes_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *提现次数
       * </pre>
       *
       * <code>int32 withdrawTimes = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearWithdrawTimes() {
        
        withdrawTimes_ = 0;
        onChanged();
        return this;
      }

      private int currencyId_ ;
      /**
       * <pre>
       *币种
       * </pre>
       *
       * <code>int32 currencyId = 4;</code>
       * @return The currencyId.
       */
      @java.lang.Override
      public int getCurrencyId() {
        return currencyId_;
      }
      /**
       * <pre>
       *币种
       * </pre>
       *
       * <code>int32 currencyId = 4;</code>
       * @param value The currencyId to set.
       * @return This builder for chaining.
       */
      public Builder setCurrencyId(int value) {
        
        currencyId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *币种
       * </pre>
       *
       * <code>int32 currencyId = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearCurrencyId() {
        
        currencyId_ = 0;
        onChanged();
        return this;
      }

      private double amount_ ;
      /**
       * <pre>
       *当前金额
       * </pre>
       *
       * <code>double amount = 5;</code>
       * @return The amount.
       */
      @java.lang.Override
      public double getAmount() {
        return amount_;
      }
      /**
       * <pre>
       *当前金额
       * </pre>
       *
       * <code>double amount = 5;</code>
       * @param value The amount to set.
       * @return This builder for chaining.
       */
      public Builder setAmount(double value) {
        
        amount_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *当前金额
       * </pre>
       *
       * <code>double amount = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearAmount() {
        
        amount_ = 0D;
        onChanged();
        return this;
      }

      private long orderId_ ;
      /**
       * <pre>
       *订单id
       * </pre>
       *
       * <code>int64 orderId = 6;</code>
       * @return The orderId.
       */
      @java.lang.Override
      public long getOrderId() {
        return orderId_;
      }
      /**
       * <pre>
       *订单id
       * </pre>
       *
       * <code>int64 orderId = 6;</code>
       * @param value The orderId to set.
       * @return This builder for chaining.
       */
      public Builder setOrderId(long value) {
        
        orderId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *订单id
       * </pre>
       *
       * <code>int64 orderId = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearOrderId() {
        
        orderId_ = 0L;
        onChanged();
        return this;
      }

      private int status_ ;
      /**
       * <pre>
       *1.complete 2.processing 3.failed 4.canceled
       * </pre>
       *
       * <code>int32 status = 7;</code>
       * @return The status.
       */
      @java.lang.Override
      public int getStatus() {
        return status_;
      }
      /**
       * <pre>
       *1.complete 2.processing 3.failed 4.canceled
       * </pre>
       *
       * <code>int32 status = 7;</code>
       * @param value The status to set.
       * @return This builder for chaining.
       */
      public Builder setStatus(int value) {
        
        status_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *1.complete 2.processing 3.failed 4.canceled
       * </pre>
       *
       * <code>int32 status = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearStatus() {
        
        status_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:ProtoMessage.ResTcpWithdrawNotifyMessage)
    }

    // @@protoc_insertion_point(class_scope:ProtoMessage.ResTcpWithdrawNotifyMessage)
    private static final com.proto.TcpMessage.ResTcpWithdrawNotifyMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.proto.TcpMessage.ResTcpWithdrawNotifyMessage();
    }

    public static com.proto.TcpMessage.ResTcpWithdrawNotifyMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResTcpWithdrawNotifyMessage>
        PARSER = new com.google.protobuf.AbstractParser<ResTcpWithdrawNotifyMessage>() {
      @java.lang.Override
      public ResTcpWithdrawNotifyMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResTcpWithdrawNotifyMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResTcpWithdrawNotifyMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResTcpWithdrawNotifyMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.proto.TcpMessage.ResTcpWithdrawNotifyMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ProtoMessage_ReqTcpTokenAuthMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ProtoMessage_ReqTcpTokenAuthMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ProtoMessage_ResTcpTokenAuthMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ProtoMessage_ResTcpTokenAuthMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ProtoMessage_ReqTcpHeartBeatMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ProtoMessage_ReqTcpHeartBeatMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ProtoMessage_ResTcpHeartBeatMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ProtoMessage_ResTcpHeartBeatMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ProtoMessage_ReqTcpQuitAgentGameMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ProtoMessage_ReqTcpQuitAgentGameMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ProtoMessage_ResTcpQuitAgentGameMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ProtoMessage_ResTcpQuitAgentGameMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ProtoMessage_ResTcpSysErrorMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ProtoMessage_ResTcpSysErrorMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ProtoMessage_ResTcpBulletinDataMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ProtoMessage_ResTcpBulletinDataMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ProtoMessage_ResTcpCurrencyUpdateMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ProtoMessage_ResTcpCurrencyUpdateMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ProtoMessage_ResTcpReceiveInboxMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ProtoMessage_ResTcpReceiveInboxMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ProtoMessage_ResTcpVipClubExpChangeMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ProtoMessage_ResTcpVipClubExpChangeMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ProtoMessage_ResTcpGameNoteDataMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ProtoMessage_ResTcpGameNoteDataMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ProtoMessage_ResTcpMyBetDataMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ProtoMessage_ResTcpMyBetDataMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ProtoMessage_ResTcpDailyContestPrizePoolMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ProtoMessage_ResTcpDailyContestPrizePoolMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ProtoMessage_ResTcpKickOutPlayerMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ProtoMessage_ResTcpKickOutPlayerMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ProtoMessage_ResTcpRechargeSuccessMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ProtoMessage_ResTcpRechargeSuccessMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ProtoMessage_ResTcpWithdrawNotifyMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_ProtoMessage_ResTcpWithdrawNotifyMessage_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\020TcpMessage.proto\022\014ProtoMessage\032\023Common" +
      "Message.proto\032\026BackStageMessage.proto\"6\n" +
      "\026ReqTcpTokenAuthMessage\022\r\n\005msgID\030\001 \001(\005\022\r" +
      "\n\005token\030\002 \001(\t\"6\n\026ResTcpTokenAuthMessage\022" +
      "\r\n\005msgID\030\001 \001(\005\022\r\n\005error\030\002 \001(\005\"v\n\026ReqTcpH" +
      "eartBeatMessage\022\r\n\005msgID\030\001 \001(\005\022\020\n\010gameNa" +
      "me\030\002 \001(\t\022\017\n\007account\030\003 \001(\t\022\023\n\013business_no" +
      "\030\004 \001(\t\022\025\n\ruserSessionId\030\005 \001(\003\"J\n\026ResTcpH" +
      "eartBeatMessage\022\r\n\005msgID\030\001 \001(\005\022\r\n\005error\030" +
      "\002 \001(\005\022\022\n\nsystemTime\030\003 \001(\003\"+\n\032ReqTcpQuitA" +
      "gentGameMessage\022\r\n\005msgID\030\001 \001(\005\":\n\032ResTcp" +
      "QuitAgentGameMessage\022\r\n\005msgID\030\001 \001(\005\022\r\n\005e" +
      "rror\030\002 \001(\005\"F\n\025ResTcpSysErrorMessage\022\r\n\005m" +
      "sgID\030\001 \001(\005\022\r\n\005error\030\002 \001(\005\022\017\n\007context\030\003 \001" +
      "(\t\"\214\001\n\031ResTcpBulletinDataMessage\022\r\n\005msgI" +
      "D\030\001 \001(\005\022\r\n\005error\030\002 \001(\005\022\023\n\013business_no\030\003 " +
      "\001(\t\022<\n\022maintainNoticeInfo\030\004 \001(\0132 .ProtoM" +
      "essage.MaintainNoticeInfo\"x\n\033ResTcpCurre" +
      "ncyUpdateMessage\022\r\n\005msgID\030\001 \001(\005\022*\n\005cItem" +
      "\030\002 \003(\0132\033.ProtoMessage.DCurrencyItem\022\016\n\006r" +
      "eason\030\003 \001(\005\022\016\n\006source\030\004 \001(\005\"V\n\031ResTcpRec" +
      "eiveInboxMessage\022\r\n\005msgID\030\001 \001(\005\022*\n\tinbox" +
      "List\030\002 \003(\0132\027.ProtoMessage.InboxInfo\"\214\001\n\035" +
      "ResTcpVipClubExpChangeMessage\022\r\n\005msgID\030\001" +
      " \001(\005\022\016\n\006curExp\030\002 \001(\001\022\017\n\007needExp\030\003 \001(\001\022\020\n" +
      "\010vipLevel\030\004 \001(\005\022\023\n\013curRecharge\030\005 \001(\001\022\024\n\014" +
      "needRecharge\030\006 \001(\001\"\273\001\n\031ResTcpGameNoteDat" +
      "aMessage\022\r\n\005msgID\030\001 \001(\005\022\023\n\013business_no\030\002" +
      " \001(\t\022%\n\006allBet\030\003 \003(\0132\025.ProtoMessage.BetI" +
      "nfo\022*\n\013highRollers\030\004 \003(\0132\025.ProtoMessage." +
      "BetInfo\022\'\n\010luckyBet\030\005 \003(\0132\025.ProtoMessage" +
      ".BetInfo\"Q\n\026ResTcpMyBetDataMessage\022\r\n\005ms" +
      "gID\030\001 \001(\005\022(\n\tmyBetList\030\002 \003(\0132\025.ProtoMess" +
      "age.BetInfo\"[\n\"ResTcpDailyContestPrizePo" +
      "olMessage\022\r\n\005msgID\030\001 \001(\005\022\023\n\013business_no\030" +
      "\002 \001(\t\022\021\n\tprizePool\030\003 \001(\001\";\n\032ResTcpKickOu" +
      "tPlayerMessage\022\r\n\005msgID\030\001 \001(\005\022\016\n\006reason\030" +
      "\002 \001(\005\"\307\001\n\034ResTcpRechargeSuccessMessage\022\r" +
      "\n\005msgID\030\001 \001(\005\022\020\n\010playerId\030\002 \001(\003\022\025\n\rrecha" +
      "rgeTimes\030\003 \001(\005\022\022\n\ncurrencyId\030\004 \001(\005\022\024\n\014cu" +
      "rrRecharge\030\005 \001(\001\022\025\n\rtotalRecharge\030\006 \001(\001\022" +
      "\017\n\007orderId\030\007 \001(\003\022\016\n\006status\030\010 \001(\005\022\r\n\005extr" +
      "a\030\t \001(\001\"\232\001\n\033ResTcpWithdrawNotifyMessage\022" +
      "\r\n\005msgID\030\001 \001(\005\022\020\n\010playerId\030\002 \001(\003\022\025\n\rwith" +
      "drawTimes\030\003 \001(\005\022\022\n\ncurrencyId\030\004 \001(\005\022\016\n\006a" +
      "mount\030\005 \001(\001\022\017\n\007orderId\030\006 \001(\003\022\016\n\006status\030\007" +
      " \001(\005B\013\n\tcom.protob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.proto.CommonMessage.getDescriptor(),
          com.proto.BackStageMessage.getDescriptor(),
        });
    internal_static_ProtoMessage_ReqTcpTokenAuthMessage_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_ProtoMessage_ReqTcpTokenAuthMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ProtoMessage_ReqTcpTokenAuthMessage_descriptor,
        new java.lang.String[] { "MsgID", "Token", });
    internal_static_ProtoMessage_ResTcpTokenAuthMessage_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_ProtoMessage_ResTcpTokenAuthMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ProtoMessage_ResTcpTokenAuthMessage_descriptor,
        new java.lang.String[] { "MsgID", "Error", });
    internal_static_ProtoMessage_ReqTcpHeartBeatMessage_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_ProtoMessage_ReqTcpHeartBeatMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ProtoMessage_ReqTcpHeartBeatMessage_descriptor,
        new java.lang.String[] { "MsgID", "GameName", "Account", "BusinessNo", "UserSessionId", });
    internal_static_ProtoMessage_ResTcpHeartBeatMessage_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_ProtoMessage_ResTcpHeartBeatMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ProtoMessage_ResTcpHeartBeatMessage_descriptor,
        new java.lang.String[] { "MsgID", "Error", "SystemTime", });
    internal_static_ProtoMessage_ReqTcpQuitAgentGameMessage_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_ProtoMessage_ReqTcpQuitAgentGameMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ProtoMessage_ReqTcpQuitAgentGameMessage_descriptor,
        new java.lang.String[] { "MsgID", });
    internal_static_ProtoMessage_ResTcpQuitAgentGameMessage_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_ProtoMessage_ResTcpQuitAgentGameMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ProtoMessage_ResTcpQuitAgentGameMessage_descriptor,
        new java.lang.String[] { "MsgID", "Error", });
    internal_static_ProtoMessage_ResTcpSysErrorMessage_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_ProtoMessage_ResTcpSysErrorMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ProtoMessage_ResTcpSysErrorMessage_descriptor,
        new java.lang.String[] { "MsgID", "Error", "Context", });
    internal_static_ProtoMessage_ResTcpBulletinDataMessage_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_ProtoMessage_ResTcpBulletinDataMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ProtoMessage_ResTcpBulletinDataMessage_descriptor,
        new java.lang.String[] { "MsgID", "Error", "BusinessNo", "MaintainNoticeInfo", });
    internal_static_ProtoMessage_ResTcpCurrencyUpdateMessage_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_ProtoMessage_ResTcpCurrencyUpdateMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ProtoMessage_ResTcpCurrencyUpdateMessage_descriptor,
        new java.lang.String[] { "MsgID", "CItem", "Reason", "Source", });
    internal_static_ProtoMessage_ResTcpReceiveInboxMessage_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_ProtoMessage_ResTcpReceiveInboxMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ProtoMessage_ResTcpReceiveInboxMessage_descriptor,
        new java.lang.String[] { "MsgID", "InboxList", });
    internal_static_ProtoMessage_ResTcpVipClubExpChangeMessage_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_ProtoMessage_ResTcpVipClubExpChangeMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ProtoMessage_ResTcpVipClubExpChangeMessage_descriptor,
        new java.lang.String[] { "MsgID", "CurExp", "NeedExp", "VipLevel", "CurRecharge", "NeedRecharge", });
    internal_static_ProtoMessage_ResTcpGameNoteDataMessage_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_ProtoMessage_ResTcpGameNoteDataMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ProtoMessage_ResTcpGameNoteDataMessage_descriptor,
        new java.lang.String[] { "MsgID", "BusinessNo", "AllBet", "HighRollers", "LuckyBet", });
    internal_static_ProtoMessage_ResTcpMyBetDataMessage_descriptor =
      getDescriptor().getMessageTypes().get(12);
    internal_static_ProtoMessage_ResTcpMyBetDataMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ProtoMessage_ResTcpMyBetDataMessage_descriptor,
        new java.lang.String[] { "MsgID", "MyBetList", });
    internal_static_ProtoMessage_ResTcpDailyContestPrizePoolMessage_descriptor =
      getDescriptor().getMessageTypes().get(13);
    internal_static_ProtoMessage_ResTcpDailyContestPrizePoolMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ProtoMessage_ResTcpDailyContestPrizePoolMessage_descriptor,
        new java.lang.String[] { "MsgID", "BusinessNo", "PrizePool", });
    internal_static_ProtoMessage_ResTcpKickOutPlayerMessage_descriptor =
      getDescriptor().getMessageTypes().get(14);
    internal_static_ProtoMessage_ResTcpKickOutPlayerMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ProtoMessage_ResTcpKickOutPlayerMessage_descriptor,
        new java.lang.String[] { "MsgID", "Reason", });
    internal_static_ProtoMessage_ResTcpRechargeSuccessMessage_descriptor =
      getDescriptor().getMessageTypes().get(15);
    internal_static_ProtoMessage_ResTcpRechargeSuccessMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ProtoMessage_ResTcpRechargeSuccessMessage_descriptor,
        new java.lang.String[] { "MsgID", "PlayerId", "RechargeTimes", "CurrencyId", "CurrRecharge", "TotalRecharge", "OrderId", "Status", "Extra", });
    internal_static_ProtoMessage_ResTcpWithdrawNotifyMessage_descriptor =
      getDescriptor().getMessageTypes().get(16);
    internal_static_ProtoMessage_ResTcpWithdrawNotifyMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_ProtoMessage_ResTcpWithdrawNotifyMessage_descriptor,
        new java.lang.String[] { "MsgID", "PlayerId", "WithdrawTimes", "CurrencyId", "Amount", "OrderId", "Status", });
    com.proto.CommonMessage.getDescriptor();
    com.proto.BackStageMessage.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
