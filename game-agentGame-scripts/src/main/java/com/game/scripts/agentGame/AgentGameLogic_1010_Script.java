package com.game.scripts.agentGame;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.game.c_entity.middleplatform.C_BaseGamePlatform;
import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.enums.AgentGame;
import com.game.enums.ErrorCode;
import com.game.gamesr.main.AgentGameServer;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.manager.DataAgentGameMrg;
import com.game.gamesr.scripts.IAgentGameLogicScript;
import com.proto.HallMessage;
import com.proto.InnerMessage;
import com.proto.MIDMessage;
import io.netty.channel.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URI;
import java.net.URLEncoder;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 1GameHub 集成实现（PGS real_play GET）
 * 文档要点：
 * - 每次请求必须带 secret（对接方提供，放 platformCfg.getSecretKey()）。
 * - real_play 必填：action/secret/game_id/player_id/currency/ip_address；可选：mobile/return_url/deposit_url/language/extra/debug
 * - 成功返回 { status:200, response:{ game_url, token } }
 */
public class AgentGameLogic_1010_Script implements IAgentGameLogicScript {
    private static final Logger LOGGER = LoggerFactory.getLogger(AgentGameLogic_1010_Script.class);

    @Override
    public boolean entryAgentGame(HallMessage.ReqEntryAgentGameMessage req, Player player, Channel session, long udpSessionId) {
        final HallMessage.ResEntryAgentGameMessage.Builder res = HallMessage.ResEntryAgentGameMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResEntryAgentGame_VALUE);

        try {
            // ===== 输入准备 =====
            final String currency = req.getCurrencyName();
            final String platformGameId = req.getPlatformGameId();
            final String ip = MsgUtil.getClientIp(session);          // 文档必填：ip_address
            // todo 暂时没法获得，看效果再决定是否扩展
//            final int mobileFlag = (req.getClientType() == HallMessage.ClientType.MOBILE) ? 1 : 0;

            // todo 以后有印度和墨西哥 语言占位（按你现有规则）
            String language = "EN";
            if (player.getLanguage() == 2) {
                language = "PT";
            }

            // 平台配置（含 apiUrl / secret 等）
            final C_BaseGamePlatform platformCfg = DataAgentGameMrg.getInstance()
                    .findC_BaseGamePlatformSupplierId(this.getClass().getSimpleName(), AgentGame.GAMEHUB.getType());

            final String apiBase = platformCfg.getApiUrl();   // 例如：https://<PGS_HOST>/api
            final String secret  = platformCfg.getSecretKey();// 对接方提供的 secret（务必从安全配置加载）

            // ===== 组装 real_play GET 请求参数 =====
            Map<String, String> qp = new LinkedHashMap<>();
            qp.put("action", "real_play");                               // 必填
            qp.put("secret", secret);                                    // 必填：服务认证
            qp.put("game_id", platformGameId);                           // 必填：provider 的 game_id
            qp.put("player_id", String.valueOf(player.getPlayerId()));   // 必填：你方玩家唯一 ID
            qp.put("currency", currency);                                // 必填：ISO4217/4 字母
            qp.put("ip_address", ip);                                    // 必填：用户 IP

            // todo 可选参数（按需开放）
//            qp.put("mobile", String.valueOf(mobileFlag));                // 可选：1=移动版
            // qp.put("return_url", "https://your-site.example.com/return");   // 可选：回退按钮 URL（对接时配置）
            // qp.put("deposit_url", "https://your-site.example.com/deposit"); // 可选：充值按钮 URL（新版本可用）
            qp.put("language", language);                                // 可选：ISO 639-1 两位
            // 透传参数：后续 GAP 调用会原样带回（如需要再开启）
            // qp.put("extra", urlEncode("uid=" + player.getPlayerId() + "&agent=" + AgentGame.GAMEHUB.getType()));
            if (ConstantConfig.getInstance().isAgentDebug()) {
                qp.put("debug", "true");                                 // 可选：联调期打开
            }

            final String url = buildGetUrl(apiBase, qp);
            LOGGER.info("GameHub real_play GET url: {}", url);

            // ===== 发送 HTTP (沿用你给的 Funky 写法：使用 AgentGameServer 的 HttpClient) =====
            HttpRequest httpReq = HttpRequest.newBuilder()
                    .GET()
                    .uri(URI.create(url))
                    .timeout(Duration.ofSeconds(10))
                    .version(HttpClient.Version.HTTP_1_1)
                    .build();

            HttpResponse<String> httpRes = AgentGameServer.getInstance().getHttpClientMrg()
                    .send(httpReq, HttpResponse.BodyHandlers.ofString());

            final String body = httpRes.body();
            JSONObject responseJson = JSONObject.parseObject(body);
            if (responseJson.getIntValue("status") == 200) {
                JSONObject data = responseJson.getJSONObject("response");
                String gameUrl = data.getString("game_url");
                String token = data.getString("token");

                // 构建完整游戏URL
                String fullGameUrl = gameUrl + "?token=" + token;

                res.setGameUrl(fullGameUrl)
                        .setGameId(req.getGameId())
                        .setCurrencyId(req.getCurrencyId())
                        .setGameType(req.getGameType())
                        .setGameCurrencyId(req.getGameCurrencyId())
                        .setPlatformId(req.getPlatformId())
                        .setBonus(req.getBonus());

                MsgUtil.sendInnerMsg(session, res.build(), player.getPlayerId(), udpSessionId);
                return true;
            } else {
                LOGGER.warn("GameHub LaunchGame failed, code: {}, message: {}",
                        responseJson.getIntValue("errorCode"),
                        responseJson.getString("errorMessage"));
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.sendInnerMsg(session, res.build(), player.getPlayerId(), udpSessionId);
                return false;
            }
        } catch (Exception e) {
            LOGGER.error("GameHub real_play entryAgentGame error", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.sendInnerMsg(session, res.build(), player.getPlayerId(), udpSessionId);
            return false;
        }
    }

    @Override
    public void notifyUpdateCurrency(InnerMessage.NotifyData req, long pid) {
        try {
            LOGGER.info("notifyUpdateCurrency response type: {}", req.getType());
            Channel session = AgentGameMrg.getInstance().getUdpSessionMap().get(req.getUpdSessionId());
            // 保持你现有的回前端逻辑
            com.game.handler.http.agentGame_1008_bti.BtiUtils.sendSuccessResponse(session, req.getBalance());
        } catch (Exception e) {
            LOGGER.error("updateCurrency error", e);
        } finally {
            AgentGameMrg.getInstance().removeUdpSessionMap(req.getUpdSessionId());
        }
    }

    // ====================== utils ======================

    private static String buildGetUrl(String base, Map<String, String> qp) {
        StringBuilder sb = new StringBuilder();
        sb.append(base);
        if (!base.contains("?")) sb.append("?");
        else if (!base.endsWith("&") && !base.endsWith("?")) sb.append("&");

        boolean first = true;
        for (Map.Entry<String, String> e : qp.entrySet()) {
            if (!first) sb.append("&");
            first = false;
            sb.append(URLEncoder.encode(e.getKey(), StandardCharsets.UTF_8))
                    .append("=")
                    .append(URLEncoder.encode(e.getValue(), StandardCharsets.UTF_8));
        }
        return sb.toString();
    }
}
