package com.game.scripts.agentGame;

import com.alibaba.fastjson.JSONObject;
import com.game.c_entity.middleplatform.C_BaseGamePlatform;
import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.enums.AgentGame;
import com.game.enums.ErrorCode;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.manager.DataAgentGameMrg;
import com.game.gamesr.scripts.IAgentGameLogicScript;
import com.game.gamesr.scripts.IAgentGameScript;
import com.game.handler.http.agentGame_1008_bti.BtiJWTUtils;
import com.game.handler.http.agentGame_1008_bti.BtiUtils;
import com.proto.HallMessage;
import com.proto.InnerMessage;
import com.proto.MIDMessage;
import io.netty.channel.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * BTI集成实现
 */
public class AgentGameLogic_1008_Script implements IAgentGameLogicScript {
    private static final Logger LOGGER = LoggerFactory.getLogger(AgentGameLogic_1008_Script.class);

    @Override
    public boolean entryAgentGame(HallMessage.ReqEntryAgentGameMessage req, Player player, Channel session, long udpSessionId) {
        final HallMessage.ResEntryAgentGameMessage.Builder res = HallMessage.ResEntryAgentGameMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResEntryAgentGame_VALUE);
        try {
            final String currency = req.getCurrencyName();

            final C_BaseGamePlatform c_baseGamePlatform = DataAgentGameMrg.getInstance()
                    .findC_BaseGamePlatformSupplierId(this.getClass().getSimpleName(), AgentGame.BTI.getType());

            LOGGER.info("白名单: {}", c_baseGamePlatform.getIpBlacklist());

            // 创建 JWT 作为 Session ID
            BtiJWTUtils.JWData jwData = new BtiJWTUtils.JWData();
//            final JWTUtils.JWData jwData = new JWTUtils.JWData();
            jwData.userId = player.getPlayerId();
            jwData.currency = currency;
            jwData.playerName = player.getPlayerName();
            jwData.bonus = req.getBonus();
            jwData.expires = TimeUtil.currentTimeMillis() + 7 * TimeUtil.DAY;
            final String token = BtiJWTUtils.jwtToken(jwData);
            final String gameUrl = "https://prod20208-174692675.freethrow777.com?operatorToken=" + token;

            LOGGER.info(token);
            LOGGER.info(gameUrl);

            res.setGameUrl(gameUrl)
                    .setGameId(req.getGameId())
                    .setCurrencyId(req.getCurrencyId())
                    .setGameType(req.getGameType())
                    .setGameCurrencyId(req.getGameCurrencyId())
                    .setPlatformId(req.getPlatformId())
                    .setBonus(req.getBonus());

            LOGGER.info("currency: {}", currency);

            // 向前端返回 token
            MsgUtil.sendInnerMsg(session, res.build(), player.getPlayerId(), udpSessionId);

        } catch (Exception e) {
            LOGGER.error("Bti entryAgentGame error", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.sendInnerMsg(session, res.build(), player.getPlayerId(), udpSessionId);
            return false;
        }
        return true;
    }

    @Override
    public void notifyUpdateCurrency(InnerMessage.NotifyData req, long pid) {
        try {
            LOGGER.info("notifyUpdateCurrency response type: {}", req.getType());
            Channel session = AgentGameMrg.getInstance().getUdpSessionMap().get(req.getUpdSessionId());
            BtiUtils.sendSuccessResponse(session, req.getBalance());
        } catch (Exception e) {
            LOGGER.error("updateCurrency error", e);
        } finally {
            AgentGameMrg.getInstance().removeUdpSessionMap(req.getUpdSessionId());
        }
    }
}