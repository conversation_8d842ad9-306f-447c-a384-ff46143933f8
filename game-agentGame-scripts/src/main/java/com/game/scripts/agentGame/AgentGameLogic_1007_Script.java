package com.game.scripts.agentGame;

import com.alibaba.fastjson.JSONObject;
import com.game.c_entity.middleplatform.C_BaseGamePlatform;
import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.enums.AgentGame;
import com.game.enums.ErrorCode;
import com.game.gamesr.main.AgentGameServer;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.manager.DataAgentGameMrg;
import com.game.gamesr.scripts.IAgentGameLogicScript;
import com.game.handler.http.agentGame_1007_funky.FunkyConstants;
import com.game.handler.http.agentGame_1007_funky.FunkyUtils;
import com.proto.HallMessage;
import com.proto.InnerMessage;
import com.proto.MIDMessage;
import io.netty.channel.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.TimeZone;


/**
 * Funky Games集成实现
 */
public class AgentGameLogic_1007_Script implements IAgentGameLogicScript {
    private static final Logger LOGGER = LoggerFactory.getLogger(AgentGameLogic_1007_Script.class);

    // 日期格式化器 - 根据官方文档，只包含日期部分 (yyyy-MM-dd)
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");

    static {
        DATE_FORMAT.setTimeZone(TimeZone.getTimeZone("UTC"));
    }

    @Override
    public boolean entryAgentGame(HallMessage.ReqEntryAgentGameMessage req, Player player, Channel session, long udpSessionId) {
        final HallMessage.ResEntryAgentGameMessage.Builder res = HallMessage.ResEntryAgentGameMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResEntryAgentGame_VALUE);
        try {
            final String platformGameId = req.getPlatformGameId(); // 对应 Funky 的 gameCode
            final String currency = req.getCurrencyName();

            // 设置语言
            String language = "EN";
            if (player.getLanguage() == 2) {
                language = "PT";
            }

            // 获取API配置
            final C_BaseGamePlatform c_baseGamePlatform = DataAgentGameMrg.getInstance()
                    .findC_BaseGamePlatformSupplierId(this.getClass().getSimpleName(), AgentGame.FUNKY.getType());
            String agent = c_baseGamePlatform.getLobby();
            String auth = c_baseGamePlatform.getSecretKey();
            String API_URL = c_baseGamePlatform.getApiUrl();

            // 构造玩家唯一用户名
            String userName = AgentGameMrg.getPlatformUserName(currency, player.getPlayerId());

            // 三方要求sessionId不能超过100
            // 创建 JWT 作为 Session ID
//            final FunkyJWTUtils.JWData jwData = new FunkyJWTUtils.JWData();
//            jwData.userId = player.getPlayerId();
//            jwData.expires = TimeUtil.currentTimeMillis() + 7 * TimeUtil.DAY;
//            final String sessionId = FunkyJWTUtils.jwtToken(jwData);
            final String sessionId = FunkyUtils.generateSessionId(90);
            LOGGER.info("sessionId len: {}", sessionId.length());
            LOGGER.info("sessionId: {}", sessionId);

            // 获取玩家 IP
            String playerIp = player.getIp() != null ? player.getIp() : "127.0.0.1";

            // 构建请求 JSON
            JSONObject params = new JSONObject();
            params.put("gameCode", platformGameId);
            params.put("userName", userName);
            params.put("playerId", String.valueOf(player.getPlayerId()));
            params.put("currency", currency);
            params.put("language", language);
            params.put("playerIp", playerIp);
            params.put("sessionId", sessionId);
            params.put("RedirectUrl", "");
//            params.put("IsTestAccount", !ConstantConfig.getInstance().isAgentGame());
            // todo 在 正式環境 測試時，請務必將 Launch Game API 中的 isTestAccount 參數設為 True，否則注單會被納入正式財務報表。
            params.put("IsTestAccount", true);

            LOGGER.info("!isAgentGame: {}", !ConstantConfig.getInstance().isAgentGame());

            LOGGER.info("!isAgentGame: {}", !ConstantConfig.getInstance().isAgentGame());

            // 打印请求头和请求体内容
            LOGGER.info("Sending request to URL: {}", API_URL);
            LOGGER.info("Request Headers: Content-Type: application/json;charset=UTF-8, User-Agent: {}, Authentication: {}", agent, auth);
            LOGGER.info("Request Body: {}", params.toJSONString());

            // 构造 HTTP 请求
            HttpRequest request = HttpRequest.newBuilder()
                    .timeout(Duration.ofSeconds(15))
                    .uri(URI.create(API_URL))
                    .version(HttpClient.Version.HTTP_1_1)
                    .header("Content-Type", "application/json;charset=UTF-8")
                    .header("User-Agent", agent)
                    .header("Authentication", auth)
                    .POST(HttpRequest.BodyPublishers.ofString(params.toJSONString()))
                    .build();

            // 发送请求
            HttpResponse<String> httpResponse = AgentGameServer.getInstance().getHttpClientMrg()
                    .send(request, HttpResponse.BodyHandlers.ofString());

            String response = httpResponse.body();
            LOGGER.info("Funky LaunchGame response: {}", response);

            // 解析响应
            JSONObject responseJson = JSONObject.parseObject(response);
            if (responseJson.getIntValue("errorCode") == 0) {
                JSONObject data = responseJson.getJSONObject("data");
                String gameUrl = data.getString("gameUrl");
                String token = data.getString("token");

                // 构建完整游戏URL
                String fullGameUrl = gameUrl + "?token=" + token;

                res.setGameUrl(fullGameUrl)
                        .setGameId(req.getGameId())
                        .setCurrencyId(req.getCurrencyId())
                        .setGameType(req.getGameType())
                        .setGameCurrencyId(req.getGameCurrencyId())
                        .setPlatformId(req.getPlatformId())
                        .setBonus(req.getBonus());

                MsgUtil.sendInnerMsg(session, res.build(), player.getPlayerId(), udpSessionId);
                return true;
            } else {
                LOGGER.warn("Funky LaunchGame failed, code: {}, message: {}", 
                        responseJson.getIntValue("errorCode"), 
                        responseJson.getString("errorMessage"));
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.sendInnerMsg(session, res.build(), player.getPlayerId(), udpSessionId);
                return false;
            }
        } catch (Exception e) {
            LOGGER.error("FunkyGames entryAgentGame error", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.sendInnerMsg(session, res.build(), player.getPlayerId(), udpSessionId);
            return false;
        }
    }

    @Override
    public void notifyUpdateCurrency(InnerMessage.NotifyData req, long pid) {
        try {
            switch (req.getType()) {
                case 1 -> handleBetUpdateCurrency(req);
                case 2 -> handleWinUpdateCurrency(req);
                case 3 -> handleCancelUpdateCurrency(req);
                default -> LOGGER.warn("Unknown updateCurrency type: {}", req.getType());
            }
        } catch (Exception e) {
            LOGGER.error("updateCurrency error", e);
        } finally {
            AgentGameMrg.getInstance().removeUdpSessionMap(req.getUpdSessionId());
        }
    }


    private void handleBetUpdateCurrency(InnerMessage.NotifyData req) {
        final Map<String, Object> responseMap = new LinkedHashMap<>();

        responseMap.put("errorCode", FunkyConstants.ErrorCode.SUCCESS.getCode());
        responseMap.put("errorMessage", FunkyConstants.ErrorCode.SUCCESS.getMessage());

        final JSONObject data = new JSONObject();
        data.put("balance", BigDecimalUtils.round(req.getBalance(), 2));
        responseMap.put("data", data);

        LOGGER.info("handleBetUpdateCurrency response: {}", responseMap);
        Channel session = AgentGameMrg.getInstance().getUdpSessionMap().get(req.getUpdSessionId());
        MsgUtil.responseHttp(responseMap, session);
    }

    private void handleWinUpdateCurrency(InnerMessage.NotifyData req) {
        final Map<String, Object> responseMap = new LinkedHashMap<>();

        responseMap.put("errorCode", FunkyConstants.ErrorCode.SUCCESS.getCode());
        responseMap.put("errorMessage", FunkyConstants.ErrorCode.SUCCESS.getMessage());

        // 8. 构建成功响应
        final JSONObject data = new JSONObject();
        data.put("refNo", req.getData());
        data.put("balance", BigDecimalUtils.round(req.getBalance(), 2));
        data.put("playerId", req.getPlayerName());
        data.put("currency", req.getCurrencyId());

        long timestamp = System.currentTimeMillis();
        data.put("statementDate", DATE_FORMAT.format(new Date(timestamp)));
        responseMap.put("data", data);

        LOGGER.info("handleWinUpdateCurrency response: {}", responseMap);
        Channel session = AgentGameMrg.getInstance().getUdpSessionMap().get(req.getUpdSessionId());
        MsgUtil.responseHttp(responseMap, session);
    }

    private void handleCancelUpdateCurrency(InnerMessage.NotifyData req) {
        final Map<String, Object> responseMap = new LinkedHashMap<>();

        responseMap.put("errorCode", FunkyConstants.ErrorCode.SUCCESS.getCode());
        responseMap.put("errorMessage", FunkyConstants.ErrorCode.SUCCESS.getMessage());

        // 8. 构建成功响应
        final JSONObject data = new JSONObject();
        data.put("refNo", req.getData());
        responseMap.put("data", data);

        LOGGER.info("handleCancelUpdateCurrency response: {}", responseMap);
        Channel session = AgentGameMrg.getInstance().getUdpSessionMap().get(req.getUpdSessionId());
        MsgUtil.responseHttp(responseMap, session);
    }
}