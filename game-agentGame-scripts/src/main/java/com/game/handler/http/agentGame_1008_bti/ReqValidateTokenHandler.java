package com.game.handler.http.agentGame_1008_bti;

import com.alibaba.fastjson.JSONObject;
import com.game.c_entity.middleplatform.C_BaseGamePlatform;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.HttpUtils11;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.enums.AgentGame;
import com.game.enums.Currency;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.manager.DataAgentGameMrg;
import com.game.gamesr.scripts.IAgentGameScript;
import com.game.gamesr.utils.JWTUtils;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;
import java.util.Map;

//http://127.0.0.1:8680/bti/ValidateToken
@IHandlerEntity(path = BtiConstants.Paths.VALIDATE_TOKEN, desc = "令牌验证")
public class ReqValidateTokenHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqValidateTokenHandler.class);

    @Override
    public void run() {
        try {
            final Map<String, Object> paramsMap = getParamsMap();
            LOGGER.info("ReqValidateTokenHandler，params：{}", JsonUtils.writeAsJson(paramsMap));

            final C_BaseGamePlatform c_baseGamePlatform = DataAgentGameMrg.getInstance()
                    .findC_BaseGamePlatformSupplierId(this.getClass().getSimpleName(), AgentGame.BTI.getType());

            // 没有设置就默认通过
            if (!c_baseGamePlatform.getIpBlacklist().isEmpty()) {
                String ip = MsgUtil.getClientIp(session);
                final boolean inWhiteList = ScriptLoader.getInstance().functionScript("AgentGameScript",
                        (IAgentGameScript script) -> script.isIpBlacklist(ip, AgentGame.BTI.getType()));
                if (!inWhiteList) {
                    LOGGER.warn("not in IP whitelist: {}", ip);
                    BtiUtils.sendErrorResponse(session, BtiConstants.ErrorCode.TOKEN_NOT_VALID, "server error");
                    return;
                }
            }

            final String authToken = (String) paramsMap.get("auth_token");
            if (StringUtil.isNullOrEmpty(authToken)) {
                LOGGER.warn("token not valid: {}", authToken);
                BtiUtils.sendErrorResponse(session, BtiConstants.ErrorCode.TOKEN_NOT_VALID);
                return;
            }

            final Map<String, Object> verify = BtiJWTUtils.verify(authToken);
            if (verify.isEmpty()) {
                LOGGER.warn("token not valid: {}", authToken);
                BtiUtils.sendErrorResponse(session, BtiConstants.ErrorCode.TOKEN_NOT_VALID);
                return;
            }

            LOGGER.warn("token: {}", authToken);

            final Player player;
            try {
                long playerIdLong = (long) verify.get("userId");
                player = AgentGameMrg.getInstance().findDbPlayer(playerIdLong);
                if (player == null) {
                    throw new NumberFormatException("Player not found");
                }
            } catch (NumberFormatException e) {
                LOGGER.warn("Invalid playerId or not found");
                BtiUtils.sendErrorResponse(session, BtiConstants.ErrorCode.CUSTOMER_NOT_FOUND);
                return;
            }

            boolean bonus = (boolean) verify.get("bonus");

            String currencyName = (String) verify.get("currency");
            LOGGER.info("verify currency: {}", currencyName);
            LOGGER.info("playerid: {}, currencyid: {}", player.getPlayerId(), player.getCurrencyId());

            Currency currency = Currency.valueOf(currencyName);

            // 单独查询余额带bonus
            player.setBonus(bonus);
            player.setPlayerCurrencyId(currency.getCurrencyId());
            player.setGameCurrencyId(currency.getCurrencyId());

            final double realBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.realBalance(player));

            final double currentBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.amountTransformUsd(player, realBalance));

            LOGGER.info("realBalance: {}, currentBalance: {}", realBalance, currentBalance);

            Map<String, Object> responseMap = new LinkedHashMap<>();
            responseMap.put("error_code", BtiConstants.ErrorCode.SUCCESS.getCode());
            responseMap.put("error_message", BtiConstants.ErrorCode.SUCCESS.getCode());
            responseMap.put("cust_id", BtiUtils.encodeCustId(player.getCurrencyId(), player.getPlayerId(), bonus));
            responseMap.put("cust_login", player.getPlayerId());
            responseMap.put("city", "city");
            // todo 先硬编码
            responseMap.put("country", "BR");
            responseMap.put("currency_code", currencyName);
            responseMap.put("balance", currentBalance);

            MsgUtil.responseHttp(BtiUtils.toTextPlain(responseMap), session, HttpUtils11.HTTP_CONTENT_TYPE_PLAIN);
        } catch (Exception e) {
            LOGGER.error("ReqValidateTokenHandler", e);
            BtiUtils.sendErrorResponse(session, BtiConstants.ErrorCode.TOKEN_NOT_VALID, "server error");
        }
    }
}
