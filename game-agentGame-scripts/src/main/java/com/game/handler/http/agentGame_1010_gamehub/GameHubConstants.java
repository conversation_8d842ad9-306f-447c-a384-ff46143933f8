package com.game.handler.http.agentGame_1010_gamehub;

/**
 * GameHub API 常量定义
 * 包含错误代码、路径等常量
 */
public class GameHubConstants {

    /**
     * GameHub API 错误代码枚举
     * 基于官方文档 v3.2 定义
     */
    public enum ErrorCode {
        // 成功：注意文档里成功响应 status=200，不走 error 结构
        SUCCESS(200, "Success", false, "continue"),

        // 通用错误
        ERR001(500, "Unknown error occurred.", true, "restart"),
        ERR002(500, "The session has timed out. Please login again to continue playing.", true, "restart"),
        ERR003(500, "Insufficient funds to place current wager. Please reduce the stake or add more funds to your balance.", true, "continue"),
        ERR004(500, "This wagering will exceed your wagering limitation. Please try a smaller amount or increase the limit.", true, "continue"),
        ERR005(500, "Player authentication failed.", true, "restart"),
        ERR006(500, "Unauthorized request.", false, "restart"),
        ERR008(500, "Unsupported currency.", true, "restart"),
        ERR009(500, "Bonus bet max restriction.", true, "continue");

        private final int status;
        private final String message;
        private final boolean display;
        private final String action;

        ErrorCode(int status, String message, boolean display, String action) {
            this.status = status;
            this.message = message;
            this.display = display;
            this.action = action;
        }

        public int getStatus() {
            return status;
        }

        public String getMessage() {
            return message;
        }

        public boolean isDisplay() {
            return display;
        }

        public String getAction() {
            return action;
        }
    }

    /**
     * GameHub API 请求路径常量
     * 可以在 Handler 上用 @IHandlerEntity(path = ...) 引用
     */
    public static class Paths {
        public static final String GAMEHUB = "/gamehub/";
    }
}
