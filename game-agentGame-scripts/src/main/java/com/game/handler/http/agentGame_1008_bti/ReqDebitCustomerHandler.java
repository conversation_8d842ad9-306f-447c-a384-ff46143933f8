package com.game.handler.http.agentGame_1008_bti;

import com.game.c_entity.middleplatform.C_BaseGamePlatform;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.MsgUtil;
import com.game.entity.game.GameNote;
import com.game.entity.player.Player;
import com.game.enums.AgentGame;
import com.game.enums.ChangeType;
import com.game.enums.Currency;
import com.game.gamesr.NotifyData;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.manager.DataAgentGameMrg;
import com.game.gamesr.scripts.IAgentGameScript;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.Map;

@IHandlerEntity(path = BtiConstants.Paths.DEBIT_CUSTOMER, desc = "重新结算回收彩金")
public class ReqDebitCustomerHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqDebitCustomerHandler.class);

    @Override
    public void run() {
        try {
            final Map<String, Object> paramsMap = getParamsMap();
            LOGGER.info("ReqDebitCustomerHandler，params：{}", JsonUtils.writeAsJson(paramsMap));

            final C_BaseGamePlatform c_baseGamePlatform = DataAgentGameMrg.getInstance()
                    .findC_BaseGamePlatformSupplierId(this.getClass().getSimpleName(), AgentGame.BTI.getType());

            // 没有设置就默认通过
            if (!c_baseGamePlatform.getIpBlacklist().isEmpty()) {
                String ip = MsgUtil.getClientIp(session);
                final boolean inWhiteList = ScriptLoader.getInstance().functionScript("AgentGameScript",
                        (IAgentGameScript script) -> script.isIpBlacklist(ip, AgentGame.BTI.getType()));
                if (!inWhiteList) {
                    LOGGER.warn("not in IP whitelist: {}", ip);
                    BtiUtils.sendErrorResponse(session, BtiConstants.ErrorCode.TOKEN_NOT_VALID, "server error");
                    return;
                }
            }

            final String custId = (String) paramsMap.get("cust_id");
            final String amount = (String) paramsMap.get("amount");
            final String reqId = (String) paramsMap.get("req_id");
            final String purchaseId = (String) paramsMap.get("purchase_id");

            final Player player = BtiUtils.getPlayer(custId);
            if (player == null) {
                BtiUtils.sendSuccessResponse(session, "Player not found", 0.00);
                return;
            }

            final double realBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.realBalance(player));

            final double currentBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.amountTransformUsd(player, realBalance));

            GameNote gameNote = AgentGameMrg.getInstance().findByTransactionId(purchaseId);
            if (gameNote == null) {
                LOGGER.warn("bet round id not found: {}", purchaseId);
                BtiUtils.sendSuccessResponse(session, "bet round id not found", currentBalance);
                return;
            }

            if (gameNote.getTransactionIds().contains(reqId)) {
                LOGGER.warn("Duplicate messages: {}", reqId);
                BtiUtils.sendSuccessResponse(session, "Duplicate messages", currentBalance);
                return;
            }

            final double rollbackAmountTmp = Double.parseDouble(amount);
            double rollbackAmount = Math.min(rollbackAmountTmp, currentBalance);

            final double rollBackValue = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.usdTransformAmount(player, rollbackAmount));

            double newBalance = BigDecimalUtils.sub(currentBalance, rollbackAmount, 2);

            final double finalBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.usdTransformAmount(player, newBalance));

            ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.updateGameNote(player, gameNote.getRoundId(), reqId, 5, rollBackValue, finalBalance));

            // 获取货币名称
            Currency currency = Currency.valueOf(gameNote.getCurrencyId());
            String currencyName = "";
            if (currency != null) {
                currencyName = currency.name();
            }

            final NotifyData notifyData = new NotifyData();
            notifyData.setType(ChangeType.Change.getType())
                    .setPid(player.getPlayerId())
                    .setPlayerName(player.getPlayerName())
                    .setNoteId(gameNote.getNoteId() + "")
                    .setCurrency(currencyName)
                    .setWin(rollBackValue)
                    .setUpdSessionId(MsgUtil.getSessionID(session))
                    .setGameId(gameNote.getGameId())
                    .setCurrencyId(gameNote.getCurrencyId());

            ScriptLoader.getInstance().consumerScript("AgentGameScript",
                    (IAgentGameScript script) -> script.updateCurrency(notifyData));
        } catch (Exception e) {
            LOGGER.error("ReqDebitCustomerHandler", e);
            BtiUtils.sendSuccessResponse(session, "server error", 0.00);
        }
    }

}
