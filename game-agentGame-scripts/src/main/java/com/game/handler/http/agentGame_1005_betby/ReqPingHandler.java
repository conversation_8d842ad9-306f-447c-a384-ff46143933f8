package com.game.handler.http.agentGame_1005_betby;

import com.game.c_entity.middleplatform.C_BaseGamePlatform;
import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.enums.AgentGame;
import com.game.gamesr.manager.DataAgentGameMrg;
import com.game.gamesr.scripts.IAgentGameScript;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;
import java.util.Map;

// DONE
@IHandlerEntity(path = "/betby/ping", desc = "健康检查")
public class ReqPingHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqPingHandler.class);

    @Override
    public void run() {
        final Map<String, Object> responseMap = new LinkedHashMap<>();
        try {
            if (ConstantConfig.getInstance().isAgentDebug()) {
                LOGGER.info("betby PING request");
            }

            final C_BaseGamePlatform c_baseGamePlatform = DataAgentGameMrg.getInstance()
                    .findC_BaseGamePlatformSupplierId(this.getClass().getSimpleName(), AgentGame.BETBY.getType());

            // 没有设置就默认通过
            if (!c_baseGamePlatform.getIpBlacklist().isEmpty()) {
                String ip = MsgUtil.getClientIp(session);
                final boolean inWhiteList = ScriptLoader.getInstance().functionScript("AgentGameScript",
                        (IAgentGameScript script) -> script.isIpBlacklist(ip, AgentGame.BETBY.getType()));
                if (!inWhiteList) {
                    LOGGER.warn("not in IP whitelist: {}", ip);
                    responseMap.put("code", 3000);
                    MsgUtil.responseHttp(responseMap, session);
                    return;
                }
            }

            // 返回当前时间戳
            responseMap.put("timestamp", TimeUtil.currentTimeMillis() / 1000); // 转换为秒级时间戳
            MsgUtil.responseHttp(responseMap, session);
        } catch (Exception e) {
            LOGGER.error("ReqPingHandler", e);
            // 根据实际需求处理错误情况
            responseMap.put("timestamp", TimeUtil.currentTimeMillis() / 1000);
            MsgUtil.responseHttp(responseMap, session);
        }
    }
}