package com.game.handler.http.agentGame_1002_st8;

import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.MsgUtil;
import com.game.entity.game.GameNote;
import com.game.entity.player.Player;
import com.game.enums.ChangeType;
import com.game.gamesr.NotifyData;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.scripts.IAgentGameScript;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;
import java.util.Map;

//http://127.0.0.1:8680/st8/debit
@IHandlerEntity(path = "/st8/debit", desc = "扣钱")
public class ReqDebitHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqDebitHandler.class);

    @Override
    public void run() {
        final Map<String, Object> responseMap = new LinkedHashMap<>();
        try {
            if (ConstantConfig.getInstance().isAgentDebug()) {
                LOGGER.info("ReqDebitHandler，params：{}", JsonUtils.writeAsJson(paramsMap));
            }

            paramsMap.remove("userAgent");

            final String players = (String) paramsMap.get("player");
            final String currency = (String) paramsMap.get("currency");
            final String round = (String) paramsMap.get("round");
            final String transaction_id = (String) paramsMap.get("transaction_id");
            final String amounts = (String) paramsMap.get("amount");


            final long playerId = AgentGameMrg.getInstance().getAgentGamePlayerId(players);
            final Player player = AgentGameMrg.getInstance().findDbPlayer(playerId);
            if (player == null) {
                responseMap.put("status", "error");
                responseMap.put("reason", "player_not_found");
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            final double realBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.realBalance(player));

            double balance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.amountTransformUsd(player, realBalance));

            GameNote gameNote = AgentGameMrg.getInstance().findRoundIdGameNote(player, round);
            if (gameNote != null && gameNote.getTransactionIds().contains(transaction_id)) {
                responseMap.put("status", "ok");
                responseMap.put("currency", currency);
                responseMap.put("balance", "" + BigDecimalUtils.round(balance, 2));
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            if (Double.parseDouble(amounts) == 0) {
                responseMap.put("status", "ok");
                responseMap.put("currency", currency);
                responseMap.put("balance", "" + BigDecimalUtils.round(balance, 2));
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            if (Double.parseDouble(amounts) > balance) {
                responseMap.put("status", "not_enough_money");
                responseMap.put("currency", currency);
                responseMap.put("balance", "" + BigDecimalUtils.round(balance, 2));
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            balance = BigDecimalUtils.sub(balance, Double.parseDouble(amounts), 3);

//            responseMap.put("status", "ok");
//            responseMap.put("currency", currency);
//            responseMap.put("balance", "" + BigDecimalUtils.round(balance, 2));
//            MsgUtil.responseHttp(responseMap, session);

            final double amount = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.usdTransformAmount(player, Double.parseDouble(amounts)));

            final double finalBalance = balance;
            final double balances = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.usdTransformAmount(player, finalBalance));

            if (gameNote == null) {
                gameNote = ScriptLoader.getInstance().functionScript("AgentGameScript",
                        (IAgentGameScript script) -> script.addGameNote(player, round, transaction_id, amount, amount, balances));
            }

            final NotifyData notifyData = new NotifyData();
            notifyData.setType(ChangeType.cost.getType())
                    .setPid(playerId)
                    .setNoteId(gameNote.getNoteId() + "")
                    .setBetAmount(amount)
                    .setCurrency(currency)
                    .setUpdSessionId(MsgUtil.getSessionID(session))
                    .setGameId(gameNote.getGameId())
                    .setCurrencyId(gameNote.getCurrencyId());

            ScriptLoader.getInstance().consumerScript("AgentGameScript",
                    (IAgentGameScript script) -> script.updateCurrency(notifyData));
        } catch (Exception e) {
            LOGGER.error("ReqDebitHandler", e);
            responseMap.put("status", "error");
            responseMap.put("reason", "unknown");
            MsgUtil.responseHttp(responseMap, session);
        }
    }

}
