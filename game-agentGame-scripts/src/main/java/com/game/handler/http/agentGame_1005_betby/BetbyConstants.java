package com.game.handler.http.agentGame_1005_betby;

import com.game.engine.utils.JsonUtils;
import com.game.entity.player.Player;
import com.game.enums.Currency;
import io.netty.buffer.Unpooled;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFutureListener;
import io.netty.handler.codec.http.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * 此文件汇总 Betby 相关的常量、枚举及参考信息。
 * 根据需要可将其拆分或合并到不同文件中。
 */
public class BetbyConstants {

    public enum TransactionOperation {
        BET("bet"), // 新投注的交易记录
        WIN("win"), // 中奖投注的交易记录（赢、提前兑现、退款、部分赢、部分输）
        REFUND("refund"), // 被取消投注的交易记录
        LOST("lost"), // 输掉投注的交易记录
        ROLLBACK("rollback"); // 回滚操作的交易记录

        private final String value;

        TransactionOperation(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static TransactionOperation fromValue(String value) {
            for (TransactionOperation op : TransactionOperation.values()) {
                if (op.getValue().equals(value)) {
                    return op;
                }
            }
            throw new IllegalArgumentException("Unknown value: " + value);
        }
    }


    public enum BetType {
        SELECTION("Selection", "Possible separate outcome"), // Selection - Possible separate outcome of the event.
        SINGLE("Single", "1/1"), // Single - 单注，缩写“1/1”
        COMBO("Combo", "N/N"), // Combo (accumulator) - 多注组合，缩写“N/N”
        SYSTEM("System", "N/M"), // System - 系统投注，缩写“N/M”
        TRIXIE("Trixie", "F/3"), // Trixie - 一种系统全覆盖投注，缩写“F/3”
        PATENT("Patent", "FS/3"), // Patent - 一种系统全覆盖（含单注）投注，缩写“FS/3”
        YANKEE("Yankee", "F/4"), // Yankee - 一种系统投注，缩写“F/4”
        LUCKY_15("Lucky 15", "FS/4"), // Lucky 15 - 一种系统投注，缩写“FS/4”
        CANADIAN("Canadian", "F/5"), // Canadian (Super Yankee) - 缩写“F/5”
        LUCKY_31("Lucky 31", "FS/5"), // Lucky 31 - 缩写“FS/5”
        HEINZ("Heinz", "F/6"), // Heinz - 一种系统投注，缩写“F/6”
        LUCKY_63("Lucky 63", "FS/6"), // Lucky 63 - 缩写“FS/6”
        SUPER_HEINZ("Super Heinz", "F/7"), // Super Heinz - 缩写“F/7”
        GOLIATH("Goliath", "F/8"); // Goliath - 缩写“F/8”

        private final String name;
        private final String abbreviation;

        BetType(String name, String abbreviation) {
            this.name = name;
            this.abbreviation = abbreviation;
        }

        public String getName() {
            return name;
        }

        public String getAbbreviation() {
            return abbreviation;
        }

        // 根据 name 值匹配 BetType 枚举
        public static BetType fromName(String name) {
            for (BetType type : values()) {
                if (type.getName().equals(name)) {
                    return type;
                }
            }
            throw new IllegalArgumentException("Unknown BetType name: " + name);
        }

        // 根据 abbreviation 值匹配 BetType 枚举
        public static BetType fromAbbreviation(String abbreviation) {
            for (BetType type : values()) {
                if (type.getAbbreviation().equals(abbreviation)) {
                    return type;
                }
            }
            throw new IllegalArgumentException("Unknown BetType abbreviation: " + abbreviation);
        }
    }


    public enum LanguageSupport {
        AR("ar", "Arabic"), // Arabic
        AZ("az", "Azerbaijan"), // Azerbaijan
        BG("bg", "Bulgarian"), // Bulgarian
        BS("bs", "Bosnian"), // Bosnian
        CS("cs", "Czech"), // Czech
        DA("da", "Danish"), // Danish
        DE("de", "German"), // German
        EL("el", "Greek"), // Greek
        EN("en", "English"), // English
        EN_GB("en-gb", "English"), // English
        EN_US("en-us", "English"), // English
        EN_CA("en-ca", "English"), // English
        EN_IN("en-in", "English"), // English
        EN_AU("en-au", "English + others locales if needed"), // English + others locales if needed
        ES("es", "Spanish"), // Spanish
        ES_VE("es-ve", "Spanish + others Latin America locales if needed"), // Spanish + others Latin America locales if needed
        ET("et", "Estonian"), // Estonian
        FI("fi", "Finnish"), // Finnish
        FIL("fil", "Filipino"), // Filipino
        FR("fr", "French"), // French
        FR_CA("fr-ca", "French"), // French
        HE("he", "Hebrew"), // Hebrew
        HI("hi", "Hindi"), // Hindi
        HR("hr", "Croatian"), // Croatian
        HU("hu", "Hungarian"), // Hungarian
        ID("id", "Indonesian"), // Indonesian
        IT("it", "Italian"), // Italian
        JA("ja", "Japanese"), // Japanese
        KA("ka", "Georgian"), // Georgian
        KK("kk", "Kazakh"), // Kazakh
        KO("ko", "Korean"), // Korean
        LT("lt", "Lithuanian"), // Lithuanian
        LV("lv", "Latvian"), // Latvian
        MK("mk", "Macedonian"), // Macedonian
        NL("nl", "Dutsch"), // Dutsch
        NO("no", "Norwegian"), // Norwegian
        PL("pl", "Polish"), // Polish
        PT("pt", "Portuguese"), // Portuguese
        PT_BR("pt-br", "Portuguese"), // Portuguese
        RO("ro", "Romanian"), // Romanian
        RU("ru", "Russian"), // Russian
        SK("sk", "Slovak"), // Slovak
        SL("sl", "Slovenian"), // Slovenian
        SQ("sq", "Albanian"), // Albanian
        SR("sr", "Serbian"), // Serbian
        SV("sv", "Swedish"), // Swedish
        TH("th", "Thai"), // Thai
        TR("tr", "Turkish"), // Turkish
        UK("uk", "Ukranian"), // Ukranian
        VI("vi", "Vietnamese"), // Vietnamese
        ZH("zh", "Chinese (simplified)"), // Chinese (simplified)
        ZH_HANT("zh-Hant", "Chinese (traditional)"); // Chinese (traditional)

        private final String code;
        private final String description;

        LanguageSupport(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        // 根据 code 值匹配 LanguageSupport 枚举
        public static LanguageSupport fromCode(String code) {
            for (LanguageSupport lang : values()) {
                if (lang.getCode().equals(code)) {
                    return lang;
                }
            }
            throw new IllegalArgumentException("Unknown language code: " + code);
        }
    }


    public enum CurrencySupport {
        AED("AED", "UAE Dirham"), // UAE Dirham
        AFN("AFN", "Afghan Afghani"), // Afghan Afghani
        ALL("ALL", "Albanian Lek"), // Albanian Lek
        AMD("AMD", "Armenian Dram"), // Armenian Dram
        ANG("ANG", "Netherlands Antillean Gulden"), // Netherlands Antillean Gulden
        AOA("AOA", "Angolan Kwanza"), // Angolan Kwanza
        ARS("ARS", "Argentine Peso"), // Argentine Peso
        AUD("AUD", "Australian Dollar"), // Australian Dollar
        AWG("AWG", "Aruban Florin"), // Aruban Florin
        AZN("AZN", "Azerbaijani Manat"), // Azerbaijani Manat
        BAM("BAM", "Bosnia And Herzegovina Konvertibilna Marka"), // Bosnia And Herzegovina Konvertibilna Marka
        BBD("BBD", "Barbadian Dollar"), // Barbadian Dollar
        BDT("BDT", "Bangladeshi Taka"), // Bangladeshi Taka
        BGN("BGN", "Bulgarian Lev"), // Bulgarian Lev
        BHD("BHD", "Bahraini Dinar"), // Bahraini Dinar
        BIF("BIF", "Burundi Franc"), // Burundi Franc
        BND("BND", "Brunei Dollar"), // Brunei Dollar
        BOB("BOB", "Bolivian Boliviano"), // Bolivian Boliviano
        BRL("BRL", "Brazilian Real"), // Brazilian Real
        BSD("BSD", "Bahamian Dollar"), // Bahamian Dollar
        BTN("BTN", "Bhutanese Ngultrum"), // Bhutanese Ngultrum
        BWP("BWP", "Botswana Pula"), // Botswana Pula
        BYN("BYN", "New Belarusian Ruble"), // New Belarusian Ruble
        BYR("BYR", "Belarusian Ruble"), // Belarusian Ruble
        BZD("BZD", "Belize Dollar"), // Belize Dollar
        CAD("CAD", "Canadian Dollar"), // Canadian Dollar
        CDF("CDF", "Congolese Franc"), // Congolese Franc
        CHF("CHF", "Swiss Franc"), // Swiss Franc
        CLP("CLP", "Chilean Peso"), // Chilean Peso
        CNY("CNY", "Chinese Yuan"), // Chinese Yuan
        COP("COP", "Colombian Peso"), // Colombian Peso
        CRC("CRC", "Costa Rican Colon"), // Costa Rican Colon
        CUP("CUP", "Cuban Peso"), // Cuban Peso
        CVE("CVE", "Cape Verdean Escudo"), // Cape Verdean Escudo
        CZK("CZK", "Czech Koruna"), // Czech Koruna
        DJF("DJF", "Djiboutian Franc"), // Djiboutian Franc
        DKK("DKK", "Danish Krone"), // Danish Krone
        DOP("DOP", "Dominican Peso"), // Dominican Peso
        DZD("DZD", "Algerian Dinar"), // Algerian Dinar
        EGP("EGP", "Egyptian Pound"), // Egyptian Pound
        ERN("ERN", "Eritrean Nakfa"), // Eritrean Nakfa
        ETB("ETB", "Ethiopian Birr"), // Ethiopian Birr
        EUR("EUR", "Euro"), // Euro
        FJD("FJD", "Fijian Dollar"), // Fijian Dollar
        FKP("FKP", "Falkland Islands Pound"), // Falkland Islands Pound
        GBP("GBP", "British Pound"), // British Pound
        GEL("GEL", "Georgian Lari"), // Georgian Lari
        GHS("GHS", "Ghanaian Cedi"), // Ghanaian Cedi
        GIP("GIP", "Gibraltar Pound"), // Gibraltar Pound
        GMD("GMD", "Gambian Dalasi"), // Gambian Dalasi
        GNF("GNF", "Guinean Franc"), // Guinean Franc
        GTQ("GTQ", "Guatemalan Quetzal"), // Guatemalan Quetzal
        GYD("GYD", "Guyanese Dollar"), // Guyanese Dollar
        HKD("HKD", "Hong Kong Dollar"), // Hong Kong Dollar
        HNL("HNL", "Honduran Lempira"), // Honduran Lempira
        HRK("HRK", "Croatian Kuna"), // Croatian Kuna
        HTG("HTG", "Haitian Gourde"), // Haitian Gourde
        HUF("HUF", "Hungarian Forint"), // Hungarian Forint
        IDR("IDR", "Indonesian Rupiah"), // Indonesian Rupiah
        ILS("ILS", "Israeli New Sheqel"), // Israeli New Sheqel
        INR("INR", "Indian Rupee"), // Indian Rupee
        IQD("IQD", "Iraqi Dinar"), // Iraqi Dinar
        IRR("IRR", "Iranian Rial"), // Iranian Rial
        ISK("ISK", "Icelandic Króna"), // Icelandic Króna
        JMD("JMD", "Jamaican Dollar"), // Jamaican Dollar
        JOD("JOD", "Jordanian Dinar"), // Jordanian Dinar
        JPY("JPY", "Japanese Yen"), // Japanese Yen
        KES("KES", "Kenyan Shilling"), // Kenyan Shilling
        KGS("KGS", "Kyrgyzstani Som"), // Kyrgyzstani Som
        KHR("KHR", "Cambodian Riel"), // Cambodian Riel
        KMF("KMF", "Comorian Franc"), // Comorian Franc
        KPW("KPW", "North Korean Won"), // North Korean Won
        KRW("KRW", "South Korean Won"), // South Korean Won
        KWD("KWD", "Kuwaiti Dinar"), // Kuwaiti Dinar
        KYD("KYD", "Cayman Islands Dollar"), // Cayman Islands Dollar
        KZT("KZT", "Kazakhstani Tenge"), // Kazakhstani Tenge
        LAK("LAK", "Lao Kip"), // Lao Kip
        LBP("LBP", "Lebanese Lira"), // Lebanese Lira
        LKR("LKR", "Sri Lankan Rupee"), // Sri Lankan Rupee
        LRD("LRD", "Liberian Dollar"), // Liberian Dollar
        LSL("LSL", "Lesotho Loti"), // Lesotho Loti
        LVL("LVL", "Latvian Lats"), // Latvian Lats
        LYD("LYD", "Libyan Dinar"), // Libyan Dinar
        MAD("MAD", "Moroccan Dirham"), // Moroccan Dirham
        MDL("MDL", "Moldovan Leu"), // Moldovan Leu
        MGA("MGA", "Malagasy Ariary"), // Malagasy Ariary
        MKD("MKD", "Macedonian Denar"), // Macedonian Denar
        MMK("MMK", "Myanma Kyat"), // Myanma Kyat
        MNT("MNT", "Mongolian Tugrik"), // Mongolian Tugrik
        MOP("MOP", "Macanese Pataca"), // Macanese Pataca
        MRO("MRO", "Mauritanian Ouguiya"), // Mauritanian Ouguiya
        MUR("MUR", "Mauritian Rupee"), // Mauritian Rupee
        MVR("MVR", "Maldivian Rufiyaa"), // Maldivian Rufiyaa
        MWK("MWK", "Malawian Kwacha"), // Malawian Kwacha
        MXN("MXN", "Mexican Peso"), // Mexican Peso
        MYR("MYR", "Malaysian Ringgit"), // Malaysian Ringgit
        MZN("MZN", "Mozambican Metical"), // Mozambican Metical
        NAD("NAD", "Namibian Dollar"), // Namibian Dollar
        NGN("NGN", "Nigerian Naira"), // Nigerian Naira
        NIO("NIO", "Nicaraguan Cordoba"), // Nicaraguan Cordoba
        NOK("NOK", "Norwegian Krone"), // Norwegian Krone
        NPR("NPR", "Nepalese Rupee"), // Nepalese Rupee
        NZD("NZD", "New Zealand Dollar"), // New Zealand Dollar
        OMR("OMR", "Omani Rial"), // Omani Rial
        PAB("PAB", "Panamanian Balboa"), // Panamanian Balboa
        PEN("PEN", "Peruvian Nuevo Sol"), // Peruvian Nuevo Sol
        PGK("PGK", "Papua New Guinean Kina"), // Papua New Guinean Kina
        PHP("PHP", "Philippine Peso"), // Philippine Peso
        PKR("PKR", "Pakistani Rupee"), // Pakistani Rupee
        PLN("PLN", "Polish Zloty"), // Polish Zloty
        PYG("PYG", "Paraguayan Guarani"), // Paraguayan Guarani
        QAR("QAR", "Qatari Riyal"), // Qatari Riyal
        RON("RON", "Romanian Leu"), // Romanian Leu
        RSD("RSD", "Serbian Dinar"), // Serbian Dinar
        RUB("RUB", "Russian Ruble"), // Russian Ruble
        RWF("RWF", "Rwandan Franc"), // Rwandan Franc
        SAR("SAR", "Saudi Riyal"), // Saudi Riyal
        SBD("SBD", "Solomon Islands Dollar"), // Solomon Islands Dollar
        SCR("SCR", "Seychellois Rupee"), // Seychellois Rupee
        SDG("SDG", "Sudanese Pound"), // Sudanese Pound
        SEK("SEK", "Swedish Krona"), // Swedish Krona
        SGD("SGD", "Singapore Dollar"), // Singapore Dollar
        SHP("SHP", "Saint Helena Pound"), // Saint Helena Pound
        SLL("SLL", "Sierra Leonean Leone"), // Sierra Leonean Leone
        SOS("SOS", "Somali Shilling"), // Somali Shilling
        SRD("SRD", "Surinamese Dollar"), // Surinamese Dollar
        STD("STD", "Sao Tome And Principe Dobra"), // Sao Tome And Principe Dobra
        SYP("SYP", "Syrian Pound"), // Syrian Pound
        SZL("SZL", "Swazi Lilangeni"), // Swazi Lilangeni
        THB("THB", "Thai Baht"), // Thai Baht
        TJS("TJS", "Tajikistani Somoni"), // Tajikistani Somoni
        TMT("TMT", "Turkmenistan Manat"), // Turkmenistan Manat
        TND("TND", "Tunisian Dinar"), // Tunisian Dinar
        TOP("TOP", "Paanga"), // Paanga
        TRY("TRY", "Turkish New Lira"), // Turkish New Lira
        TTD("TTD", "Trinidad and Tobago Dollar"), // Trinidad and Tobago Dollar
        TWD("TWD", "New Taiwan Dollar"), // New Taiwan Dollar
        TZS("TZS", "Tanzanian Shilling"), // Tanzanian Shilling
        UAH("UAH", "Ukrainian Hryvnia"), // Ukrainian Hryvnia
        UGX("UGX", "Ugandan Shilling"), // Ugandan Shilling
        USD("USD", "United States Dollar"), // United States Dollar
        UYU("UYU", "Uruguayan Peso"), // Uruguayan Peso
        UZS("UZS", "Uzbekistani Som"), // Uzbekistani Som
        VES("VES", "Venezuelan Bolivar"), // Venezuelan Bolivar
        VND("VND", "Vietnamese Dong"), // Vietnamese Dong
        VUV("VUV", "Vanuatu Vatu"), // Vanuatu Vatu
        WST("WST", "Samoan Tala"), // Samoan Tala
        XAF("XAF", "Central African CFA Franc"), // Central African CFA Franc
        XCD("XCD", "East Caribbean Dollar"), // East Caribbean Dollar
        XDR("XDR", "Special Drawing Rights"), // Special Drawing Rights
        XOF("XOF", "West African CFA Franc"), // West African CFA Franc
        XPF("XPF", "CFP Franc"), // CFP Franc
        YER("YER", "Yemeni Rial"), // Yemeni Rial
        ZAR("ZAR", "South African Rand"), // South African Rand
        ZMW("ZMW", "Zambian Kwacha"), // Zambian Kwacha

        // 可能不支持，需要用USD结算
        USDT("USDT", "USDT");

        private final String code;
        private final String description;

        CurrencySupport(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        // 根据代码匹配 CurrencySupport 枚举
        public static CurrencySupport fromCode(String code) {
            for (CurrencySupport currency : values()) {
                if (currency.getCode().equals(code)) {
                    return currency;
                }
            }
            throw new IllegalArgumentException("Unknown currency code: " + code);
        }
    }

    public enum ErrorCode {
        PLAYER_BANNED(1005),  // 玩家被封禁
        PLAYER_NOT_FOUND(1006),  // 未找到玩家
        SESSION_EXPIRED(1007),  // 会话已过期
        INSUFFICIENT_FUNDS(2001),  // 资金不足
        INVALID_CURRENCY(2002),  // 货币无效
        PARENT_TRANSACTION_NOT_FOUND(2003),  // 未找到父交易
        INVALID_REQUEST(2004),  // 错误请求
        INVALID_JWT_TOKEN(2005),  // 无效的 JWT 令牌
        BONUS_NOT_FOUND(3001),  // 找不到奖金
        PLAYER_LIMIT_EXCEEDED(4001),  // 玩家限制超出
        MAX_BET_LIMIT_EXCEEDED(4002);  // 超过最大奖金投注限额

        private final int code;  // 错误代码

        // 枚举构造函数，用于初始化错误代码
        ErrorCode(int code) {
            this.code = code;
        }

        // 获取错误代码
        public int getCode() {
            return code;
        }
    }

    public static class ValidationResult {
        private final boolean success;
        private final int errorCode;
        private final String errorMessage;
        private final Player player;

        private ValidationResult(boolean success, int errorCode, String errorMessage, Player player) {
            this.success = success;
            this.errorCode = errorCode;
            this.errorMessage = errorMessage;
            this.player = player;
        }

        public static ValidationResult success(Player player) {
            return new ValidationResult(true, 0, null, player);
        }

        public static ValidationResult failure(int errorCode, String errorMessage) {
            return new ValidationResult(false, errorCode, errorMessage, null);
        }

        public boolean isSuccess() { return success; }
        public int getErrorCode() { return errorCode; }
        public String getErrorMessage() { return errorMessage; }
        public Player getPlayer() { return player; }
    }

    public static void sendErrorResponse(Channel session) {
        final Map<String, Object> responseMap = new LinkedHashMap<>();
        sendErrorResponse(responseMap, session);
    }

    public static void sendErrorResponse(Channel session, int code, String message) {
        final Map<String, Object> errorMap = new LinkedHashMap<>();
        errorMap.put("code", code);
        errorMap.put("message", message);
        sendErrorResponse(errorMap, session);
    }

    public static void sendErrorResponse(Map<String, Object> params, Channel session) {
        String contentType = "application/json";

        String jsonData = JsonUtils.writeAsJson(params);
        FullHttpResponse response = new DefaultFullHttpResponse(HttpVersion.HTTP_1_1, HttpResponseStatus.BAD_REQUEST, Unpooled.wrappedBuffer(jsonData.getBytes(StandardCharsets.UTF_8)));
        response.headers().set(HttpHeaderNames.CONTENT_TYPE, contentType);
        response.headers().set(HttpHeaderNames.CONTENT_LENGTH, response.content().readableBytes());
        response.headers().set("Access-Control-Allow-Origin", "*");
        session.writeAndFlush(response).addListener(ChannelFutureListener.CLOSE);
    }

    public static String getPlayerId(String fullPlayerId) {
        if (fullPlayerId != null && fullPlayerId.contains("-")) {
            return fullPlayerId.split("-")[0];
        } else {
            // 如果没有"-"，就使用整个字符串
            return fullPlayerId;
        }
    }

    // 根据 CurrencySupport 的 code 来匹配对应的 Currency 枚举值
    public static Currency mapToCurrency(String code) {
        try {
            // 通过 code 获取 CurrencySupport 枚举值
            CurrencySupport support = CurrencySupport.fromCode(code);

            // 根据 CurrencySupport 的 description 映射到 Currency 的 currencyId
            switch (support) {
                case BRL:
                    return Currency.BRL;
                case INR:
                    return Currency.INR;
                case USD:
                    return Currency.USD;
                case MXN:
                    return Currency.MXN;
                case PHP:
                    return Currency.PHP;
                case USDT:
                    return Currency.USDT;
                // 添加其他 CurrencySupport 到 Currency 的映射关系
                default:
                    return null; // 返回null表示不支持
            }
        } catch (IllegalArgumentException e) {
            // 捕获CurrencySupport.fromCode可能抛出的IllegalArgumentException
            return null; // 返回null表示不支持
        }
    }

    /**
     * 根据货币ID获取对应的货币代码字符串
     *
     * @param currencyId 货币ID（如1000、1001等）
     * @return 对应的货币代码字符串（如"BRL"、"USD"等）
     * @throws IllegalArgumentException 如果找不到对应的Currency或无法映射到CurrencySupport
     */
    public static String getCurrencyCodeFromId(int currencyId) {
        // 根据currencyId查找对应的Currency枚举
        Currency currency = Currency.valueOf(currencyId);
        if (currency == null) {
            throw new IllegalArgumentException("No Currency found for ID: " + currencyId);
        }

        // 根据Currency枚举获取对应的CurrencySupport枚举
        CurrencySupport currencySupport;
        try {
            // 使用Currency的名称来匹配CurrencySupport
            currencySupport = CurrencySupport.valueOf(currency.name());
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Cannot map Currency " + currency + " to CurrencySupport", e);
        }

        // 返回CurrencySupport的code
        return currencySupport.getCode();
    }

    private static final Logger LOGGER = LoggerFactory.getLogger(BetbyConstants.class);

    /**
     * Win/Lost 接口的选择信息类
     */
    public static class SelectionRemarkInfo {
        /**
         * 选择ID
         */
        private String id;
        /**
         * 事件ID
         */
        private String eventId;
        /**
         * 状态 (won/lost/open)
         */
        private String status;

        public SelectionRemarkInfo(String id, String eventId, String status) {
            this.id = id;
            this.eventId = eventId;
            this.status = status;
        }

        // getter/setter
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        public String getEventId() { return eventId; }
        public void setEventId(String eventId) { this.eventId = eventId; }
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
    }



    /**
     * 为Win/Lost接口生成备注信息（统一方法）
     * @param selections 选择列表 (包含id, event_id, status)
     * @return 压缩的JSON字符串
     */
    public static String generateSelectionRemark(List<Map<String, Object>> selections) {
        try {
            List<SelectionRemarkInfo> selectionList = new ArrayList<>();

            if (selections != null && !selections.isEmpty()) {
                for (Map<String, Object> selection : selections) {
                    SelectionRemarkInfo selectionInfo = new SelectionRemarkInfo(
                        getStringValue(selection, "id"),
                        getStringValue(selection, "event_id"),
                        getStringValue(selection, "status")
                    );
                    selectionList.add(selectionInfo);
                }
            }

            String result = JsonUtils.writeAsJson(selectionList);
            LOGGER.info("Generated Selection remark: {}", result);
            return result;
        } catch (Exception e) {
            LOGGER.error("Error generating selection remark", e);
            return "";
        }
    }

    /**
     * 为Win接口生成备注信息
     */
    public static String generateWinRemark(List<Map<String, Object>> selections) {
        return generateSelectionRemark(selections);
    }

    /**
     * 为Lost接口生成备注信息
     */
    public static String generateLostRemark(List<Map<String, Object>> selections) {
        return generateSelectionRemark(selections);
    }

    /**
     * 从Map中安全获取字符串值
     */
    private static String getStringValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : "";
    }

    /**
     * 将SelectionItem对象转换为Map格式（便捷方法）
     * @param selections SelectionItem列表
     * @return Map格式的选择列表
     */
    public static List<Map<String, Object>> convertSelectionsToMap(List<?> selections) {
        List<Map<String, Object>> selectionsMapList = new ArrayList<>();
        for (Object selectionObj : selections) {
            try {
                Map<String, Object> selectionMap = new HashMap<>();

                // 使用反射获取字段值，支持不同Handler中的SelectionItem类
                Class<?> clazz = selectionObj.getClass();

                // 获取id字段
                try {
                    Object id = clazz.getMethod("getId").invoke(selectionObj);
                    selectionMap.put("id", id != null ? id.toString() : "");
                } catch (Exception e) {
                    LOGGER.warn("Failed to get id field from {}: {}", clazz.getSimpleName(), e.getMessage());
                    selectionMap.put("id", "");
                }

                // 获取eventId字段
                try {
                    Object eventId = clazz.getMethod("getEventId").invoke(selectionObj);
                    selectionMap.put("event_id", eventId != null ? eventId.toString() : "");
                } catch (Exception e) {
                    LOGGER.warn("Failed to get eventId field from {}: {}", clazz.getSimpleName(), e.getMessage());
                    selectionMap.put("event_id", "");
                }

                // 获取status字段（保留原始值）
                try {
                    Object status = clazz.getMethod("getStatus").invoke(selectionObj);
                    String statusStr = status != null ? status.toString() : "";
                    selectionMap.put("status", statusStr);
                } catch (Exception e) {
                    LOGGER.warn("Failed to get status field from {}: {}", clazz.getSimpleName(), e.getMessage());
                    selectionMap.put("status", "");
                }

                selectionsMapList.add(selectionMap);
            } catch (Exception e) {
                LOGGER.error("Error converting selection to map", e);
            }
        }
        return selectionsMapList;
    }
}
