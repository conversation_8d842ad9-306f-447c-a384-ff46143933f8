package com.game.handler.http.agentGame_1002_st8;

import com.game.engine.io.handler.HttpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.enums.ChangeType;
import com.game.gamesr.NotifyData;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.scripts.IAgentGameScript;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;
import java.util.Map;

//http://127.0.0.1:8680/st8/buyin
@IHandlerEntity(path = "/st8/buyin", desc = "购买")
public class ReqBuyInHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqBuyInHandler.class);

    @Override
    public void run() {
        final Map<String, Object> responseMap = new LinkedHashMap<>();
        try {
            LOGGER.info("ReqBuyInHandler，params：{}", JsonUtils.writeAsJson(paramsMap));

            paramsMap.remove("userAgent");

            final String players = (String) paramsMap.get("player");
            final String currency = (String) paramsMap.get("currency");
            final String amount = (String) paramsMap.get("amount");

            final long playerId = AgentGameMrg.getInstance().getAgentGamePlayerId(players);
            final Player player = AgentGameMrg.getInstance().findDbPlayer(playerId);
            if (player == null) {
                responseMap.put("status", "error");
                responseMap.put("reason", "player_not_found");
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            final double realBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.realBalance(player));

            double balance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.amountTransformUsd(player, realBalance));
            final double beforeBalance = balance;

            balance = BigDecimalUtils.sub(balance, Double.parseDouble(amount), 2);

            if (balance < 0) {
                responseMap.put("status", "not_enough_money");
                responseMap.put("currency", currency);
                responseMap.put("balance", "" + BigDecimalUtils.round(beforeBalance, 2));
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

//            responseMap.put("status", "ok");
//            responseMap.put("currency", currency);
//            responseMap.put("balance", BigDecimalUtils.round(balance, 2) + "");
//            MsgUtil.responseHttp(responseMap, session);

            final NotifyData notifyData = new NotifyData();
            notifyData.setType(ChangeType.cost.getType())
                    .setPid(playerId)
                    .setNoteId("")
                    .setBetAmount(Double.parseDouble(amount))
                    .setCurrency(currency)
                    .setUpdSessionId(MsgUtil.getSessionID(session))
                    .setGameId(player.getGameId())
                    .setCurrencyId(player.getPlayerCurrencyId());

            ScriptLoader.getInstance().consumerScript("AgentGameScript",
                    (IAgentGameScript script) -> script.updateCurrency(notifyData));
        } catch (Exception e) {
            LOGGER.error("ReqPayoutHandler", e);
            responseMap.put("status", "unknown");
            MsgUtil.responseHttp(responseMap, session);
        }
    }

}
