package com.game.handler.http.agentGame_1007_funky;

import com.alibaba.fastjson.JSONObject;
import com.game.c_entity.middleplatform.C_BaseGamePlatform;
import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.MsgUtil;
import com.game.entity.game.GameNote;
import com.game.entity.player.Player;
import com.game.enums.AgentGame;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.manager.DataAgentGameMrg;
import com.game.gamesr.scripts.IAgentGameScript;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.TimeZone;

@IHandlerEntity(path = FunkyConstants.Paths.CHECK_BET, desc = "Funky查询下注状态接口")
public class ReqCheckBetHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqCheckBetHandler.class);

    // 日期格式化器 - 根据官方文档，只包含日期部分 (yyyy-MM-dd)
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");

    static {
        DATE_FORMAT.setTimeZone(TimeZone.getTimeZone("UTC"));
    }

    @Override
    public void run() {
        try {
            // 获取请求参数
            final Map<String, Object> paramsMap = getParamsMap();
            if (ConstantConfig.getInstance().isAgentDebug()) {
                LOGGER.info("ReqCheckBetHandler params: {}", paramsMap);
            }

            // 1. 校验请求
            Player player = validateRequest(paramsMap);
            if (player == null) {
                return; // 校验失败，错误响应已发送
            }

            // 2. 处理业务逻辑
            processCheckBet(paramsMap, player);

        } catch (Exception e) {
            LOGGER.error("ReqCheckBetHandler error", e);
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 校验请求参数和权限
     *
     * @param paramsMap 请求参数
     * @return 校验通过返回Player对象，失败返回null
     */
    private Player validateRequest(Map<String, Object> paramsMap) {
        final C_BaseGamePlatform c_baseGamePlatform = DataAgentGameMrg.getInstance()
                .findC_BaseGamePlatformSupplierId(this.getClass().getSimpleName(), AgentGame.FUNKY.getType());

        // 没有设置就默认通过
        if (!c_baseGamePlatform.getIpBlacklist().isEmpty()) {
            String ip = MsgUtil.getClientIp(session);
            final boolean inWhiteList = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.isIpBlacklist(ip, AgentGame.FUNKY.getType()));
            if (!inWhiteList) {
                LOGGER.warn("not in IP whitelist: {}", ip);
                FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.INVALID_INPUT);
                return null;
            }
        }

        // 1. 验证请求头信息
        if (FunkyHeaderValidator.hasInvalidHeaders(paramsMap)) {
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.PLAYER_NOT_LOGIN);
            return null;
        }

        // 2. 获取请求体参数
        final String id = (String) paramsMap.get("id");
        final String playerId = (String) paramsMap.get("playerId");

        // 3. 验证必要参数
        if (id == null || id.trim().isEmpty()) {
            LOGGER.warn("Missing required id parameter");
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.INVALID_INPUT);
            return null;
        }

        if (playerId == null || playerId.trim().isEmpty()) {
            LOGGER.warn("Missing required playerId parameter");
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.INVALID_INPUT);
            return null;
        }

        if (FunkyUtils.isInvalidPlayerId(playerId)) {
            LOGGER.warn("Invalid playerId format: {} (must be at least 3 characters)", playerId);
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.INVALID_INPUT);
            return null;
        }

        // 4. 查找玩家
        long playerIdLong;
        try {
            playerIdLong = Long.parseLong(playerId.trim());
        } catch (NumberFormatException e) {
            LOGGER.warn("Invalid playerId format: {}", playerId);
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.INVALID_INPUT);
            return null;
        }

        final Player player = AgentGameMrg.getInstance().findDbPlayer(playerIdLong);
        if (player == null) {
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.PLAYER_NOT_LOGIN);
            return null;
        }

        return player;
    }

    /**
     * 处理查询下注状态业务逻辑
     *
     * @param paramsMap 请求参数
     * @param player 已验证的玩家对象
     */
    private void processCheckBet(Map<String, Object> paramsMap, Player player) {
        // 获取请求参数
        final String id = (String) paramsMap.get("id");

        // 查找下注记录
        GameNote gameNote = AgentGameMrg.getInstance().findRoundIdGameNote(player, id);
        if (gameNote == null) {
            LOGGER.warn("Bet not found: id={}, playerId={}", id, player.getPlayerId());
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.BET_WAS_NOT_FOUND);
            return;
        }

        final JSONObject data = new JSONObject();

        // 获取下注金额（转换为USD）
        final double stake = ScriptLoader.getInstance().functionScript("AgentGameScript",
                (IAgentGameScript script) -> script.amountTransformUsd(player, gameNote.getBetAmount()));

        // 获取赢取金额（转换为USD）
        final double winAmount = ScriptLoader.getInstance().functionScript("AgentGameScript",
                (IAgentGameScript script) -> script.amountTransformUsd(player, gameNote.getWin()));

        data.put("stake", BigDecimalUtils.round(stake, 2));
        data.put("winAmount", BigDecimalUtils.round(winAmount, 2));

        // 确定下注状态
        String status = determineBetStatus(gameNote, stake, winAmount);
        data.put("status", status);

        // 设置结算日期
        String statementDate = getStatementDate(gameNote, status);
        data.put("statementDate", statementDate);

        LOGGER.info("ReqCheckBetHandler response data: {}", data);

        // 发送成功响应
        FunkyUtils.sendSuccessResponse(session, data);
    }

    /**
     * 确定下注状态
     * R: 下注仍在进行中
     * W: 玩家赢了
     * L: 玩家输了
     * C: 下注被取消
     * D: 平局，玩家输赢为0
     */
    private String determineBetStatus(GameNote gameNote, double stake, double winAmount) {
        // 检查是否被取消
        if (gameNote.getStatus() == 3) { // 假设3表示取消状态
            return "C";
        }
        
        // 检查是否已结算
        if (gameNote.getStatus() == 2) { // 假设2表示已结算
            if (winAmount > stake) {
                return "W"; // 玩家赢了
            } else if (winAmount < stake) {
                return "L"; // 玩家输了
            } else {
                return "D"; // 平局
            }
        }
        
        // 默认为进行中
        return "R";
    }

    /**
     * 获取结算日期
     * 根据官方文档：格式为 yyyy-MM-dd，只包含日期部分
     * 对于R(运行中)和C(取消)状态，返回null
     * 对于其他状态，返回结算日期
     */
    private String getStatementDate(GameNote gameNote, String status) {
        if ("R".equals(status) || "C".equals(status)) {
            return null;
        }

        // 返回结算时间，如果没有则返回创建时间
        long timestamp = gameNote.getCreateTime();
        return DATE_FORMAT.format(new Date(timestamp));
    }


}
