package com.game.handler.http.agentGame_1008_bti;

import com.game.c_entity.middleplatform.C_BaseGamePlatform;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.MsgUtil;
import com.game.entity.game.GameNote;
import com.game.entity.player.Player;
import com.game.enums.AgentGame;
import com.game.enums.ChangeType;
import com.game.enums.Currency;
import com.game.gamesr.NotifyData;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.manager.DataAgentGameMrg;
import com.game.gamesr.scripts.IAgentGameScript;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

@IHandlerEntity(path = BtiConstants.Paths.CREDIT_CUSTOMER, desc = "结算派彩")
public class ReqCreditCustomerHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqCreditCustomerHandler.class);

    @Override
    public void run() {
        try {
            final Map<String, Object> paramsMap = getParamsMap();
            LOGGER.info("ReqCreditCustomerHandler，params：{}", JsonUtils.writeAsJson(paramsMap));

            final C_BaseGamePlatform c_baseGamePlatform = DataAgentGameMrg.getInstance()
                    .findC_BaseGamePlatformSupplierId(this.getClass().getSimpleName(), AgentGame.BTI.getType());

            // 没有设置就默认通过
            if (!c_baseGamePlatform.getIpBlacklist().isEmpty()) {
                String ip = MsgUtil.getClientIp(session);
                final boolean inWhiteList = ScriptLoader.getInstance().functionScript("AgentGameScript",
                        (IAgentGameScript script) -> script.isIpBlacklist(ip, AgentGame.BTI.getType()));
                if (!inWhiteList) {
                    LOGGER.warn("not in IP whitelist: {}", ip);
                    BtiUtils.sendErrorResponse(session, BtiConstants.ErrorCode.TOKEN_NOT_VALID, "server error");
                    return;
                }
            }

            final String custId = (String) paramsMap.get("cust_id");
            final String amount = (String) paramsMap.get("amount");
            final String purchaseId = (String) paramsMap.get("purchase_id");
            final String reqId = (String) paramsMap.get("req_id");
            final String dataXml = (String) paramsMap.get("data");

            final Player player = BtiUtils.getPlayer(custId);
            if (player == null) {
                BtiUtils.sendSuccessResponse(session, "Player not found", 0.00);
                return;
            }

            final double realBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.realBalance(player));

            final double currentBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.amountTransformUsd(player, realBalance));

            GameNote gameNote = AgentGameMrg.getInstance().findByTransactionId(purchaseId);
            if (gameNote == null) {
                LOGGER.warn("bet round id not found: {}", purchaseId);
                BtiUtils.sendSuccessResponse(session, "bet round id not found", currentBalance);
                return;
            }

            if (Double.parseDouble(amount) <= 0) {
                LOGGER.warn("Not enough balance : {}", purchaseId);
                BtiUtils.sendSuccessResponse(session, "Not enough balance", currentBalance);
                return;
            }

            if (gameNote.getTransactionIds().contains(reqId)) {
                LOGGER.warn("Duplicate messages: {}", reqId);
                BtiUtils.sendSuccessResponse(session, "Duplicate messages", currentBalance);
                return;
            }

            final double winAmount = Double.parseDouble(amount);
            final double winValue = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.usdTransformAmount(player, winAmount));

            double newBalance = BigDecimalUtils.add(currentBalance, winAmount, 2);

            final double finalBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.usdTransformAmount(player, newBalance));

            // 需要与 generateSelectionRemark 一致的 String JSON 时：
            String betRemark = BtiCreditRemarkMapper.buildSelectionRemarkFromCreditXmlSafe(dataXml);
            LOGGER.info("betRemark：{}", betRemark);

            // 更新游戏记录，添加当前交易ID和remark信息
            GameNote gameNote2 = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.updateGameNote(player, gameNote.getRoundId(), reqId, 2, winValue, finalBalance, betRemark));

            // 获取货币名称
            Currency currency = Currency.valueOf(gameNote.getCurrencyId());
            String currencyName = "";
            if (currency != null) {
                currencyName = currency.name();
            }

            final NotifyData notifyData = new NotifyData();
            notifyData.setType(ChangeType.win.getType())
                    .setPid(player.getPlayerId())
                    .setPlayerName(player.getPlayerName())
                    .setNoteId(gameNote.getNoteId() + "")
                    .setCurrency(currencyName)
                    .setBetAmount(gameNote2.getBetAmount())
                    .setValidBets(gameNote2.getValidBets())
                    .setWin(winValue)
                    .setTotalWin(winValue)
                    .setUpdSessionId(MsgUtil.getSessionID(session))
                    .setGameId(gameNote.getGameId())
                    .setCurrencyId(gameNote.getCurrencyId());

            // 执行货币更新并发送响应
            ScriptLoader.getInstance().consumerScript("AgentGameScript",
                    (IAgentGameScript script) -> script.updateCurrency(notifyData));
        } catch (Exception e) {
            LOGGER.error("ReqCreditCustomerHandler", e);
            BtiUtils.sendSuccessResponse(session, "server error", 0.00);
        }
    }

}
