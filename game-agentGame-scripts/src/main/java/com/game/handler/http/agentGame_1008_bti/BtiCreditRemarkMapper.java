package com.game.handler.http.agentGame_1008_bti;

import com.game.engine.utils.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.w3c.dom.*;

import javax.xml.XMLConstants;
import javax.xml.parsers.DocumentBuilderFactory;
import java.nio.charset.StandardCharsets;
import java.util.*;

public final class BtiCreditRemarkMapper {
    private static final Logger LOGGER = LoggerFactory.getLogger(BtiCreditRemarkMapper.class);
    private BtiCreditRemarkMapper() {}

    /**
     * 规则（无回退）：
     *   event_id = Selection@MasterEventID（取不到 -> ""）
     *   对“最新 Change”下的每一个 <Bet> 产一条：
     *     id     = Bet@ID（取不到 -> ""）
     *     status = Bet@NewStatus 归一化为 won/lost/open（取不到 -> "open"）
     */
    public static List<Map<String, Object>> buildSelectionMapsFromCreditXmlSafe(String xml) {
        if (xml == null || xml.isBlank()) {
            LOGGER.warn("BTI Credit XML is null/blank");
            return Collections.emptyList();
        }
        try {
            DocumentBuilderFactory f = DocumentBuilderFactory.newInstance();
            // 安全：防 XXE
            f.setNamespaceAware(false);
            f.setExpandEntityReferences(false);
            f.setXIncludeAware(false);
            f.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
            f.setFeature("http://xml.org/sax/features/external-general-entities", false);
            f.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
            f.setFeature(XMLConstants.FEATURE_SECURE_PROCESSING, true);
            try {
                f.setAttribute(XMLConstants.ACCESS_EXTERNAL_DTD, "");
                f.setAttribute(XMLConstants.ACCESS_EXTERNAL_SCHEMA, "");
            } catch (IllegalArgumentException ignore) {}
            f.setFeature("http://apache.org/xml/features/nonvalidating/load-external-dtd", false);

            Document doc = f.newDocumentBuilder()
                    .parse(new java.io.ByteArrayInputStream(xml.getBytes(StandardCharsets.UTF_8)));
            Element root = doc.getDocumentElement();

            List<Map<String, Object>> out = new ArrayList<>();
            NodeList selectionNodes = root.getElementsByTagName("Selection");
            for (int i = 0; i < selectionNodes.getLength(); i++) {
                Element sel = (Element) selectionNodes.item(i);

                String eventId = attr(sel, "MasterEventID");      // 只取 MasterEventID
                if (eventId.isEmpty()) eventId = "";

                // 最新 Change（按 DOM 顺序取最后一个）
                Element latestChange = lastElementByTagName(sel, "Change");
                if (latestChange == null) continue;

                // 该 Change 下的所有 Bet 都产一条
                NodeList bets = latestChange.getElementsByTagName("Bet");
                for (int b = 0; b < bets.getLength(); b++) {
                    Element bet = (Element) bets.item(b);
                    String id = attr(bet, "ID");
                    String status = normalizeStatus(attr(bet, "NewStatus"));

                    Map<String, Object> m = new LinkedHashMap<>();
                    m.put("id", id);
                    m.put("event_id", eventId);
                    m.put("status", status);
                    out.add(m);

                    if (LOGGER.isDebugEnabled()) {
                        LOGGER.debug("selection -> id='{}', event_id='{}', bet.NewStatus='{}' => status='{}'",
                                id, eventId, attr(bet, "NewStatus"), status);
                    }
                }
            }
            return out;
        } catch (Exception e) {
            LOGGER.warn("BTI Credit XML parse error: {}", e.toString());
            return Collections.emptyList();
        }
    }

    /** 单份 <Credit> XML -> 字符串 JSON：[{id,event_id,status}, ...] */
    public static String buildSelectionRemarkFromCreditXmlSafe(String xml) {
        try {
            List<Map<String, Object>> list = buildSelectionMapsFromCreditXmlSafe(xml);
            if (list == null || list.isEmpty()) return "[]";
            List<Map<String, String>> out = new ArrayList<>(list.size());
            for (Map<String, Object> sel : list) {
                Map<String, String> m = new LinkedHashMap<>();
                m.put("id",       sval(sel, "id"));
                m.put("event_id", sval(sel, "event_id"));
                m.put("status",   sval(sel, "status"));
                out.add(m);
            }
            return JsonUtils.writeAsJson(out);
        } catch (Exception e) {
            LOGGER.warn("buildSelectionRemarkFromCreditXmlSafe error: {}", e.toString());
            return "[]";
        }
    }

    // —— 工具 —— //
    private static Element lastElementByTagName(Element base, String tag) {
        if (base == null) return null;
        NodeList nodes = base.getElementsByTagName(tag);
        if (nodes.getLength() == 0) return null;
        return (Element) nodes.item(nodes.getLength() - 1);
    }
    private static String attr(Element e, String name) {
        if (e == null || name == null) return "";
        String v = e.getAttribute(name);
        return v == null ? "" : v.trim();
    }
    /** 极简归一：won/lost/open */
    private static String normalizeStatus(String raw) {
        if (raw == null || raw.isBlank()) return "open";
        String s = raw.trim().toLowerCase(Locale.ROOT);
        if (s.equals("won") || s.contains("won") || s.contains("win")) return "won";
        if (s.equals("lost") || s.contains("lost") || s.contains("lose")) return "lost";
        return "open";
    }
    private static String sval(Map<String, Object> m, String k) {
        Object v = (m == null ? null : m.get(k));
        return v == null ? "" : String.valueOf(v);
    }
}
