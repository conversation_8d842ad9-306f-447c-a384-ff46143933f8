package com.game.handler.http.agentGame_1007_funky;

import com.alibaba.fastjson.JSONObject;
import com.game.c_entity.middleplatform.C_BaseGamePlatform;
import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.MsgUtil;
import com.game.entity.game.GameNote;
import com.game.entity.player.Player;
import com.game.enums.AgentGame;
import com.game.enums.ChangeType;

import com.game.enums.Currency;
import com.game.gamesr.NotifyData;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.manager.DataAgentGameMrg;
import com.game.gamesr.scripts.IAgentGameScript;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;
import java.util.Map;

@IHandlerEntity(path = FunkyConstants.Paths.PLACE_BET, desc = "Funky下注接口")
public class ReqPlaceBetHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqPlaceBetHandler.class);

    /**
     * 下注请求数据 - 严格按照官方文档定义
     */
    private static class PlaceBetRequest {
        /**
         * bet对象
         */
        private BetInfo bet;
        /**
         * 玩家IP地址
         */
        private String playerIp;
        /**
         * 用户会话ID
         */
        private String sessionId;
        /**
         * 玩家唯一ID
         */
        private String playerId;
        /**
         * X-Request-ID (从HTTP头中获取)
         */
        private String xRequestId;

        // getter/setter
        public BetInfo getBet() { return bet; }
        public void setBet(BetInfo bet) { this.bet = bet; }
        public String getPlayerIp() { return playerIp; }
        public void setPlayerIp(String playerIp) { this.playerIp = playerIp; }
        public String getSessionId() { return sessionId; }
        public void setSessionId(String sessionId) { this.sessionId = sessionId; }
        public String getPlayerId() { return playerId; }
        public void setPlayerId(String playerId) { this.playerId = playerId; }
        public String getXRequestId() { return xRequestId; }
        public void setXRequestId(String xRequestId) { this.xRequestId = xRequestId; }
    }

    /**
     * bet对象定义 - 严格按照官方文档
     */
    private static class BetInfo {
        /**
         * 游戏代码，用于识别游戏
         */
        private String gameCode;
        /**
         * 投注的参考号，始终唯一
         */
        private String refNo;
        /**
         * 投注金额
         */
        private Double stake;
        /**
         * 游戏提供商（可选）
         */
        private String gameProvider;
        /**
         * 如果玩家使用代金券，填入代金券的唯一ID（可选）
         */
        private String voucherId;

        // getter/setter
        public String getGameCode() { return gameCode; }
        public void setGameCode(String gameCode) { this.gameCode = gameCode; }
        public String getRefNo() { return refNo; }
        public void setRefNo(String refNo) { this.refNo = refNo; }
        public Double getStake() { return stake; }
        public void setStake(Double stake) { this.stake = stake; }
        public String getGameProvider() { return gameProvider; }
        public void setGameProvider(String gameProvider) { this.gameProvider = gameProvider; }
        public String getVoucherId() { return voucherId; }
        public void setVoucherId(String voucherId) { this.voucherId = voucherId; }
    }

    /**
     * 校验结果类
     */
    private static class ValidationResult {
        private boolean success;
        private Player player;
        private FunkyConstants.ErrorCode errorCode;
        private String errorMessage;

        private ValidationResult(boolean success, Player player, FunkyConstants.ErrorCode errorCode, String errorMessage) {
            this.success = success;
            this.player = player;
            this.errorCode = errorCode;
            this.errorMessage = errorMessage;
        }

        public static ValidationResult success(Player player) {
            return new ValidationResult(true, player, null, null);
        }

        public static ValidationResult failure(FunkyConstants.ErrorCode errorCode, String errorMessage) {
            return new ValidationResult(false, null, errorCode, errorMessage);
        }

        public boolean isSuccess() { return success; }
        public Player getPlayer() { return player; }
        public FunkyConstants.ErrorCode getErrorCode() { return errorCode; }
        public String getErrorMessage() { return errorMessage; }
    }

    @Override
    public void run() {
        try {
            // 获取请求参数
            final Map<String, Object> paramsMap = getParamsMap();
            if (ConstantConfig.getInstance().isAgentDebug()) {
                LOGGER.info("ReqPlaceBetHandler params: {}", paramsMap);
            }

            // 1. 解析请求
            PlaceBetRequest request = parseRequest(paramsMap);
            if (request == null) {
                FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.INVALID_INPUT);
                return;
            }

            // 2. 校验请求
            ValidationResult validationResult = validateRequest(request);
            if (!validationResult.isSuccess()) {
                FunkyUtils.sendErrorResponse(session, validationResult.getErrorCode());
                return;
            }

            Player player = validationResult.getPlayer();

            // 3. 处理业务逻辑
            processPlaceBet(request, player);

        } catch (Exception e) {
            LOGGER.error("ReqPlaceBetHandler error", e);
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 解析请求参数
     *
     * @param paramsMap 请求参数
     * @return 解析成功返回PlaceBetRequest对象，失败返回null
     */
    private PlaceBetRequest parseRequest(Map<String, Object> paramsMap) {
        try {
            PlaceBetRequest request = new PlaceBetRequest();

            // 解析基本参数
            request.setPlayerId((String) paramsMap.get("playerId"));
            request.setSessionId((String) paramsMap.get("sessionId"));
            request.setPlayerIp((String) paramsMap.get("playerIp"));

            // 从HTTP头中获取X-Request-ID
            request.setXRequestId((String) paramsMap.get("xRequestId"));

            // 解析bet对象 - bet对象是必需的
            @SuppressWarnings("unchecked")
            final Map<String, Object> betMap = (Map<String, Object>) paramsMap.get("bet");
            if (betMap == null) {
                LOGGER.warn("Missing required bet object");
                return null;
            }

            BetInfo betInfo = new BetInfo();

            // gameCode是必需的
            String gameCode = (String) betMap.get("gameCode");
            if (gameCode == null) {
                LOGGER.warn("Missing required gameCode in bet object");
                return null;
            }
            betInfo.setGameCode(gameCode);

            // refNo是必需的
            String refNo = (String) betMap.get("refNo");
            if (refNo == null) {
                LOGGER.warn("Missing required refNo in bet object");
                return null;
            }
            betInfo.setRefNo(refNo);

            // stake是必需的
            Object stakeObj = betMap.get("stake");
            if (stakeObj == null) {
                LOGGER.warn("Missing required stake in bet object");
                return null;
            }

            try {
                if (stakeObj instanceof Number) {
                    betInfo.setStake(((Number) stakeObj).doubleValue());
                } else if (stakeObj instanceof String) {
                    betInfo.setStake(Double.parseDouble((String) stakeObj));
                } else {
                    LOGGER.warn("Invalid stake type: {}", stakeObj.getClass().getSimpleName());
                    return null;
                }
            } catch (NumberFormatException e) {
                LOGGER.warn("Invalid stake format: {}", stakeObj);
                return null;
            }

            // 可选字段
            betInfo.setGameProvider((String) betMap.get("gameProvider"));
            betInfo.setVoucherId((String) betMap.get("voucherId"));

            request.setBet(betInfo);
            return request;

        } catch (Exception e) {
            LOGGER.error("Error parsing request", e);
            return null;
        }
    }

    /**
     * 校验请求参数和权限
     *
     * @param request 解析后的请求对象
     * @return 校验结果
     */
    private ValidationResult validateRequest(PlaceBetRequest request) {
        try {
            final C_BaseGamePlatform c_baseGamePlatform = DataAgentGameMrg.getInstance()
                    .findC_BaseGamePlatformSupplierId(this.getClass().getSimpleName(), AgentGame.FUNKY.getType());

            // 没有设置就默认通过
            if (!c_baseGamePlatform.getIpBlacklist().isEmpty()) {
                String ip = MsgUtil.getClientIp(session);
                final boolean inWhiteList = ScriptLoader.getInstance().functionScript("AgentGameScript",
                        (IAgentGameScript script) -> script.isIpBlacklist(ip, AgentGame.FUNKY.getType()));
                if (!inWhiteList) {
                    LOGGER.warn("not in IP whitelist: {}", ip);
                    return ValidationResult.failure(FunkyConstants.ErrorCode.INVALID_INPUT, "not in ip whitelist");
                }
            }

            // 1. 验证请求头信息
            final Map<String, Object> paramsMap = getParamsMap();
            if (FunkyHeaderValidator.hasInvalidHeaders(paramsMap)) {
                return ValidationResult.failure(FunkyConstants.ErrorCode.PLAYER_NOT_LOGIN, "Invalid headers");
            }

            // 2. 验证必要参数
            if (request.getPlayerId() == null || request.getPlayerId().trim().isEmpty()) {
                LOGGER.warn("Missing required playerId parameter");
                return ValidationResult.failure(FunkyConstants.ErrorCode.INVALID_INPUT, "Missing playerId");
            }

            if (FunkyUtils.isInvalidPlayerId(request.getPlayerId())) {
                LOGGER.warn("Invalid playerId format: {} (must be at least 3 characters)", request.getPlayerId());
                return ValidationResult.failure(FunkyConstants.ErrorCode.INVALID_INPUT, "Invalid playerId format");
            }

            if (request.getSessionId() == null || request.getSessionId().trim().isEmpty()) {
                LOGGER.warn("Missing required sessionId parameter");
                return ValidationResult.failure(FunkyConstants.ErrorCode.INVALID_INPUT, "Missing sessionId");
            }

            // 3. 验证bet对象
            if (request.getBet() == null) {
                LOGGER.warn("Missing required bet parameter");
                return ValidationResult.failure(FunkyConstants.ErrorCode.INVALID_INPUT, "Missing bet object");
            }

            BetInfo bet = request.getBet();
            if (bet.getGameCode() == null || bet.getGameCode().trim().isEmpty()) {
                LOGGER.warn("Missing required gameCode parameter");
                return ValidationResult.failure(FunkyConstants.ErrorCode.INVALID_INPUT, "Missing gameCode");
            }

            if (bet.getRefNo() == null || bet.getRefNo().trim().isEmpty()) {
                LOGGER.warn("Missing required refNo parameter");
                return ValidationResult.failure(FunkyConstants.ErrorCode.INVALID_INPUT, "Missing refNo");
            }

            if (bet.getStake() == null || bet.getStake() < 0) {
                LOGGER.warn("Invalid stake: {}", bet.getStake());
                return ValidationResult.failure(FunkyConstants.ErrorCode.INVALID_INPUT, "Invalid stake");
            }

            // 4. 查找玩家
            long playerIdLong;
            try {
                playerIdLong = Long.parseLong(request.getPlayerId().trim());
            } catch (NumberFormatException e) {
                LOGGER.warn("Invalid playerId format: {}", request.getPlayerId());
                return ValidationResult.failure(FunkyConstants.ErrorCode.INVALID_INPUT, "Invalid playerId format");
            }

            final Player player = AgentGameMrg.getInstance().findDbPlayer(playerIdLong);
            if (player == null) {
                return ValidationResult.failure(FunkyConstants.ErrorCode.PLAYER_NOT_LOGIN, "Player not found");
            }

            return ValidationResult.success(player);

        } catch (Exception e) {
            LOGGER.error("Validation error", e);
            return ValidationResult.failure(FunkyConstants.ErrorCode.INTERNAL_SERVER_ERROR, "Validation error");
        }
    }

    /**
     * 处理下注业务逻辑
     *
     * @param request 已验证的下注请求
     * @param player 已验证的玩家对象
     */
    private void processPlaceBet(PlaceBetRequest request, Player player) {
        // 记录X-Request-ID用于日志追踪
        LOGGER.info("Processing PlaceBet request - X-Request-ID: {}, refNo: {}, playerId: {}",
            request.getXRequestId(), request.getBet().getRefNo(), request.getPlayerId());

        // 2. 检查是否存在重复下注
        GameNote gameNote = AgentGameMrg.getInstance().findRoundIdGameNote(player, request.getBet().getRefNo());
        if (gameNote != null && gameNote.getTransactionIds().contains(request.getXRequestId())) {
            // 如果收到重复refNo的请求，需返回首次响应该refNo时的相同内容
            LOGGER.warn("Duplicate bet detected: {}", request.getXRequestId());
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.INVALID_INPUT);
            return;
        }

        // 1. 获取玩家余额
        final double realBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                (IAgentGameScript script) -> script.realBalance(player));
        final double balance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                (IAgentGameScript script) -> script.amountTransformUsd(player, realBalance));

        // 3. 检查余额是否足够
        if (request.getBet().getStake() > balance) {
            LOGGER.warn("Insufficient balance. stake: {} balance: {}", request.getBet().getStake(), balance);
            FunkyUtils.sendErrorResponse(session, FunkyConstants.ErrorCode.INSUFFICIENT_BALANCE);
            return;
        }

        // 4. 转换金额为实际金额
        final double betValue = ScriptLoader.getInstance().functionScript("AgentGameScript",
                (IAgentGameScript script) -> script.usdTransformAmount(player, request.getBet().getStake()));

        // 5. 扣除玩家余额并创建游戏记录
        final double finalBalance = BigDecimalUtils.sub(balance, request.getBet().getStake(), 2);
        final double balances = ScriptLoader.getInstance().functionScript("AgentGameScript",
                (IAgentGameScript script) -> script.usdTransformAmount(player, finalBalance));

        // 6. 创建或更新游戏记录
        gameNote = ScriptLoader.getInstance().functionScript("AgentGameScript",
                (IAgentGameScript script) -> script.addGameNote(player, request.getBet().getRefNo(), request.getXRequestId(), betValue, betValue, balances));

        // 获取货币名称
        Currency currency = Currency.valueOf(gameNote.getCurrencyId());
        String currencyName = "";
        if (currency != null) {
            currencyName = currency.name();
        }

        // 7. 通知余额变更
        NotifyData notifyData = new NotifyData();
        notifyData.setType(ChangeType.cost.getType())
                .setPid(player.getPlayerId())
                .setPlayerName(request.getPlayerId())
                .setNoteId(gameNote.getNoteId() + "")
                .setBetAmount(betValue)
                .setCurrency(currencyName)
                .setUpdSessionId(MsgUtil.getSessionID(session))
                .setGameId(gameNote.getGameId())
                .setCurrencyId(gameNote.getCurrencyId());

        ScriptLoader.getInstance().consumerScript("AgentGameScript",
                (IAgentGameScript script) -> script.updateCurrency(notifyData));
    }
}
