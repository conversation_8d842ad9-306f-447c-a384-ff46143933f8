package com.game.handler.http.agentGame_1010_gamehub;

import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.MsgUtil;
import com.game.entity.game.GameNote;
import com.game.entity.player.Player;
import com.game.enums.ChangeType;
import com.game.enums.Currency;
import com.game.gamesr.NotifyData;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.scripts.IAgentGameScript;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * GameHub GAP: 下注 - bet
 * 文档：action=bet, amount, player_id, transaction_id, currency, game_id, round_id, ext_round_id, ext_round_finished, hash
 * 返回：{ "status":200, "balance":<int cents>, "currency":"EUR" }
 * 金额单位：整数“分”（minor units）
 */
@IHandlerEntity(path = GameHubConstants.Paths.BET, desc = "GameHub-下注")
public class ReqBetHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqBetHandler.class);

    /* -------------------- 内部类型 -------------------- */
    private static final class BetContext {
        long playerId;
        String currency;
        String transactionId;
        String gameId;
        String roundId;
        String extRoundId;
        int extFinished;     // 0=open, 1=closed
        long amountCents;    // 分
        String freeroundsId; // 可空（如需可从 paramsMap 取）
    }

    /** 当前请求上下文（仅本次请求内使用） */
    private final BetContext ctx = new BetContext();

    @Override
    public void run() {
        try {
            if (ConstantConfig.getInstance().isAgentDebug()) {
                LOGGER.info("[GameHub][bet] params: {}", JsonUtils.writeAsJson(paramsMap));
            }

            GameHubSecurityUtil.Validation v = GameHubSecurityUtil.validate(
                    paramsMap,
                    session,
                    this.getClass().getSimpleName(),
                    "bet",
                    GameHubSecurityUtil.Options.forBet()
            );
            if (!v.ok) {
                GameHubResponseUtil.respondByValidationError(session, v.error);
                return;
            }

            // 2) 填充上下文（从校验结果读取，避免重复解析）
            ctx.playerId      = v.playerId;
            ctx.currency      = v.currency;
            ctx.transactionId = v.transactionId;
            ctx.gameId        = v.gameId;
            ctx.roundId       = v.roundId;
            ctx.extRoundId    = v.extRoundId;
            ctx.extFinished   = v.extRoundFinished == null ? 0 : v.extRoundFinished;
            ctx.amountCents   = v.amountCents == null ? 0L : v.amountCents;

            // 如需 freerounds_id，可选读取（不参与校验）
            Object fr = paramsMap.get("freerounds_id");
            ctx.freeroundsId = (fr == null || String.valueOf(fr).isEmpty()) ? null : String.valueOf(fr);

            // 3) 处理下注（幂等→余额→扣款→落账→通知）
            handle(v.player);

        } catch (Exception e) {
            LOGGER.error("[GameHub][bet] error", e);
            GameHubResponseUtil.sendError(session, GameHubConstants.ErrorCode.ERR001);
        }
    }

    /**
     * 下注处理：
     * - 幂等：回合维度 GameNote 存在即返回当前余额（文档/ATGAME风格）
     * - 余额：对外返回“分”，内部展示币为“元”
     * - 记账：addGameNote / updateCurrency 走你们现有脚本
     */
    private void handle(final Player player) {
        // 1) 当前余额（展示币种：元）
        final double realBalance = ScriptLoader.getInstance().functionScript(
                "AgentGameScript", (IAgentGameScript script) -> script.realBalance(player));
        final double balanceDisplay = ScriptLoader.getInstance().functionScript(
                "AgentGameScript", (IAgentGameScript script) -> script.amountTransformUsd(player, realBalance));
        final long currentCents = Math.round(BigDecimalUtils.round(balanceDisplay, 2) * 100);

        // 2) 幂等：round + transaction（此处沿用你们 findRoundIdGameNote 的风格）
        GameNote gameNote = AgentGameMrg.getInstance().findRoundIdGameNote(player, ctx.roundId);
        if (gameNote != null) {
            LOGGER.warn("[GameHub][bet] duplicate: roundId={}, transactionId={}", ctx.roundId, ctx.transactionId);
            GameHubResponseUtil.sendBalanceOk(session, currentCents, ctx.currency);
            return;
        }

        // 3) 余额不足（分）
        if (currentCents < ctx.amountCents) {
            GameHubResponseUtil.sendError(session, GameHubConstants.ErrorCode.ERR003);
            return;
        }

        // 4) 下注金额（展示币：元）
        final double betDisplay = BigDecimalUtils.round(ctx.amountCents / 100.0, 2);

        // 5) 扣款后余额（展示币：元）
        final double finalBalanceDisplay = BigDecimalUtils.sub(balanceDisplay, betDisplay, 2);

        // 6) 转主账币（脚本负责换算）
        final double betValue = ScriptLoader.getInstance().functionScript(
                "AgentGameScript", (IAgentGameScript script) -> script.usdTransformAmount(player, betDisplay));
        final double balancesValue = ScriptLoader.getInstance().functionScript(
                "AgentGameScript", (IAgentGameScript script) -> script.usdTransformAmount(player, finalBalanceDisplay));

        // 7) 落账（签名方法名/参数顺序保持你们现有实现）
        gameNote = ScriptLoader.getInstance().functionScript(
                "AgentGameScript",
                (IAgentGameScript script) -> script.addGameNote(
                        player, ctx.roundId, ctx.transactionId, betValue, betValue, balancesValue));

        // 8) 货币名称（容错）
        String currencyName = "";
        try {
            final Currency c = Currency.valueOf(gameNote.getCurrencyId());
            if (c != null) currencyName = c.name();
        } catch (Exception ignore) { /* ignore */ }

        // 9) 通知（账变/前端）
        final NotifyData notifyData = new NotifyData();
        notifyData.setType(ChangeType.cost.getType())
                .setPid(player.getPlayerId())
                .setPlayerName(player.getPlayerName())
                .setNoteId(String.valueOf(gameNote.getNoteId()))
                .setCurrency(currencyName)
                .setBetAmount(betValue)
                .setUpdSessionId(MsgUtil.getSessionID(session))
                .setGameId(gameNote.getGameId())
                .setCurrencyId(gameNote.getCurrencyId());

        ScriptLoader.getInstance().consumerScript("AgentGameScript",
                (IAgentGameScript script) -> script.updateCurrency(notifyData));
    }
}
