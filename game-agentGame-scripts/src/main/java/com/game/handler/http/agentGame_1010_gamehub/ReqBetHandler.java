package com.game.handler.http.agentGame_1010_gamehub;

import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.MsgUtil;
import com.game.entity.game.GameNote;
import com.game.entity.player.Player;
import com.game.enums.ChangeType;
import com.game.enums.Currency;
import com.game.gamesr.NotifyData;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.scripts.IAgentGameScript;
import com.game.handler.http.agentGame_1008_bti.BtiUtils;
import io.netty.channel.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * GameHub GAP: 下注 - bet（单一URI分发模式）
 * 请求：action=bet, amount, player_id, transaction_id, currency, game_id, round_id, ext_round_id, ext_round_finished, hash
 * 响应：{ "status":200, "balance":<int cents>, "currency":"..." }
 * 金额单位：整数“分”（minor units）
 *
 * 说明：
 * - 不再继承 HttpHandler，不使用 @IHandlerEntity
 * - 对外仅提供静态入口 process(...)，由 ReqCommonHandler 按 action 调用
 */
public final class ReqBetHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqBetHandler.class);

    private ReqBetHandler() {}

    /** 单一URI分发调用入口 */
    public static void process(Channel session, Map<String, Object> paramsMap, String handlerClassNameForPlatform) {
        try {
            if (ConstantConfig.getInstance().isAgentDebug()) {
                LOGGER.info("[GameHub][bet] params: {}", JsonUtils.writeAsJson(paramsMap));
            }

            // 统一校验（平台配置 / 白名单 / HMAC / 参数解析 / 玩家存在）
            GameHubSecurityUtil.Validation v = GameHubSecurityUtil.validate(
                    paramsMap,
                    session,
                    handlerClassNameForPlatform, // 推荐传 ReqCommonHandler.class.getSimpleName()
                    "bet",
                    GameHubSecurityUtil.Options.forBet()
            );
            if (!v.ok) {
                GameHubResponseUtil.respondByValidationError(session, v.error);
                return;
            }

            // 可选参数：freerounds_id（目前只记录日志，不改变扣款/落账逻辑）
            String freeroundsId = null;
            Object fr = paramsMap.get("freerounds_id");
            if (fr != null) {
                freeroundsId = String.valueOf(fr);
                if (!freeroundsId.isEmpty()) {
                    LOGGER.info("[GameHub][bet] freerounds_id present: roundId={}, tx={}, freeroundsId={}",
                            v.roundId, v.transactionId, freeroundsId);
                }
            }

            // 真正处理下注
            handle(session, v);

        } catch (Exception e) {
            LOGGER.error("[GameHub][bet.process] error", e);
            GameHubResponseUtil.sendError(session, GameHubConstants.ErrorCode.ERR001);
        }
    }

    /**
     * 下注处理主流程：
     * 1) 幂等：同 roundId 下已包含 transactionId → 直接返回余额
     * 2) cancel 先到保护：命中 Redis 标记则拒绝 bet
     * 3) 余额检查 → 扣款金额换算
     * 4) 落账 addGameNote
     * 5) 通知 updateCurrency
     * 6) 返回成功余额（由通知侧回调到前端；如需立即回包余额，也可在此直接 sendBalanceOk）
     */
    private static void handle(Channel session, final GameHubSecurityUtil.Validation v) {
        final Player player = v.player;

        // 1) 当前余额（展示币：元 -> 对外“分”）
        final double realBalance = ScriptLoader.getInstance().functionScript(
                "AgentGameScript", (IAgentGameScript s) -> s.realBalance(player));
        final double balanceDisplay = ScriptLoader.getInstance().functionScript(
                "AgentGameScript", (IAgentGameScript s) -> s.amountTransformUsd(player, realBalance));
        final long currentCents = Math.round(BigDecimalUtils.round(balanceDisplay, 2) * 100);

        // 2) 幂等：同一 roundId 内重复的 transactionId 直接返回余额
        GameNote gameNote = AgentGameMrg.getInstance().findRoundIdGameNote(player, v.roundId);
        if (gameNote != null && gameNote.getTransactionIds() != null
                && gameNote.getTransactionIds().contains(v.transactionId)) {
            LOGGER.warn("[GameHub][bet] duplicate: roundId={}, transactionId={}", v.roundId, v.transactionId);
            GameHubResponseUtil.sendBalanceOk(session, currentCents, v.currency);
            return;
        }

        // 3) cancel 先到保护：若存在 cancel 标记，拒绝本次 bet
        final String cancelMarkerKey = "gh:cancel:transactionId:" + v.transactionId;
        try {
            Long exists = RedisPoolManager.getInstance().function(jedis -> jedis.sync().exists(cancelMarkerKey));
            if (exists != null && exists > 0) {
                String markedTx = RedisPoolManager.getInstance().function(jedis -> jedis.sync().get(cancelMarkerKey));
                LOGGER.warn("[GameHub][bet] cancel-first marker found, reject bet: roundId={}, cancelTx={}, incomingTx={}",
                        v.roundId, markedTx, v.transactionId);

                // 可选：清理标记，避免后续重复命中
                RedisPoolManager.getInstance().function(jedis -> { jedis.sync().del(cancelMarkerKey); return null; });

                GameHubResponseUtil.sendError(session, GameHubConstants.ErrorCode.ERR001);
                return;
            }
        } catch (Exception re) {
            // Redis 不可用不影响主流程
            LOGGER.error("[GameHub][bet] redis check cancel-first marker failed: key={}, err={}", cancelMarkerKey, re.getMessage(), re);
        }

        // 4) 余额不足（分）
        final long amountCents = (v.amountCents == null ? 0L : v.amountCents);
        if (currentCents < amountCents) {
            GameHubResponseUtil.sendError(session, GameHubConstants.ErrorCode.ERR003);
            return;
        }

        // 5) 下注金额（展示币：元）与扣款后余额
        final double betDisplay = BigDecimalUtils.round(amountCents / 100.0, 2);
        final double finalBalanceDisplay = BigDecimalUtils.sub(balanceDisplay, betDisplay, 2);

        // 6) 转主账币（脚本负责换算）
        final double betValue = ScriptLoader.getInstance().functionScript(
                "AgentGameScript", (IAgentGameScript s) -> s.usdTransformAmount(player, betDisplay));
        final double balancesValue = ScriptLoader.getInstance().functionScript(
                "AgentGameScript", (IAgentGameScript s) -> s.usdTransformAmount(player, finalBalanceDisplay));

        if (gameNote == null) {
            gameNote = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript s) -> s.gamehubAddGameNote(player, v.roundId, v.transactionId, betValue, betValue, balancesValue));
        } else {
            gameNote = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript s) -> s.gamehubUpdateBetGameNote(player, v.roundId, v.transactionId, betValue, betValue, balancesValue));

        }

        // 8) 货币名称（容错）
        String currencyName = "";
        try {
            final Currency c = Currency.valueOf(gameNote.getCurrencyId());
            if (c != null) currencyName = c.name();
        } catch (Exception ignore) { /* ignore */ }

        // 9) 通知（账变/前端）
        final NotifyData notifyData = new NotifyData();
        notifyData.setType(ChangeType.cost.getType())
                .setPid(player.getPlayerId())
                .setPlayerName(player.getPlayerName())
                .setNoteId(String.valueOf(gameNote.getNoteId()))
                .setCurrency(currencyName)
                .setBetAmount(betValue)
                .setUpdSessionId(MsgUtil.getSessionID(session))
                .setGameId(gameNote.getGameId())
                .setCurrencyId(gameNote.getCurrencyId());

        ScriptLoader.getInstance().consumerScript(
                "AgentGameScript",
                (IAgentGameScript s) -> s.updateCurrency(notifyData));
    }
}
