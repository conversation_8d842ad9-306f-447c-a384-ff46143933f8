package com.game.handler.http.agentGame_1010_gamehub;

import com.game.c_entity.middleplatform.C_BaseGamePlatform;
import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.MsgUtil;
import com.game.entity.game.GameNote;
import com.game.entity.player.Player;
import com.game.enums.AgentGame;
import com.game.enums.ChangeType;
import com.game.enums.Currency;
import com.game.gamesr.NotifyData;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.manager.DataAgentGameMrg;
import com.game.gamesr.scripts.IAgentGameScript;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * GameHub GAP: 下注 - bet
 * 文档位置：第18页 “Make a bet – bet”
 * - 必填：action=bet, amount, player_id, transaction_id, currency, game_id, round_id, ext_round_id, ext_round_finished, hash
 * - 可选：freerounds_id, extra
 * - 返回：{ "status":200, "balance":<int cents>, "currency":"EUR" }
 * - 金额单位：整数“分”（minor units）
 */
@IHandlerEntity(path = GameHubConstants.Paths.BET, desc = "GameHub-下注")
public class ReqBetHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqBetHandler.class);

    /* -------------------- 内部类型 -------------------- */
    private static final class BetContext {
        long playerId;
        String currency;
        String transactionId;
        String gameId;
        String roundId;
        String extRoundId;
        int extFinished;   // 0=open, 1=closed
        long amountCents;   // 整数分（minor units）
        String freeroundsId;  // 可为空
    }

    /**
     * 当前请求上下文（解析后只在本请求内使用）
     */
    private final BetContext ctx = new BetContext();

    @Override
    public void run() {
        try {
            if (ConstantConfig.getInstance().isAgentDebug()) {
                LOGGER.info("[GameHub][bet] params: {}", JsonUtils.writeAsJson(paramsMap));
            }

            // 1) 业务侧校验（失败直接回包，成功返回 Player 并填充 ctx）
            final Player player = validateAndGetPlayer();
            if (player == null) return;

            // 2) 处理下注（幂等→余额→扣款→落账→通知）
            handle(player);

        } catch (Exception e) {
            LOGGER.error("[GameHub][bet] error", e);
            GameHubResponseUtil.errUnknown(session, "unknown");
        }
    }

    /**
     * 业务校验（非通用）：参数完整性与类型、平台配置、IP 白名单（命名遗留）、HMAC、玩家存在性
     */
    private Player validateAndGetPlayer() {
        final String action = (String) paramsMap.get("action");
        final String playerIdStr = (String) paramsMap.get("player_id");
        final String currency = (String) paramsMap.get("currency");
        final String transactionId = (String) paramsMap.get("transaction_id");
        final String gameId = (String) paramsMap.get("game_id");
        final String roundId = (String) paramsMap.get("round_id");
        final String extRoundId = (String) paramsMap.get("ext_round_id");
        final String extFinishedStr = (String) paramsMap.get("ext_round_finished");
        final String amountStr = (String) paramsMap.get("amount");          // 整数分
        final String freeroundsId = (String) paramsMap.get("freerounds_id");   // 可选
        final String hash = (String) paramsMap.get("hash");

        if (!"bet".equalsIgnoreCase(action)
                || StringUtil.isNullOrEmpty(playerIdStr)
                || StringUtil.isNullOrEmpty(currency)
                || StringUtil.isNullOrEmpty(transactionId)
                || StringUtil.isNullOrEmpty(gameId)
                || StringUtil.isNullOrEmpty(roundId)
                || StringUtil.isNullOrEmpty(extRoundId)
                || StringUtil.isNullOrEmpty(extFinishedStr)
                || StringUtil.isNullOrEmpty(amountStr)
                || StringUtil.isNullOrEmpty(hash)) {
            GameHubResponseUtil.errUnknown(session, "missing or invalid params");
            return null;
        }

        final long playerId;
        final long amountCents;
        final int extFinished;
        try {
            playerId = Long.parseLong(playerIdStr);
            amountCents = Long.parseLong(amountStr);
            extFinished = Integer.parseInt(extFinishedStr);
        } catch (Exception e) {
            GameHubResponseUtil.errUnknown(session, "invalid numeric param");
            return null;
        }
        if (amountCents < 0 || (extFinished != 0 && extFinished != 1)) {
            GameHubResponseUtil.errUnknown(session, "invalid amount or ext_round_finished");
            return null;
        }

        // 平台配置（取 secret / 白名单）
        final C_BaseGamePlatform cfg = DataAgentGameMrg.getInstance()
                .findC_BaseGamePlatformSupplierId(this.getClass().getSimpleName(), AgentGame.GAMEHUB.getType());
        if (cfg == null || StringUtil.isNullOrEmpty(cfg.getSecretKey())) {
            GameHubResponseUtil.errUnknown(session, "platform config not found");
            return null;
        }
        final String secret = cfg.getSecretKey();

        // IP 白名单（注意：getIpBlacklist 实际返回白名单，命名历史遗留）
        if (!cfg.getIpBlacklist().isEmpty()) {
            final String ip = MsgUtil.getClientIp(session);
            final boolean inWhiteList = ScriptLoader.getInstance().functionScript(
                    "AgentGameScript",
                    (IAgentGameScript script) -> script.isIpBlacklist(ip, AgentGame.GAMEHUB.getType()));
            if (!inWhiteList) {
                LOGGER.warn("[GameHub][bet] not in IP whitelist: {}", ip);
                GameHubResponseUtil.errUnauthorized(session, "ip_not_allowed:" + ip);
                return null;
            }
        }

        // HMAC
        if (!GameHubSecurityUtil.verifySignature(paramsMap, hash, secret)) {
            GameHubResponseUtil.errUnauthorized(session, "HMAC mismatch");
            return null;
        }

        // 玩家必须存在
        final Player player = AgentGameMrg.getInstance().findDbPlayer(playerId);
        if (player == null) {
            GameHubResponseUtil.errUnknown(session, "player_not_found");
            return null;
        }

        // 上下文
        ctx.playerId = playerId;
        ctx.currency = currency;
        ctx.transactionId = transactionId;
        ctx.gameId = gameId;
        ctx.roundId = roundId;
        ctx.extRoundId = extRoundId;
        ctx.extFinished = extFinished;     // 0=open, 1=closed
        ctx.amountCents = amountCents;     // 分
        ctx.freeroundsId = StringUtil.isNullOrEmpty(freeroundsId) ? null : freeroundsId;

        return player;
    }

    /**
     * 下注处理：
     * - 幂等：回合维度 GameNote + 交易集合 contains(transactionId)
     * - 余额：基于“展示币种（元）”，对外返回“分”
     * - 记账：addGameNote / updateCurrency 走你们现有脚本
     */
    private void handle(final Player player) {
        // 1) 当前余额（展示币种：元）
        final double realBalance = ScriptLoader.getInstance().functionScript(
                "AgentGameScript", (IAgentGameScript script) -> script.realBalance(player));
        final double balanceDisplay = ScriptLoader.getInstance().functionScript(
                "AgentGameScript", (IAgentGameScript script) -> script.amountTransformUsd(player, realBalance));
        final long currentCents = Math.round(BigDecimalUtils.round(balanceDisplay, 2) * 100);

        // 2) 幂等：round + transaction 两键判断（参考 ATGAME）
        GameNote gameNote = AgentGameMrg.getInstance().findRoundIdGameNote(player, ctx.roundId);
        if (gameNote != null) {
            LOGGER.warn("[GameHub][bet] duplicate: roundId={}, transactionId={}", ctx.roundId, ctx.transactionId);
            GameHubResponseUtil.sendBalanceOk(session, currentCents, ctx.currency);
            return;
        }

        // 3) 余额不足（分）
        if (currentCents < ctx.amountCents) {
            GameHubResponseUtil.errInsufficientFunds(session);
            return;
        }

        // 4) 下注金额（展示币种：元）
        final double betDisplay = BigDecimalUtils.round(ctx.amountCents / 100.0, 2);

        // 5) 扣款后余额（展示币种：元）
        final double finalBalanceDisplay = BigDecimalUtils.sub(balanceDisplay, betDisplay, 2);

        // 6) 转换为主账币种金额（脚本层负责换算）
        final double betValue = ScriptLoader.getInstance().functionScript(
                "AgentGameScript", (IAgentGameScript script) -> script.usdTransformAmount(player, betDisplay));
        final double balancesValue = ScriptLoader.getInstance().functionScript(
                "AgentGameScript", (IAgentGameScript script) -> script.usdTransformAmount(player, finalBalanceDisplay));

        // 7) 落账：创建或更新游戏记录（你们脚本签名已验证通过，这里不改方法名/参数顺序）
        gameNote = ScriptLoader.getInstance().functionScript(
                "AgentGameScript",
                (IAgentGameScript script) -> script.addGameNote(
                        player, ctx.roundId, ctx.transactionId, betValue, betValue, balancesValue));

        // 8) 货币名称（容错：避免枚举映射异常）
        String currencyName = "";
        try {
            final Currency c = Currency.valueOf(gameNote.getCurrencyId());
            if (c != null) currencyName = c.name();
        } catch (Exception ignore) {
            // 忽略映射异常，保留空串
        }

        // 9) 通知（按你们既有的账变/前端通知流程）
        final NotifyData notifyData = new NotifyData();
        notifyData.setType(ChangeType.cost.getType())
                .setPid(player.getPlayerId())
                .setPlayerName(player.getPlayerName())
                .setNoteId(String.valueOf(gameNote.getNoteId()))
                .setCurrency(currencyName)
                .setBetAmount(betValue)
                .setUpdSessionId(MsgUtil.getSessionID(session))
                .setGameId(gameNote.getGameId())
                .setCurrencyId(gameNote.getCurrencyId());

        ScriptLoader.getInstance().consumerScript("AgentGameScript",
                (IAgentGameScript script) -> script.updateCurrency(notifyData));
    }
}
