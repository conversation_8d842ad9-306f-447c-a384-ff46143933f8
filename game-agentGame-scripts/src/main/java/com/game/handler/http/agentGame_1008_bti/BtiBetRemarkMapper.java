package com.game.handler.http.agentGame_1008_bti;

import com.game.engine.utils.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.w3c.dom.*;
import javax.xml.parsers.DocumentBuilderFactory;
import java.nio.charset.StandardCharsets;
import java.util.*;

public class BtiBetRemarkMapper {
    private static final Logger LOGGER = LoggerFactory.getLogger(BtiBetRemarkMapper.class);

    /**
     * 重组的投注信息类，用于生成备注
     */
    public static class BetRemarkInfo {
        /**
         * 投注ID
         */
        private String id;
        /**
         * 事件ID
         */
        private String eventId;
        /**
         * 体育类型
         */
        private String sportName;
        /**
         * 联赛
         */
        private String tournamentName;
        /**
         * 对阵双方 (使用vs连接的字符串格式)
         */
        private String competitorNames;
        /**
         * 投注选项
         */
        private String outcomeName;
        /**
         * 赔率
         */
        private String odds;

        public BetRemarkInfo(String id, String eventId, String sportName, String tournamentName, String competitorNames, String outcomeName, String odds) {
            this.id = id;
            this.eventId = eventId;
            this.sportName = sportName;
            this.tournamentName = tournamentName;
            this.competitorNames = competitorNames;
            this.outcomeName = outcomeName;
            this.odds = odds;
        }

        // getter/setter
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        public String getEventId() { return eventId; }
        public void setEventId(String eventId) { this.eventId = eventId; }
        public String getSportName() { return sportName; }
        public void setSportName(String sportName) { this.sportName = sportName; }
        public String getTournamentName() { return tournamentName; }
        public void setTournamentName(String tournamentName) { this.tournamentName = tournamentName; }
        public String getCompetitorNames() { return competitorNames; }
        public void setCompetitorNames(String competitorNames) { this.competitorNames = competitorNames; }
        public String getOutcomeName() { return outcomeName; }
        public void setOutcomeName(String outcomeName) { this.outcomeName = outcomeName; }
        public String getOdds() { return odds; }
        public void setOdds(String odds) { this.odds = odds; }
    }

    /** 从 paramsMap.get("data") 的 XML 生成 BetRemarkInfo 列表；缺字段不报错 */
    public static List<BetRemarkInfo> buildBetRemarksFromBtiXmlSafe(String xml) {
        if (xml == null || xml.isBlank()) {
            LOGGER.warn("BTI XML is null/blank");
            return Collections.emptyList();
        }

        try {
            // —— 安全 DOM（禁 DTD/外部实体，防 XXE）——
            DocumentBuilderFactory f = DocumentBuilderFactory.newInstance();
            f.setNamespaceAware(false);
            f.setExpandEntityReferences(false);
            f.setXIncludeAware(false);
            f.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
            f.setFeature("http://xml.org/sax/features/external-general-entities", false);
            f.setFeature("http://xml.org/sax/features/external-parameter-entities", false);

            Document doc = f.newDocumentBuilder()
                    .parse(new java.io.ByteArrayInputStream(xml.getBytes(StandardCharsets.UTF_8)));
            Element root = doc.getDocumentElement();

            NodeList betNodes = root.getElementsByTagName("Bet");
            List<BetRemarkInfo> out = new ArrayList<>(betNodes.getLength());

            for (int i = 0; i < betNodes.getLength(); i++) {
                Element bet = (Element) betNodes.item(i);

                // —— 所有字段都用空串兜底 ——
                String id             = firstNonBlank(attr(bet, "BetID"));
                String eventId        = firstNonBlank(attr(bet, "EventID"), attr(bet, "NewEventID"));
                String sportName      = firstNonBlank(attr(bet, "TransBranchName"),  attr(bet, "BranchName"));
                String tournamentName = firstNonBlank(attr(bet, "TransLeagueName"),  attr(bet, "LeagueName"));
                String outcomeName    = firstNonBlank(attr(bet, "TransYourBet"),     attr(bet, "YourBet"));
                String odds           = firstNonBlank(attr(bet, "OddsInUserStyle"),  attr(bet, "OddsDec"),
                        attr(bet, "DBOdds"),           attr(bet, "Odds"));

                // 对阵
                List<String> names = new ArrayList<>(2);
                String home = firstNonBlank(attr(bet, "TransHomeTeam"), attr(bet, "HomeTeam"));
                String away = firstNonBlank(attr(bet, "TransAwayTeam"), attr(bet, "AwayTeam"));
                if (!home.isEmpty()) names.add(home);
                if (!away.isEmpty()) names.add(away);
                String formatted = formatCompetitorNames(names);  // 你的方法，已对空做兜底

                LOGGER.info("Processing bet {}: id={}, eventId={}, sportName={}, tournamentName={}, competitorNames={} -> {}, outcomeName={}, odds={}",
                        i + 1, id, eventId, sportName, tournamentName, names, formatted, outcomeName, odds);

                out.add(new BetRemarkInfo(id, eventId, sportName, tournamentName, formatted, outcomeName, odds));
            }
            return out;

        } catch (Exception e) {
            LOGGER.warn("BTI XML parse error: {}", e.toString());
            return Collections.emptyList();  // 失败也不抛，返回空列表
        }
    }

    // —— 辅助方法（全部 null-safe）——
    private static String attr(Element e, String name) {
        if (e == null || name == null) return "";
        String v = e.getAttribute(name);
        return v == null ? "" : v.trim();
    }
    private static String firstNonBlank(String... vals) {
        if (vals != null) for (String v : vals) if (v != null && !v.trim().isEmpty()) return v.trim();
        return ""; // 兜底为空串
    }

    // 也可以直接复用你已有的实现
    private static String formatCompetitorNames(List<String> competitorNames) {
        if (competitorNames == null || competitorNames.isEmpty()) return "";
        return competitorNames.size() == 1 ? competitorNames.get(0) : String.join(" vs ", competitorNames);
    }

    public static List<BetRemarkInfo> buildBetRemarksFromBtiXmlSafe(Iterable<String> xmls) {
        if (xmls == null) throw new IllegalArgumentException("xmls is null");
        if (xmls instanceof Collection<?> c && c.isEmpty()) {
            throw new IllegalArgumentException("xmls is empty");
        }
        List<BetRemarkInfo> out = new ArrayList<>();
        for (String xml : xmls) {
            List<BetRemarkInfo> one = buildBetRemarksFromBtiXmlSafe(xml); // 复用单条
            if (one != null && !one.isEmpty()) out.addAll(one);
        }
        return out;
    }

    public static String buildBetRemarksFromBtiXmlSafeToJson(Iterable<String> xmls) {
        return JsonUtils.writeAsJson(buildBetRemarksFromBtiXmlSafe(xmls));
    }
}

