package com.game.handler.http.agentGame_1007_funky;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTCreator;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.TokenExpiredException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.game.engine.utils.TimeUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

public class FunkyJWTUtils {
    private static final Logger LOGGER = LoggerFactory.getLogger(FunkyJWTUtils.class);

    private static final String key = "38f6fe699382f0b2410189b44548e2e6";

    public static String jwtToken(JWData jwData) {
        //获取jwt生成器
        final JWTCreator.Builder jwtBuilder = JWT.create();

        //由于该生成器设置Header的参数为一个<String, Object>的Map,
        //所以我们提前准备好
        final Map<String, Object> headers = new HashMap<>();

        headers.put("typ", "jwt");   //设置token的type为jwt
        headers.put("alg", "hs256");  //表明加密的算法为HS256

        //开始生成token
        //我们将之前准备好的header设置进去

        //token生成完毕，可以发送给客户端了，前端可以使用
        //localStorage.setItem("your_token", token)进行存储，在
        final JWTCreator.Builder builder = jwtBuilder.withHeader(headers);
        //接下来为设置PayLoad,Claim中的键值对可自定义
        //设置用户id
        builder.withClaim("userId", jwData.userId);
        //token失效时间，这里为一天后失效
        builder.withExpiresAt(new Date(jwData.expires));
        //设置该jwt的发行时间，一般为当前系统时间
        builder.withIssuedAt(new Date(TimeUtil.currentTimeMillis()));

        //进行签名，选择加密算法，以一个字符串密钥为参数
        return builder.sign(Algorithm.HMAC256(key));
    }

    public static Map<String, Object> verify(String token) {
        /*判断token是否存在，若不存在，验证失败，
            并进行验证失败的逻辑操作（例如跳转到登录界面，
            或拒绝访问等等）*/
        final Map<String, Object> params = new LinkedHashMap<>();
        if (token == null) return params;

        /*获取jwt的验证器对象，传入的算法参数以及密钥字符串（KEY）必须
        和加密时的相同*/
        final var require = JWT.require(Algorithm.HMAC256(key)).build();

        DecodedJWT decode;
        try {
            /*开始进行验证，该函数会验证此token是否遭到修改，
                以及是否过期，验证成功会生成一个解码对象
                ，如果token遭到修改或已过期就会
                抛出异常，我们用try-catch抓一下*/
            decode = require.verify(token);

        } catch (Exception e) {
            //抛出异常，验证失败
            if (e instanceof TokenExpiredException) {
                params.put("error", 11);
                return params;
            } else {
                LOGGER.error("", e);
            }
            return params;
        }

        //若验证成功，就可获取其携带的信息进行其他操作
        params.put("userId", decode.getClaim("userId").asLong());
        params.put("expires", decode.getExpiresAt().getTime());
        return params;
    }

    public static class JWData {
        public long userId;
        public long expires;
    }
}
