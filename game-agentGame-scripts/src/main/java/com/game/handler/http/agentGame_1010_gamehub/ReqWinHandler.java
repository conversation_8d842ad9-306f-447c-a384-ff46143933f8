package com.game.handler.http.agentGame_1010_gamehub;

import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.MsgUtil;
import com.game.entity.game.GameNote;
import com.game.entity.player.Player;
import com.game.enums.ChangeType;
import com.game.enums.Currency;
import com.game.gamesr.NotifyData;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.scripts.IAgentGameScript;
import io.netty.channel.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * GameHub GAP: 派奖 - win（单一URI分发模式）
 * 请求：action=win, amount, player_id, transaction_id, currency, game_id, round_id, ext_round_id, ext_round_finished, hash
 * 响应：{ "status":200, "balance":<int cents>, "currency":"..." }
 * 金额单位：整数“分”（minor units）
 *
 * 说明：
 * - 不再继承 HttpHandler，不使用 @IHandlerEntity
 * - 对外仅提供静态入口 process(...)，由 ReqCommonHandler 按 action 调用
 */
public final class ReqWinHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqWinHandler.class);

    private ReqWinHandler() {}

    /** 单一URI分发调用入口 */
    public static void process(Channel session, Map<String, Object> paramsMap, String handlerClassNameForPlatform) {
        try {
            if (ConstantConfig.getInstance().isAgentDebug()) {
                LOGGER.info("[GameHub][win] params: {}", JsonUtils.writeAsJson(paramsMap));
            }

            // 统一校验（平台配置 / 白名单 / HMAC / 参数解析 / 玩家存在）
            GameHubSecurityUtil.Validation v = GameHubSecurityUtil.validate(
                    paramsMap,
                    session,
                    handlerClassNameForPlatform, // 建议传入 ReqCommonHandler.class.getSimpleName()
                    "win",
                    GameHubSecurityUtil.Options.forWin()
            );
            if (!v.ok) {
                GameHubResponseUtil.respondByValidationError(session, v.error);
                return;
            }

            // 处理派奖
            handle(session, v);

        } catch (Exception e) {
            LOGGER.error("[GameHub][win.process] error", e);
            GameHubResponseUtil.sendError(session, GameHubConstants.ErrorCode.ERR001);
        }
    }

    /**
     * 派奖处理：
     * - 幂等：回合维度 GameNote + 交易集合 contains(transactionId) 则返回当下余额
     * - 金额：内部展示币“元”，对外返回“分”
     * - 记账：updateGameNote / updateCurrency（ChangeType.win）
     */
    private static void handle(final Channel session, final GameHubSecurityUtil.Validation v) {
        final Player player = v.player;

        // 1) 当前余额（展示币：元）
        final double realBalance = ScriptLoader.getInstance().functionScript(
                "AgentGameScript", (IAgentGameScript script) -> script.realBalance(player));
        final double balanceDisplay = ScriptLoader.getInstance().functionScript(
                "AgentGameScript", (IAgentGameScript script) -> script.amountTransformUsd(player, realBalance));
        final long currentCents = Math.round(BigDecimalUtils.round(balanceDisplay, 2) * 100);

        // 2) 幂等：round + transaction
        GameNote gameNote = AgentGameMrg.getInstance().findRoundIdGameNote(player, v.roundId);
        if (gameNote != null
                && gameNote.getTransactionIds() != null
                && gameNote.getTransactionIds().contains(v.transactionId)) {
            LOGGER.warn("[GameHub][win] duplicate: roundId={}, transactionId={}", v.roundId, v.transactionId);
            GameHubResponseUtil.sendBalanceOk(session, currentCents, v.currency);
            return;
        }

        if (gameNote.getStatus() == 3) { // 已取消幂等返回
            LOGGER.error("[GameHub][bet.process] Bet request Already Cancelled. roundId={}, transactionId={}", v.roundId, v.transactionId);
            GameHubResponseUtil.sendError(session, GameHubConstants.ErrorCode.ERR001);
            return;
        }

        // 3) 派奖金额（展示币：元）
        final double winDisplay = BigDecimalUtils.round((v.amountCents == null ? 0L : v.amountCents) / 100.0, 2);

        // 4) 加钱后的余额（展示币：元）
        final double finalBalanceDisplay = BigDecimalUtils.add(balanceDisplay, winDisplay, 2);

        // 5) 转主账币金额（脚本换算）
        final double winValue = ScriptLoader.getInstance().functionScript(
                "AgentGameScript", (IAgentGameScript script) -> script.usdTransformAmount(player, winDisplay));
        final double balancesValue = ScriptLoader.getInstance().functionScript(
                "AgentGameScript", (IAgentGameScript script) -> script.usdTransformAmount(player, finalBalanceDisplay));

        // 6) 记账：更新 GameNote（签名保持既有实现）
        gameNote = ScriptLoader.getInstance().functionScript(
                "AgentGameScript",
                (IAgentGameScript script) -> script.updateGameNote(
                        player, v.roundId, v.transactionId,
                        /* type */ 2,
                        /* win  */ winValue,
                        /* balances */ balancesValue));

        // 7) 货币名称（容错）
        String currencyName = "";
        try {
            final Currency c = Currency.valueOf(gameNote.getCurrencyId());
            if (c != null) currencyName = c.name();
        } catch (Exception ignore) { /* ignore */ }

        // 8) 通知（账变/前端）
        final NotifyData notifyData = new NotifyData();
        notifyData.setType(ChangeType.win.getType())
                .setPid(player.getPlayerId())
                .setPlayerName(player.getPlayerName())
                .setNoteId(String.valueOf(gameNote.getNoteId()))
                .setCurrency(currencyName)
                .setBetAmount(gameNote.getBetAmount())
                .setValidBets(gameNote.getValidBets())
                .setWin(winValue)
                .setTotalWin(winValue)
                .setUpdSessionId(MsgUtil.getSessionID(session))
                .setGameId(gameNote.getGameId())
                .setCurrencyId(gameNote.getCurrencyId());

        ScriptLoader.getInstance().consumerScript(
                "AgentGameScript",
                (IAgentGameScript script) -> script.updateCurrency(notifyData));
    }
}
