package com.game.handler.http.agentGame_1008_bti;

import com.game.c_entity.middleplatform.C_BaseGamePlatform;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.*;
import com.game.entity.game.GameNote;
import com.game.entity.player.Player;
import com.game.enums.AgentGame;
import com.game.enums.ChangeType;
import com.game.enums.Currency;
import com.game.gamesr.NotifyData;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.manager.DataAgentGameMrg;
import com.game.gamesr.scripts.IAgentGameScript;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

//http://127.0.0.1:8680/bti/reserve
@IHandlerEntity(path = BtiConstants.Paths.RESERVE, desc = "扣款")
public class ReqReserveHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqReserveHandler.class);

    @Override
    public void run() {
        try {
            final Map<String, Object> paramsMap = getParamsMap();
            LOGGER.info("ReqReserveHandler，params：{}", JsonUtils.writeAsJson(paramsMap));

            final C_BaseGamePlatform c_baseGamePlatform = DataAgentGameMrg.getInstance()
                    .findC_BaseGamePlatformSupplierId(this.getClass().getSimpleName(), AgentGame.BTI.getType());

            // 没有设置就默认通过
            if (!c_baseGamePlatform.getIpBlacklist().isEmpty()) {
                String ip = MsgUtil.getClientIp(session);
                final boolean inWhiteList = ScriptLoader.getInstance().functionScript("AgentGameScript",
                        (IAgentGameScript script) -> script.isIpBlacklist(ip, AgentGame.BTI.getType()));
                if (!inWhiteList) {
                    LOGGER.warn("not in IP whitelist: {}", ip);
                    BtiUtils.sendErrorResponse(session, BtiConstants.ErrorCode.TOKEN_NOT_VALID, "server error");
                    return;
                }
            }

            final String custId = (String) paramsMap.get("cust_id");
            final String reserveId = (String) paramsMap.get("reserve_id");
            final String amount = (String) paramsMap.get("amount");

            final Player player = BtiUtils.getPlayer(custId);
            if (player == null) {
                BtiUtils.sendSuccessResponse(session, "Player not found", 0.00);
                return;
            }

            // 2. 检查是否存在重复下注
            GameNote gameNote = AgentGameMrg.getInstance().findRoundIdGameNote(player, reserveId);
            if (gameNote != null) {
                LOGGER.warn("Duplicate bet detected: {}", reserveId);
                BtiUtils.sendSuccessResponse(session, "Duplicate bet detected", gameNote.getBalance());
                return;
            }

            final double realBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.realBalance(player));
            double balance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.amountTransformUsd(player, realBalance));
            final double reqAmount = BigDecimalUtils.round(Double.parseDouble(amount), 2);

            if (reqAmount > balance) {
                LOGGER.warn("bet failed. Not enough money");
                BtiUtils.sendErrorResponse(session, BtiConstants.ErrorCode.INSUFFICIENT_FUNDS, balance);
                return;
            }

            final double finalBalance = BigDecimalUtils.sub(balance, reqAmount, 2);

            final double betValue = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.usdTransformAmount(player, reqAmount));

            final double balances = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.usdTransformAmount(player, finalBalance));

            gameNote = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.addGameNote(player,  reserveId, reserveId, betValue, betValue, balances));

            // 获取货币名称
            Currency currency = Currency.valueOf(gameNote.getCurrencyId());
            String currencyName = "";
            if (currency != null) {
                currencyName = currency.name();
            }

            NotifyData notifyData = new NotifyData();
            notifyData.setType(ChangeType.cost.getType())
                    .setPid(player.getPlayerId())
                    .setPlayerName(custId)
                    .setNoteId(gameNote.getNoteId() + "")
                    .setCurrency(currencyName)
                    .setBetAmount(betValue)
                    .setUpdSessionId(MsgUtil.getSessionID(session))
                    .setGameId(gameNote.getGameId())
                    .setCurrencyId(gameNote.getCurrencyId());

            ScriptLoader.getInstance().consumerScript("AgentGameScript",
                    (IAgentGameScript script) -> script.updateCurrency(notifyData));
        } catch (Exception e) {
            LOGGER.error("ReqReserveHandler", e);
            BtiUtils.sendErrorResponse(session, BtiConstants.ErrorCode.NO_EXISTING_SESSION, "server error");
        }
    }

}
