package com.game.handler.http.agentGame_1005_betby;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTCreator;
import com.auth0.jwt.algorithms.Algorithm;
import com.game.c_entity.middleplatform.C_BaseGamePlatform;
import com.game.enums.AgentGame;
import com.game.gamesr.manager.DataAgentGameMrg;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.openssl.PEMKeyPair;
import org.bouncycastle.openssl.PEMParser;
import org.bouncycastle.openssl.jcajce.JcaPEMKeyConverter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.StringReader;
import java.security.KeyPair;
import java.security.Security;
import java.security.interfaces.ECPrivateKey;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;


public class BetbyJWTUtils {
    private static final Logger LOGGER = LoggerFactory.getLogger(BetbyJWTUtils.class);

    // 修改加载私钥方法使用字符串而非文件
    public static ECPrivateKey loadPrivateKey() throws Exception {
        if (Security.getProvider("BC") == null) {
            Security.addProvider(new BouncyCastleProvider());
        }

        C_BaseGamePlatform c_baseGamePlatform = DataAgentGameMrg.getInstance()
                .findC_BaseGamePlatformSupplierId(BetbyJWTUtils.class.getSimpleName(), AgentGame.BETBY.getType());

        String rawKey = c_baseGamePlatform.getSecretKey();
        LOGGER.info("raw privateKey (no header/footer): {}", rawKey);

        StringBuilder pemBuilder = new StringBuilder();
        pemBuilder.append("-----BEGIN EC PRIVATE KEY-----\n");
        for (int i = 0; i < rawKey.length(); i += 64) {
            pemBuilder.append(rawKey, i, Math.min(i + 64, rawKey.length())).append("\n");
        }
        pemBuilder.append("-----END EC PRIVATE KEY-----");

        String privateKeyPem = pemBuilder.toString();
        LOGGER.info("Formatted PEM:\n{}", privateKeyPem);

        try (
            StringReader reader = new StringReader(privateKeyPem);
            PEMParser pemParser = new PEMParser(reader);
        ) {
            Object pemObject = pemParser.readObject();
            JcaPEMKeyConverter converter = new JcaPEMKeyConverter().setProvider("BC");

            if (!(pemObject instanceof PEMKeyPair)) {
                String className = (pemObject != null ? pemObject.getClass().getName() : "null");
                throw new IllegalArgumentException("不支持的密钥格式: " + className);
            }

            KeyPair keyPair = converter.getKeyPair((PEMKeyPair) pemObject);
            return (ECPrivateKey) keyPair.getPrivate();
        }
    }

    /**
     * 生成高度唯一的JWT ID，无需外部验重
     * 结合UUID、时间戳，确保唯一性
     * @return 唯一的JWT ID字符串
     */
    private static String generateUniqueJwtId() {
        // 生成UUID并去除破折号，再附加时间戳的十六进制表示
        return UUID.randomUUID().toString().replace("-", "")
                + Long.toHexString(System.currentTimeMillis());
    }

    // 生成 JWT token
    public static String jwtToken(JWData jwData) {
        try {
            // 加载私钥
            ECPrivateKey privateKey = loadPrivateKey();

            // 使用 ECDSA256 算法签名生成 JWT
            JWTCreator.Builder jwtBuilder = JWT.create();

            // 设置 JWT 标头
            jwtBuilder.withHeader(Map.of("typ", "JWT", "alg", "ES256"));

            // 设置 JWT 负载
            jwtBuilder.withClaim("iss", jwData.iss)          // 品牌ID
                    .withClaim("sub", jwData.sub)          // 玩家ID
                    .withClaim("name", jwData.name)        // 玩家用户名
                    .withClaim("lang", jwData.lang)        // 语言代码
                    .withClaim("currency", jwData.currency) // 货币代码
                    .withClaim("ff", jwData.ff);  // feature flags作为JSON字符串

            // 可选字段
            if (jwData.odds_format != null) {
                jwtBuilder.withClaim("odds_format", jwData.odds_format);
            }

            // 获取当前时间（秒）
            long currentTimeSeconds = System.currentTimeMillis() / 1000;

            // 标准字段
            jwtBuilder.withIssuedAt(new Date(currentTimeSeconds * 1000))  // iat: 当前时间
                    .withExpiresAt(new Date(jwData.exp * 1000))    // exp: 过期时间
                    .withJWTId(jwData.jti);                        // jti: JWT唯一标识

            // 使用私钥签名并返回 JWT
            return jwtBuilder.sign(Algorithm.ECDSA256(privateKey));

        } catch (Exception e) {
            throw new RuntimeException("Error generating JWT", e);
        }
    }

    // JWData 用于生成 JWT 的负载数据
    public static class JWData {
        // 公开字段，允许外部设置
        public String iss;          // brandId
        public String sub;          // (玩家ID-货币代码)，这样才能唯一
        public String name;         // 玩家用户名
        public String lang;         // 语言代码
        public String currency;     // 货币代码
        public String odds_format;  // 可选的赔率格式
        public Map<String, Boolean> ff;  // feature flags

        // 私有字段，由类内部管理
        private long exp;           // 过期时间（秒）
        private String jti;         // JWT唯一ID
        private long iat;           // 令牌创建时间（秒）

        public JWData() {
            // 初始化 feature flags，设置默认值
            ff = new HashMap<>();
            ff.put("is_cashout_available", false);
            ff.put("is_match_tracker_available", false);

            // 设置当前时间（秒）
            this.iat = System.currentTimeMillis() / 1000;

            // 设置默认过期时间为1小时后
            this.exp = this.iat + 3600;

            // 自动生成JWT ID
            this.jti = generateUniqueJwtId();
        }

        /**
         * 创建具有自定义过期时间的JWData
         */
        public JWData(String brandId, String playerId, String playerName,
                      String language, String currency, long expirationTimeInSeconds) {
            this();  // 调用默认构造函数初始化基础字段
            this.iss = brandId;
            this.sub = playerId;
            this.name = playerName;
            this.lang = language;
            this.currency = currency;
            this.exp = expirationTimeInSeconds;
        }

        /**
         * 创建使用默认过期时间的JWData（1小时）
         */
        public JWData(String brandId, String playerId, String playerName,
                      String language, String currency) {
            this();  // 使用默认构造函数（包括默认过期时间）
            this.iss = brandId;
            this.sub = playerId;
            this.name = playerName;
            this.lang = language;
            this.currency = currency;
        }
    }
}