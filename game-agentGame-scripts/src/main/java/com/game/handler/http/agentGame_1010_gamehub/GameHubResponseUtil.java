package com.game.handler.http.agentGame_1010_gamehub;

import com.game.engine.utils.MsgUtil;
import io.netty.channel.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 1GameHub GAP 通用响应工具
 * - 成功：{ "status":200, ... }
 * - 错误：{ "status":500, "error":{ code, message, display, action } }
 * 基于 GameHubConstants.ErrorCode 枚举，统一错误码与返回结构
 */
public final class GameHubResponseUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(GameHubResponseUtil.class);

    private GameHubResponseUtil() {}

    /* ---------- 成功响应（通用） ---------- */
    public static void sendOk(Channel session, Map<String, Object> data) {
        Map<String, Object> resp = new LinkedHashMap<>();
        resp.put("status", 200);
        if (data != null) {
            resp.putAll(data); // balance/currency 等字段直接放在顶层
        }
        LOGGER.info("[GameHub][OK] {}", resp);
        MsgUtil.responseHttp(resp, session);
    }

    /* Balance 成功返回：{status:200, balance:xxx, currency:"EUR"} */
    public static void sendBalanceOk(Channel session, long balanceCents, String currency) {
        Map<String, Object> data = new LinkedHashMap<>();
        data.put("balance", balanceCents);
        data.put("currency", currency);
        sendOk(session, data);
    }

    /* ---------- 错误响应（统一使用枚举） ---------- */
    public static void sendError(Channel session, String errorCode) {
        Map<String, Object> resp = new LinkedHashMap<>();
        resp.put("status", 500);

        Map<String, Object> err = new LinkedHashMap<>();
        err.put("code", errorCode);  // 传入的错误代码
        err.put("message", getErrorMessage(errorCode));  // 错误描述（可以通过方法动态获取）
        err.put("display", shouldDisplay(errorCode));  // 是否显示（可以通过方法动态判断）
        err.put("action", getErrorAction(errorCode));  // 动作（可以通过方法动态获取）
        resp.put("error", err);

        LOGGER.warn("[GameHub][ERR] {}", resp);
        MsgUtil.responseHttp(resp, session);
    }

    /**
     * 新增重载方法，兼容传入 GameHubConstants.ErrorCode 枚举类型
     */
    public static void sendError(Channel session, GameHubConstants.ErrorCode errorCode) {
        // 直接调用传入字符串错误码的方法
        sendError(session, errorCode.name());
    }

    /**
     * 通过错误码获取错误信息
     */
    private static String getErrorMessage(String errorCode) {
        switch (errorCode) {
            case "ERR001":
                return "Unknown error occurred.";
            case "ERR002":
                return "Session has timed out. Please log in again to continue playing.";
            case "ERR003":
                return "Insufficient funds to place current wager. Please reduce the stake or add more funds to your balance.";
            case "ERR004":
                return "This wagering will exceed your wagering limitation. Please try a smaller amount or increase the limit.";
            case "ERR005":
                return "Player authentication failed.";
            case "ERR006":
                return "Unauthorized request.";
            case "ERR008":
                return "Unsupported currency.";
            case "ERR009":
                return "Bonus bet max restriction.";
            default:
                return "Unknown error occurred.";  // Default error message
        }
    }

    /**
     * 通过错误码判断是否显示该错误信息
     */
    private static boolean shouldDisplay(String errorCode) {
        return !"ERR006".equals(errorCode);  // ERR006 (Unauthorized) won't be displayed, others will be
    }

    /**
     * 通过错误码获取相关的动作
     */
    private static String getErrorAction(String errorCode) {
        switch (errorCode) {
            case "ERR001":
            case "ERR002":
            case "ERR003":
            case "ERR004":
            case "ERR005":
            case "ERR008":
            case "ERR009":
                return "restart";
            case "ERR006":
                return "restart";  // Unauthorized action will also restart
            default:
                return "restart";  // Default action
        }
    }

    /* ---------- 便捷方法 ---------- */
    /**
     * 直接映射 validation 返回的错误码，简化错误处理流程
     */
    public static void respondByValidationError(Channel session, String err) {
        if (err == null) {
            sendError(session, "ERR001");  // Default to ERR001 if error is null
            return;
        }

        // 直接传入错误码进行处理
        sendError(session, err);  // Send the error based on the provided error code
    }
}
