package com.game.handler.http.agentGame_1010_gamehub;

import com.game.engine.utils.MsgUtil;
import io.netty.channel.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 1GameHub GAP 通用响应工具
 * - 成功：{ "status":200, ... }
 * - 错误：{ "status":500, "error":{ code, message, display, action } }
 * 基于 GameHubConstants.ErrorCode 枚举，统一错误码与返回结构
 */
public final class GameHubResponseUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(GameHubResponseUtil.class);

    private GameHubResponseUtil() {}

    /* ---------- 成功响应（通用） ---------- */
    public static void sendOk(Channel session, Map<String, Object> data) {
        Map<String, Object> resp = new LinkedHashMap<>();
        resp.put("status", 200);
        if (data != null) {
            resp.putAll(data); // balance/currency 等字段直接放在顶层
        }
        LOGGER.info("[GameHub][OK] {}", resp);
        MsgUtil.responseHttp(resp, session);
    }

    /* Balance 成功返回：{status:200, balance:xxx, currency:"EUR"} */
    public static void sendBalanceOk(Channel session, long balanceCents, String currency) {
        Map<String, Object> data = new LinkedHashMap<>();
        data.put("balance", balanceCents);
        data.put("currency", currency);
        sendOk(session, data);
    }

    /* ---------- 错误响应（统一使用枚举） ---------- */
    public static void sendError(Channel session, GameHubConstants.ErrorCode errorCode) {
        Map<String, Object> resp = new LinkedHashMap<>();
        resp.put("status", errorCode.getStatus());

        Map<String, Object> err = new LinkedHashMap<>();
        err.put("code", errorCode.name());
        err.put("message", errorCode.getMessage());
        err.put("display", errorCode.isDisplay());
        err.put("action", errorCode.getAction());
        resp.put("error", err);

        LOGGER.warn("[GameHub][ERR] {}", resp);
        MsgUtil.responseHttp(resp, session);
    }

    /* ---------- 便捷方法 ---------- */
    public static void errUnauthorized(Channel session) {
        sendError(session, GameHubConstants.ErrorCode.ERR006);
    }

    public static void errUnknown(Channel session) {
        sendError(session, GameHubConstants.ErrorCode.ERR001);
    }

    public static void errInsufficientFunds(Channel session) {
        sendError(session, GameHubConstants.ErrorCode.ERR003);
    }

    /**
     * 把 GameHubSecurityUtil.validate(...) 返回的 error 文本，映射为文档错误码并回包
     * 统一入口，业务层不再写 if/else。
     */
    public static void respondByValidationError(Channel session, String err) {
        if (err == null) {
            errUnknown(session);
            return;
        }
        // 安全/权限类
        if (err.startsWith("ip_not_allowed:") || "hmac_mismatch".equals(err)) {
            errUnauthorized(session); // ERR006
            return;
        }
        // 玩家身份类
        if ("player_not_found".equals(err) || "invalid_player_id".equals(err)) {
            sendError(session, GameHubConstants.ErrorCode.ERR005); // Player authentication failed
            return;
        }
        // 入参/平台配置类（归为通用未知错误）
        if ("missing_or_invalid_basic_params".equals(err)
                || "missing_transaction_params".equals(err)
                || "invalid_ext_round_finished".equals(err)
                || "invalid_amount".equals(err)
                || "missing_amount".equals(err)
                || "platform_config_not_found".equals(err)) {
            errUnknown(session); // ERR001
            return;
        }
        // 兜底
        errUnknown(session); // ERR001
    }
}
