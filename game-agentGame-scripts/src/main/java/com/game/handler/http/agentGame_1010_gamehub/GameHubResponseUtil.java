package com.game.handler.http.agentGame_1010_gamehub;

import com.game.engine.utils.MsgUtil;
import io.netty.channel.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 1GameHub GAP 通用响应工具
 * - 成功：{ "status":200, ... }
 * - 错误：{ "status":500, "error":{ code, message, display, action } }
 * 基于 GameHubConstants.ErrorCode 枚举，统一错误码与返回结构
 */
public final class GameHubResponseUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(GameHubResponseUtil.class);

    private GameHubResponseUtil() {}

    /* ---------- 成功响应（通用） ---------- */
    public static void sendOk(Channel session, Map<String, Object> data) {
        Map<String, Object> resp = new LinkedHashMap<>();
        resp.put("status", 200);
        if (data != null) {
            resp.putAll(data); // balance/currency 等字段直接放在顶层
        }
        LOGGER.info("[GameHub][OK] {}", resp);
        MsgUtil.responseHttp(resp, session);
    }

    /* Balance 成功返回：{status:200, balance:xxx, currency:"EUR"} */
    public static void sendBalanceOk(Channel session, long balanceCents, String currency) {
        Map<String, Object> data = new LinkedHashMap<>();
        data.put("balance", balanceCents);
        data.put("currency", currency);
        sendOk(session, data);
    }

    /* ---------- 错误响应（统一使用枚举） ---------- */
    public static void sendError(Channel session, GameHubConstants.ErrorCode errorCode) {
        Map<String, Object> resp = new LinkedHashMap<>();
        resp.put("status", errorCode.getStatus());

        Map<String, Object> err = new LinkedHashMap<>();
        err.put("code", errorCode.name());
        err.put("message", errorCode.getMessage());
        err.put("display", errorCode.isDisplay());
        err.put("action", errorCode.getAction());
        resp.put("error", err);

        LOGGER.warn("[GameHub][ERR] {}", resp);
        MsgUtil.responseHttp(resp, session);
    }

    /* ---------- 便捷方法 ---------- */
    public static void errUnauthorized(Channel session, String detail) {
        String msg = GameHubConstants.ErrorCode.ERR006.getMessage();
        if (detail != null && !detail.isEmpty()) {
            msg += " - " + detail;
        }
        sendError(session, GameHubConstants.ErrorCode.ERR006);
    }

    public static void errUnknown(Channel session, String detail) {
        String msg = (detail == null || detail.isEmpty())
                ? GameHubConstants.ErrorCode.ERR001.getMessage()
                : detail;
        sendError(session, GameHubConstants.ErrorCode.ERR001);
    }

    public static void errInsufficientFunds(Channel session) {
        sendError(session, GameHubConstants.ErrorCode.ERR003);
    }
}
