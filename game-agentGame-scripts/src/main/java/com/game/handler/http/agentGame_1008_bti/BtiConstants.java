package com.game.handler.http.agentGame_1008_bti;

/**
 * Funky Games API 常量定义
 * 包含错误代码、认证信息等常量
 */
public class BtiConstants {

    /**
     * Funky API 错误代码枚举
     * 基于官方文档定义
     */
    public enum ErrorCode {
        SUCCESS(0, "Success"),                                    // 成功
        CUSTOMER_NOT_FOUND(-2, "Customer Not Found"),             // 未找到客户
        TOKEN_NOT_VALID(-3, "TokenNotValid"),                      // 令牌无效
        INSUFFICIENT_FUNDS(-4, "InsufficientFunds"),               // 资金不足
        RESTRICTED_CUSTOMER(-6, "RestrictedCustomer"),             // 受限客户
        NO_EXISTING_SESSION(-23, "NoExistingSession");             // 没有现有会话


        private final int code;
        private final String message;

        ErrorCode(int code, String message) {
            this.code = code;
            this.message = message;
        }

        public int getCode() {
            return code;
        }

        public String getMessage() {
            return message;
        }

    }

    /**
     * Funky API 路径常量
     */
    public static class Paths {
        public static final String RESERVE = "/zenith/bti/reserve";
        public static final String CANCEL_RESERVE = "/zenith/bti/cancelreserve";
        public static final String DEBIT_RESERVE = "/zenith/bti/debitreserve";
        public static final String COMMIT_RESERVE = "/zenith/bti/commitreserve";
        public static final String CREDIT_CUSTOMER = "/zenith/bti/creditcustomer";
        public static final String DEBIT_CUSTOMER = "/zenith/bti/debitcustomer";
        public static final String VALIDATE_TOKEN = "/zenith/bti/ValidateToken";
        public static final String BALANCE = "/zenith/bti/balance";
    }
}
