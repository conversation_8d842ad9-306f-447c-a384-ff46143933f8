package com.game.handler.http.agentGame_1008_bti;

import com.game.engine.utils.HttpUtils11;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.handler.http.agentGame_1008_bti.BtiConstants;
import io.netty.channel.Channel;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * BTi API 通用工具类
 */
public class BtiUtils {
    private static final Logger LOGGER = LoggerFactory.getLogger(BtiUtils.class);

    public static String toTextPlain(Map<String, Object> params) {
        if (params == null || params.isEmpty()) {
            return "";
        }

        StringBuilder result = new StringBuilder();
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue() != null ? String.valueOf(entry.getValue()) : "";

            result.append(key).append("=").append(value).append("\r\n");
        }

        return result.toString();
    }

    public static void sendErrorResponse(Channel session, BtiConstants.ErrorCode errorCode) {
        sendErrorResponse(session, errorCode, errorCode.getMessage());
    }

    public static void sendErrorResponse(Channel session, BtiConstants.ErrorCode errorCode, double balance) {
        sendErrorResponse(session, errorCode, errorCode.getMessage(), balance);
    }

    public static void sendErrorResponse(Channel session, BtiConstants.ErrorCode errorCode, String errorMessage) {
        sendErrorResponse(session, errorCode, errorMessage, 0.00);
    }

    public static void sendErrorResponse(Channel session, BtiConstants.ErrorCode errorCode, String errorMessage, double balance) {
        Map<String, Object> responseMap = new LinkedHashMap<>();
        responseMap.put("error_code", errorCode.getCode());
        responseMap.put("error_message", errorMessage);
        responseMap.put("trx_id", TimeUtil.currentTimeMillis());
        responseMap.put("balance", String.format("%.2f", balance)); // 格式化为两位小数

        LOGGER.info("sendErrorResponse params: {}", responseMap);

        MsgUtil.responseHttp(toTextPlain(responseMap), session, HttpUtils11.HTTP_CONTENT_TYPE_PLAIN);
    }

    public static void sendSuccessResponse(Channel session, double balance) {
        sendSuccessResponse(session, "OK", balance);
    }

    public static void sendSuccessResponse(Channel session, String errorMessage, double balance) {
        Map<String, Object> responseMap = new LinkedHashMap<>();
        responseMap.put("error_code", BtiConstants.ErrorCode.SUCCESS.getCode());
        responseMap.put("error_message", errorMessage);
        responseMap.put("trx_id", TimeUtil.currentTimeMillis());
        responseMap.put("balance", String.format("%.2f", balance)); // 格式化为两位小数

        LOGGER.info("sendSuccessResponse params: {}", responseMap);

        MsgUtil.responseHttp(toTextPlain(responseMap), session, HttpUtils11.HTTP_CONTENT_TYPE_PLAIN);
    }

    public static String encodeCustId(int currencyId, long playerId, Boolean bonus) {
        return currencyId + "_" + playerId + "_" + bonus;
    }

    public static Triple<Integer, Long, Boolean> decodeCustId(String custId) {
        String[] parts = custId.split("_");

        if (parts.length != 3) {
            throw new IllegalArgumentException("Invalid format: " + custId);
        }

        int currencyId = Integer.parseInt(parts[0]);
        long playerId = Long.parseLong(parts[1]);
        boolean bonus = Boolean.parseBoolean(parts[2]);

        return Triple.of(currencyId, playerId, bonus);
    }

    public static Player getPlayer(String custId) {
        final int currencyId;
        final long playerId;
        boolean bonus;

        final Player player;
        try {
            Triple<Integer, Long, Boolean> decoded = BtiUtils.decodeCustId(custId);
            currencyId = decoded.getLeft();
            playerId = decoded.getMiddle();
            bonus = decoded.getRight();

            LOGGER.info("getPlayer params: {}, {}, {}", currencyId, playerId, bonus);

            player = AgentGameMrg.getInstance().findDbPlayer(playerId);
            if (player == null) {
                throw new NumberFormatException("Player not found");
            }
        } catch (NumberFormatException e) {
            LOGGER.warn("Invalid playerId or not found: {}", custId);
            return null;
        }

        player.setBonus(bonus);
        player.setPlayerCurrencyId(currencyId);
        player.setGameCurrencyId(currencyId);

        return player;
    }
}