package com.game.handler.http.agentGame_1002_st8;

import com.game.engine.io.handler.HttpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.FreeGameInfo;
import com.game.entity.player.GameInfo;
import com.game.entity.player.Player;
import com.game.enums.ChangeType;
import com.game.gamesr.NotifyData;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.scripts.IAgentGameScript;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;
import java.util.Map;

//http://127.0.0.1:8680/st8/payout
@IHandlerEntity(path = "/st8/payout", desc = "免费")
public class ReqPayoutHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqPayoutHandler.class);

    @Override
    public void run() {
        final Map<String, Object> responseMap = new LinkedHashMap<>();
        try {
            LOGGER.info("ReqPayoutHandler，params：{}", JsonUtils.writeAsJson(paramsMap));

            paramsMap.remove("userAgent");

            final String players = (String) paramsMap.get("player");
            final String currency = (String) paramsMap.get("currency");
            final String amount = (String) paramsMap.get("amount");
            final String transaction_id = (String) paramsMap.get("transaction_id");

            final long playerId = AgentGameMrg.getInstance().getAgentGamePlayerId(players);
            final Player player = AgentGameMrg.getInstance().findDbPlayer(playerId);
            if (player == null) {
                responseMap.put("status", "error");
                responseMap.put("reason", "player_not_found");
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            final double realBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.realBalance(player));

            double balance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.amountTransformUsd(player, realBalance));

            balance = BigDecimalUtils.add(balance, Double.parseDouble(amount), 2);

//            responseMap.put("status", "ok");
//            responseMap.put("currency", currency);
//            responseMap.put("balance", BigDecimalUtils.round(balance, 2) + "");
//            MsgUtil.responseHttp(responseMap, session);

            final double win = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.usdTransformAmount(player, Double.parseDouble(amount)));

            final double finalBalance = balance;
            final double balances = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.usdTransformAmount(player, finalBalance));

            if (win > 0) {
                @SuppressWarnings("unchecked") final Map<String, Object> bonus = (Map<String, Object>) paramsMap.get("bonus");
                if (bonus != null) {
                    final String round = (String) bonus.get("bonus_id");
                    ScriptLoader.getInstance().consumerScript("AgentGameScript",
                            (IAgentGameScript script) -> script.freeAddGameNote(player, round, transaction_id, 2, win, balances));
                }
            }

            final FreeGameInfo freeGameInfo = player.getFreeGameInfo(player.getPlayerCurrencyId());
            final GameInfo gameInfo = freeGameInfo.getFreeGame(player.getGameId());

            final NotifyData notifyData = new NotifyData();
            notifyData.setType(ChangeType.Free.getType())
                    .setPid(playerId)
                    .setWin(win)
                    .setFreeTimes(-gameInfo.getFreeTimes())
                    .setCurrency(currency)
                    .setUpdSessionId(MsgUtil.getSessionID(session))
                    .setGameId(player.getGameId())
                    .setCurrencyId(player.getPlayerCurrencyId());

            ScriptLoader.getInstance().consumerScript("AgentGameScript",
                    (IAgentGameScript script) -> script.updateCurrency(notifyData));
        } catch (Exception e) {
            LOGGER.error("ReqPayoutHandler", e);
            responseMap.put("status", "unknown");
            MsgUtil.responseHttp(responseMap, session);
        }
    }

}
