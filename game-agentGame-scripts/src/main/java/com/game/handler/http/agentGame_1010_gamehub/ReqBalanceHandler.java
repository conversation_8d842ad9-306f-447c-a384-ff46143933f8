package com.game.handler.http.agentGame_1010_gamehub;

import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.JsonUtils;
import com.game.entity.player.Player;
import com.game.gamesr.scripts.IAgentGameScript;
import io.netty.channel.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * GameHub-查询余额（balance）
 * 请求：action=balance, player_id, currency, hash
 * 响应：{ status:200, balance:<int cents>, currency:"..." }
 *
 * 说明：
 * - 不再继承 HttpHandler，不再使用 @IHandlerEntity
 * - 仅提供静态入口 process(...)，供单一URI分发器 ReqCommonHandler 调用
 */
public final class ReqBalanceHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqBalanceHandler.class);

    private ReqBalanceHandler() {}

    /** 单一URI分发调用入口 */
    public static void process(Channel session, Map<String, Object> paramsMap, String handlerClassNameForPlatform) {
        try {
            if (ConstantConfig.getInstance().isAgentDebug()) {
                LOGGER.info("[GameHub][balance] params: {}", JsonUtils.writeAsJson(paramsMap));
            }

            // 统一校验（平台配置 / 白名单 / HMAC / 参数解析 / 玩家存在）
            GameHubSecurityUtil.Validation v = GameHubSecurityUtil.validate(
                    paramsMap,
                    session,
                    handlerClassNameForPlatform, // 建议传入 ReqCommonHandler.class.getSimpleName()
                    "balance",
                    GameHubSecurityUtil.Options.forBalance()
            );
            if (!v.ok) {
                GameHubResponseUtil.respondByValidationError(session, v.error);
                return;
            }

            // 计算余额并回包
            handle(session, v.player, v.currency);

        } catch (Exception e) {
            LOGGER.error("[GameHub][balance.process] error", e);
            GameHubResponseUtil.sendError(session, GameHubConstants.ErrorCode.ERR001);
        }
    }

    /** 计算余额并回包 */
    private static void handle(Channel session, Player player, String currency) {
        final double realBalance = ScriptLoader.getInstance().functionScript(
                "AgentGameScript", (IAgentGameScript script) -> script.realBalance(player));

        final double balanceDisplay = ScriptLoader.getInstance().functionScript(
                "AgentGameScript", (IAgentGameScript script) -> script.amountTransformUsd(player, realBalance));

        final long balanceCents = Math.round(BigDecimalUtils.round(balanceDisplay, 2) * 100);
        GameHubResponseUtil.sendBalanceOk(session, balanceCents, currency);
    }
}
