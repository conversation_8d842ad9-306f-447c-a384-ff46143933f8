package com.game.handler.http.agentGame_1010_gamehub;

import com.game.c_entity.middleplatform.C_BaseGamePlatform;
import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.enums.AgentGame;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.manager.DataAgentGameMrg;
import com.game.gamesr.scripts.IAgentGameScript;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

@IHandlerEntity(path = GameHubConstants.Paths.BALANCE, desc = "GameHub-查询余额")
public class ReqBalanceHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqBalanceHandler.class);

    @Override
    public void run() {
        try {
            if (ConstantConfig.getInstance().isAgentDebug()) {
                LOGGER.info("[GameHub][balance] params: {}", JsonUtils.writeAsJson(paramsMap));
            }

            // 1) 业务校验：失败会直接回包并返回 null；成功返回 Player
            Player player = validateAndGetPlayer();
            if (player == null) return;

            // 2) 处理：计算余额并返回成功
            handle(player);

        } catch (Exception e) {
            LOGGER.error("[GameHub][balance] error", e);
            GameHubResponseUtil.errUnknown(session, "unknown");
        }
    }

    /**
     * 业务侧校验（非通用）：参数校验、平台配置、IP 白名单（按你给的写法）、HMAC 校验、玩家存在性
     * 成功：返回 Player；失败：已回包并返回 null
     */
    private Player validateAndGetPlayer() {
        // ----- 基础参数 -----
        final String action = (String) paramsMap.get("action");
        final String playerIdStr = (String) paramsMap.get("player_id");
        final String currency = (String) paramsMap.get("currency");
        final String hash = (String) paramsMap.get("hash");

        if (!"balance".equalsIgnoreCase(action)
                || StringUtil.isNullOrEmpty(playerIdStr)
                || StringUtil.isNullOrEmpty(currency)
                || StringUtil.isNullOrEmpty(hash)) {
            GameHubResponseUtil.errUnknown(session, "missing or invalid params");
            return null;
        }

        final long playerId;
        try {
            playerId = Long.parseLong(playerIdStr);
        } catch (Exception e) {
            GameHubResponseUtil.errUnknown(session, "invalid player_id");
            return null;
        }

        // ----- 平台配置（取 secret / ip 列表等） -----
        final C_BaseGamePlatform cfg = DataAgentGameMrg.getInstance()
                .findC_BaseGamePlatformSupplierId(this.getClass().getSimpleName(), AgentGame.GAMEHUB.getType());
        if (cfg == null || StringUtil.isNullOrEmpty(cfg.getSecretKey())) {
            GameHubResponseUtil.errUnknown(session, "platform config not found");
            return null;
        }
        final String secret = cfg.getSecretKey();

        // ----- IP 白名单校验（注意：getIpBlacklist 实际返回的是白名单列表，命名有误）-----
        // 如果平台配置中设置了 IP 白名单列表（字段名叫 getIpBlacklist，但实际是白名单）
        if (!cfg.getIpBlacklist().isEmpty()) { // 实际是白名单，命名遗留问题
            String ip = MsgUtil.getClientIp(session);
            final boolean inWhiteList = ScriptLoader.getInstance().functionScript(
                    "AgentGameScript",
                    (IAgentGameScript script) -> script.isIpBlacklist(ip, AgentGame.GAMEHUB.getType()) // 方法名同样沿用遗留命名
            );
            if (!inWhiteList) {
                LOGGER.warn("[GameHub][balance] not in IP whitelist: {}", ip);
                GameHubResponseUtil.errUnauthorized(session, "ip_not_allowed:" + ip);
                return null;
            }
        }

        // ----- HMAC 签名校验（仍然走统一工具类）-----
        if (!GameHubSecurityUtil.verifySignature(paramsMap, hash, secret)) {
            GameHubResponseUtil.errUnauthorized(session, "HMAC mismatch");
            return null;
        }

        // ----- 玩家必须存在 -----
        final Player player = AgentGameMrg.getInstance().findDbPlayer(playerId);
        if (player == null) {
            GameHubResponseUtil.errUnknown(session, "player_not_found");
            return null;
        }
        return player;
    }

    /**
     * 真正的业务处理：计算余额、换算币种并回成功包
     */
    private void handle(Player player) {
        final String currency = (String) paramsMap.get("currency");

        final double realBalance = ScriptLoader.getInstance().functionScript(
                "AgentGameScript", (IAgentGameScript script) -> script.realBalance(player));

        final double balanceInCurrency = ScriptLoader.getInstance().functionScript(
                "AgentGameScript", (IAgentGameScript script) -> script.amountTransformUsd(player, realBalance));

        long balanceCents = Math.round(BigDecimalUtils.round(balanceInCurrency, 2) * 100);

        GameHubResponseUtil.sendBalanceOk(session, balanceCents, currency);
    }
}
