package com.game.handler.http.agentGame_1010_gamehub;

import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.JsonUtils;
import com.game.entity.player.Player;
import com.game.gamesr.scripts.IAgentGameScript;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * GameHub-查询余额（balance）
 * 请求：action=balance, player_id, currency, hash
 * 响应：{ status:200, balance:<int cents>, currency:"..." }
 */
@IHandlerEntity(path = GameHubConstants.Paths.BALANCE, desc = "GameHub-查询余额")
public class ReqBalanceHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqBalanceHandler.class);

    @Override
    public void run() {
        try {
            if (ConstantConfig.getInstance().isAgentDebug()) {
                LOGGER.info("[GameHub][balance] params: {}", JsonUtils.writeAsJson(paramsMap));
            }

            // 1) 统一校验（平台配置 / 白名单 / HMAC / 参数解析 / 玩家存在）
            GameHubSecurityUtil.Validation v = GameHubSecurityUtil.validate(
                    paramsMap,
                    session,
                    this.getClass().getSimpleName(),
                    "balance",
                    GameHubSecurityUtil.Options.forBalance()
            );
            if (!v.ok) {
                GameHubResponseUtil.respondByValidationError(session, v.error);
                return;
            }

            // 2) 计算余额（展示币：元）→ 对外返回分
            handle(v.player, v.currency);

        } catch (Exception e) {
            LOGGER.error("[GameHub][balance] error", e);
            GameHubResponseUtil.sendError(session, GameHubConstants.ErrorCode.ERR001);
        }
    }

    /** 计算余额并回包 */
    private void handle(Player player, String currency) {
        final double realBalance = ScriptLoader.getInstance().functionScript(
                "AgentGameScript", (IAgentGameScript script) -> script.realBalance(player));

        final double balanceDisplay = ScriptLoader.getInstance().functionScript(
                "AgentGameScript", (IAgentGameScript script) -> script.amountTransformUsd(player, realBalance));

        final long balanceCents = Math.round(BigDecimalUtils.round(balanceDisplay, 2) * 100);
        GameHubResponseUtil.sendBalanceOk(session, balanceCents, currency);
    }
}
