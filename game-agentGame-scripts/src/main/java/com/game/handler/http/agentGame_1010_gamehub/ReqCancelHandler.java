package com.game.handler.http.agentGame_1010_gamehub;

import com.game.c_entity.middleplatform.C_BaseGamePlatform;
import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.MsgUtil;
import com.game.entity.game.GameNote;
import com.game.entity.player.Player;
import com.game.enums.AgentGame;
import com.game.enums.ChangeType;
import com.game.enums.Currency;
import com.game.gamesr.NotifyData;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.manager.DataAgentGameMrg;
import com.game.gamesr.scripts.IAgentGameScript;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * GameHub GAP: 撤销/退款 - cancel
 * - 必填：action=cancel, amount, player_id, transaction_id, currency, game_id, round_id, ext_round_id, ext_round_finished, hash
 * - 可选：freerounds_id, extra
 * - 返回：{ "status":200, "balance":<int cents>, "currency":"..." }
 * - 金额单位：整数“分”（minor units）
 */
@IHandlerEntity(path = GameHubConstants.Paths.CANCEL, desc = "GameHub-撤销/退款")
public class ReqCancelHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqCancelHandler.class);

    /* -------------------- 内部类型 -------------------- */
    private static final class CancelContext {
        long playerId;
        String currency;
        String transactionId;
        String gameId;
        String roundId;
        String extRoundId;
        int extFinished;   // 0=open, 1=closed
        long amountCents;   // 整数分（minor units）
        String freeroundsId;  // 可为空
    }

    /**
     * 当前请求上下文（解析后只在本请求内使用）
     */
    private final CancelContext ctx = new CancelContext();

    @Override
    public void run() {
        try {
            if (ConstantConfig.getInstance().isAgentDebug()) {
                LOGGER.info("[GameHub][cancel] params: {}", JsonUtils.writeAsJson(paramsMap));
            }

            // 1) 业务侧校验（失败直接回包，成功返回 Player 并填充 ctx）
            final Player player = validateAndGetPlayer();
            if (player == null) return;

            // 2) 处理撤销/退款（幂等→加回→落账→通知→回包）
            handle(player);

        } catch (Exception e) {
            LOGGER.error("[GameHub][cancel] error", e);
            GameHubResponseUtil.errUnknown(session, "unknown");
        }
    }

    /**
     * 业务校验（非通用）：参数完整性、平台配置、白名单（命名遗留）、HMAC、玩家存在性
     */
    private Player validateAndGetPlayer() {
        final String action = (String) paramsMap.get("action");
        final String playerIdStr = (String) paramsMap.get("player_id");
        final String currency = (String) paramsMap.get("currency");
        final String transactionId = (String) paramsMap.get("transaction_id");
        final String gameId = (String) paramsMap.get("game_id");
        final String roundId = (String) paramsMap.get("round_id");
        final String extRoundId = (String) paramsMap.get("ext_round_id");
        final String extFinishedStr = (String) paramsMap.get("ext_round_finished");
        final String amountStr = (String) paramsMap.get("amount");
        final String freeroundsId = (String) paramsMap.get("freerounds_id");
        final String hash = (String) paramsMap.get("hash");

        if (!"cancel".equalsIgnoreCase(action)
                || StringUtil.isNullOrEmpty(playerIdStr)
                || StringUtil.isNullOrEmpty(currency)
                || StringUtil.isNullOrEmpty(transactionId)
                || StringUtil.isNullOrEmpty(gameId)
                || StringUtil.isNullOrEmpty(roundId)
                || StringUtil.isNullOrEmpty(extRoundId)
                || StringUtil.isNullOrEmpty(extFinishedStr)
                || StringUtil.isNullOrEmpty(amountStr)
                || StringUtil.isNullOrEmpty(hash)) {
            GameHubResponseUtil.errUnknown(session, "missing or invalid params");
            return null;
        }

        final long playerId;
        final long amountCents;
        final int extFinished;
        try {
            playerId = Long.parseLong(playerIdStr);
            amountCents = Long.parseLong(amountStr);
            extFinished = Integer.parseInt(extFinishedStr);
        } catch (Exception e) {
            GameHubResponseUtil.errUnknown(session, "invalid numeric param");
            return null;
        }
        if (amountCents < 0 || (extFinished != 0 && extFinished != 1)) {
            GameHubResponseUtil.errUnknown(session, "invalid amount or ext_round_finished");
            return null;
        }

        // 平台配置（取 secret / 白名单）
        final C_BaseGamePlatform cfg = DataAgentGameMrg.getInstance()
                .findC_BaseGamePlatformSupplierId(this.getClass().getSimpleName(), AgentGame.GAMEHUB.getType());
        if (cfg == null || StringUtil.isNullOrEmpty(cfg.getSecretKey())) {
            GameHubResponseUtil.errUnknown(session, "platform config not found");
            return null;
        }
        final String secret = cfg.getSecretKey();

        // IP 白名单（注意：getIpBlacklist 实际返回白名单，命名历史遗留）
        if (!cfg.getIpBlacklist().isEmpty()) {
            final String ip = MsgUtil.getClientIp(session);
            final boolean inWhiteList = ScriptLoader.getInstance().functionScript(
                    "AgentGameScript",
                    (IAgentGameScript script) -> script.isIpBlacklist(ip, AgentGame.GAMEHUB.getType()));
            if (!inWhiteList) {
                LOGGER.warn("[GameHub][cancel] not in IP whitelist: {}", ip);
                GameHubResponseUtil.errUnauthorized(session, "ip_not_allowed:" + ip);
                return null;
            }
        }

        // HMAC
        if (!GameHubSecurityUtil.verifySignature(paramsMap, hash, secret)) {
            GameHubResponseUtil.errUnauthorized(session, "HMAC mismatch");
            return null;
        }

        // 玩家必须存在
        final Player player = AgentGameMrg.getInstance().findDbPlayer(playerId);
        if (player == null) {
            GameHubResponseUtil.errUnknown(session, "player_not_found");
            return null;
        }

        // 上下文
        ctx.playerId = playerId;
        ctx.currency = currency;
        ctx.transactionId = transactionId;
        ctx.gameId = gameId;
        ctx.roundId = roundId;
        ctx.extRoundId = extRoundId;
        ctx.extFinished = extFinished;
        ctx.amountCents = amountCents;
        ctx.freeroundsId = StringUtil.isNullOrEmpty(freeroundsId) ? null : freeroundsId;

        return player;
    }

    /**
     * 撤销/退款处理：
     * - 幂等：roundId + transactionId
     * - 余额：把 amount 加回（展示币种：元），回包“分”
     * - 记账：updateGameNote / updateCurrency（ChangeType.refund）
     */
    private void handle(final Player player) {
        // 1) 当前余额（展示币种：元）
        final double realBalance = ScriptLoader.getInstance().functionScript(
                "AgentGameScript", (IAgentGameScript script) -> script.realBalance(player));
        final double balanceDisplay = ScriptLoader.getInstance().functionScript(
                "AgentGameScript", (IAgentGameScript script) -> script.amountTransformUsd(player, realBalance));
        final long currentCents = Math.round(BigDecimalUtils.round(balanceDisplay, 2) * 100);

        // 2) 幂等判断：round + transaction
        GameNote gameNote = AgentGameMrg.getInstance().findRoundIdGameNote(player, ctx.roundId);
        if (gameNote != null) {
            LOGGER.warn("[GameHub][cancel] duplicate: roundId={}, transactionId={}", ctx.roundId, ctx.transactionId);
            GameHubResponseUtil.sendBalanceOk(session, currentCents, ctx.currency);
            return;
        }

        ScriptLoader.getInstance().consumerScript("AgentGameScript",
                (IAgentGameScript script) -> script.updateGameNoteStatus(player, ctx.roundId, 3));

        // 7) 货币名称（容错：避免枚举映射异常）
        String currencyName = "";
        try {
            final Currency c = Currency.valueOf(gameNote.getCurrencyId());
            if (c != null) currencyName = c.name();
        } catch (Exception ignore) {
        }

        // 8) 通知（退款/撤销）——对齐你给的 Funky 示例：ChangeType.refund
        final NotifyData notifyData = new NotifyData();
        notifyData.setType(ChangeType.refund.getType())
                .setPid(player.getPlayerId())
                .setPlayerName(player.getPlayerName())
                .setNoteId(String.valueOf(gameNote.getNoteId()))
                .setCurrency(currencyName)
                .setBetAmount(gameNote.getBetAmount())
                .setValidBets(gameNote.getValidBets())
                .setWin(gameNote.getWin())
                .setTotalWin(gameNote.getWin())
                .setUpdSessionId(MsgUtil.getSessionID(session))
                .setGameId(gameNote.getGameId())
                .setCurrencyId(gameNote.getCurrencyId());

        ScriptLoader.getInstance().consumerScript("AgentGameScript",
                (IAgentGameScript script) -> script.updateCurrency(notifyData));
    }
}
