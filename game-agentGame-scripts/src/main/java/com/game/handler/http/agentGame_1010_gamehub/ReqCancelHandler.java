package com.game.handler.http.agentGame_1010_gamehub;

import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.MsgUtil;
import com.game.entity.game.GameNote;
import com.game.entity.player.Player;
import com.game.enums.ChangeType;
import com.game.enums.Currency;
import com.game.gamesr.NotifyData;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.scripts.IAgentGameScript;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * GameHub GAP: 撤销/退款 - cancel
 * 请求：action=cancel, amount, player_id, transaction_id, currency, game_id, round_id, ext_round_id, ext_round_finished, hash
 * 返回：{ "status":200, "balance":<int cents>, "currency":"..." }
 * 金额单位：整数“分”（minor units）
 */
@IHandlerEntity(path = GameHubConstants.Paths.CANCEL, desc = "GameHub-撤销/退款")
public class ReqCancelHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqCancelHandler.class);

    @Override
    public void run() {
        try {
            if (ConstantConfig.getInstance().isAgentDebug()) {
                LOGGER.info("[GameHub][cancel] params: {}", JsonUtils.writeAsJson(paramsMap));
            }

            // 1) 统一校验（平台配置 / 白名单 / HMAC / 参数解析 / 玩家存在）
            GameHubSecurityUtil.Validation v = GameHubSecurityUtil.validate(
                    paramsMap,
                    session,
                    this.getClass().getSimpleName(),
                    "cancel",
                    GameHubSecurityUtil.Options.forCancel(true)
            );
            if (!v.ok) {
                GameHubResponseUtil.respondByValidationError(session, v.error);
                return;
            }

            // 2) 处理撤销/退款（幂等→加回→落账→通知→回包）
            handle(v);

        } catch (Exception e) {
            LOGGER.error("[GameHub][cancel] error", e);
            GameHubResponseUtil.sendError(session, GameHubConstants.ErrorCode.ERR001);
        }
    }

    /**
     * 撤销/退款处理：
     * - 幂等：如相同 roundId 下已包含 transactionId，则直接返回当前余额
     * - 找不到回合：视为无更改，直接返回当前余额（容错/避免NPE）
     * - 正常：按 amountCents 计算展示币退款金额，更新回合状态，通知 refund，并返回新余额（分）
     */
    private void handle(final GameHubSecurityUtil.Validation v) {
        final Player player = v.player;

        // 1) 当前余额（展示币：元）
        final double realBalance = ScriptLoader.getInstance().functionScript(
                "AgentGameScript", (IAgentGameScript script) -> script.realBalance(player));
        final double balanceDisplay = ScriptLoader.getInstance().functionScript(
                "AgentGameScript", (IAgentGameScript script) -> script.amountTransformUsd(player, realBalance));
        final long currentCents = Math.round(BigDecimalUtils.round(balanceDisplay, 2) * 100);

        // 2) 幂等/存在性判断
        GameNote gameNote = AgentGameMrg.getInstance().findRoundIdGameNote(player, v.roundId);
        if (gameNote != null
                && gameNote.getTransactionIds() != null
                && gameNote.getTransactionIds().contains(v.transactionId)) {
            LOGGER.warn("[GameHub][cancel] duplicate: roundId={}, transactionId={}", v.roundId, v.transactionId);
            GameHubResponseUtil.sendBalanceOk(session, currentCents, v.currency);
            return;
        }
        if (gameNote == null) {
            LOGGER.warn("[GameHub][cancel] round not found, no-op: roundId={}, tx={}", v.roundId, v.transactionId);
            GameHubResponseUtil.sendBalanceOk(session, currentCents, v.currency);
            return;
        }

        // 3) 退款金额（展示币：元）
        final double refundDisplay = BigDecimalUtils.round((v.amountCents == null ? 0L : v.amountCents) / 100.0, 2);

        // 4) 退款后的展示币余额
        final double finalBalanceDisplay = BigDecimalUtils.add(balanceDisplay, refundDisplay, 2);

        // 5) 标记回合状态为取消（3）
        ScriptLoader.getInstance().consumerScript(
                "AgentGameScript",
                (IAgentGameScript script) -> script.updateGameNoteStatus(player, v.roundId, 3)
        );

        // 6) 货币名称（容错）
        String currencyName = "";
        try {
            final Currency c = Currency.valueOf(gameNote.getCurrencyId());
            if (c != null) currencyName = c.name();
        } catch (Exception ignore) { /* ignore */ }

        // 7) 通知（退款/撤销）
        final NotifyData notifyData = new NotifyData();
        notifyData.setType(ChangeType.refund.getType())
                .setPid(player.getPlayerId())
                .setPlayerName(player.getPlayerName())
                .setNoteId(String.valueOf(gameNote.getNoteId()))
                .setCurrency(currencyName)
                .setBetAmount(gameNote.getBetAmount())
                .setValidBets(gameNote.getValidBets())
                .setWin(gameNote.getWin())
                .setTotalWin(gameNote.getWin())
                .setUpdSessionId(MsgUtil.getSessionID(session))
                .setGameId(gameNote.getGameId())
                .setCurrencyId(gameNote.getCurrencyId());

        ScriptLoader.getInstance().consumerScript(
                "AgentGameScript",
                (IAgentGameScript script) -> script.updateCurrency(notifyData)
        );

        // 8) 返回成功余额（分）
        final long finalCents = Math.round(BigDecimalUtils.round(finalBalanceDisplay, 2) * 100);
        GameHubResponseUtil.sendBalanceOk(session, finalCents, v.currency);
    }
}
