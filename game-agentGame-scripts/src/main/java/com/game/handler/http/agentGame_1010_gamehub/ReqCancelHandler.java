package com.game.handler.http.agentGame_1010_gamehub;

import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.MsgUtil;
import com.game.entity.game.GameNote;
import com.game.entity.player.Player;
import com.game.enums.ChangeType;
import com.game.enums.Currency;
import com.game.gamesr.NotifyData;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.scripts.IAgentGameScript;
import io.netty.channel.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * GameHub GAP: 撤销/退款 - cancel（单一URI分发模式）
 * 请求：action=cancel, amount, player_id, transaction_id, currency, game_id, round_id, ext_round_id, ext_round_finished, hash
 * 响应：{ "status":200, "balance":<int cents>, "currency":"..." }
 * 金额单位：整数“分”（minor units）
 *
 * 说明：
 * - 不再继承 HttpHandler，不使用 @IHandlerEntity
 * - 由 ReqCommonHandler 根据 action=cancel 分发调用本类的 process(...)
 */
public final class ReqCancelHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqCancelHandler.class);

    private ReqCancelHandler() {}

    /** 单一URI分发调用入口 */
    public static void process(Channel session, Map<String, Object> paramsMap, String handlerClassNameForPlatform) {
        try {
            if (ConstantConfig.getInstance().isAgentDebug()) {
                LOGGER.info("[GameHub][cancel] params: {}", JsonUtils.writeAsJson(paramsMap));
            }

            // 统一校验（平台配置 / 白名单 / HMAC / 参数解析 / 玩家存在）
            GameHubSecurityUtil.Validation v = GameHubSecurityUtil.validate(
                    paramsMap,
                    session,
                    handlerClassNameForPlatform, // 建议传入 ReqCommonHandler.class.getSimpleName()
                    "cancel",
                    GameHubSecurityUtil.Options.forCancel(true) // 你现在要求 cancel 必带 amount
            );
            if (!v.ok) {
                GameHubResponseUtil.respondByValidationError(session, v.error);
                return;
            }

            // 处理逻辑
            handle(session, v);

        } catch (Exception e) {
            LOGGER.error("[GameHub][cancel.process] error", e);
            GameHubResponseUtil.sendError(session, GameHubConstants.ErrorCode.ERR001);
        }
    }

    /**
     * 撤销/退款处理：
     * - 回合不存在：记录“取消先到”标记（TTL 120s），幂等返回当前余额
     * - 幂等：同一 roundId 中已包含该 transactionId，直接返回当前余额
     * - 状态短路：已结算(2)/已取消(3) → 幂等返回
     * - 正常路径：按 amountCents 退款 → updateGameNoteStatus(..., 3) → 通知 refund → 返回新余额（分）
     */
    private static void handle(final Channel session, final GameHubSecurityUtil.Validation v) {
        final Player player = v.player;

        // 1) 当前余额（展示币：元）
        final double realBalance = ScriptLoader.getInstance().functionScript(
                "AgentGameScript", (IAgentGameScript script) -> script.realBalance(player));
        final double balanceDisplay = ScriptLoader.getInstance().functionScript(
                "AgentGameScript", (IAgentGameScript script) -> script.amountTransformUsd(player, realBalance));
        final long currentCents = Math.round(BigDecimalUtils.round(balanceDisplay, 2) * 100);

        // 2) 查找回合
        GameNote gameNote = AgentGameMrg.getInstance().findRoundIdGameNote(player, v.roundId);

        // 2.1) 回合不存在：记录“取消先到”标记并返回当前余额（不做资金变更）
        if (gameNote == null) {
            LOGGER.warn("[GameHub][cancel] round not found, record marker and no-op: roundId={}, tx={}",
                    v.roundId, v.transactionId);

            final String redisKey = "gh:cancel:transactionId:" + v.transactionId; // 与 betby 的命名空间区分
            try {
                RedisPoolManager.getInstance().function(jedis -> {
                    jedis.sync().setex(redisKey, 120, v.transactionId); // TTL 120s
                    return null;
                });
            } catch (Exception re) {
                LOGGER.error("[GameHub][cancel] redis setex failed: key={}, err={}", redisKey, re.getMessage(), re);
            }

            GameHubResponseUtil.sendBalanceOk(session, currentCents, v.currency);
            return;
        }

        // 2.2) 幂等：同一 roundId 内重复的 transactionId
        if (gameNote.getTransactionIds() != null &&
                Boolean.FALSE.equals(gameNote.getGamehubBetMap().get(v.transactionId))) {
            LOGGER.warn("[GameHub][cancel] duplicate tx: roundId={}, transactionId={}", v.roundId, v.transactionId);
            GameHubResponseUtil.sendBalanceOk(session, currentCents, v.currency);
            return;
        }

        // 2.3) 状态短路（仅示例：2=已结算，3=已取消）
        if (gameNote.getStatus() == 2) { // 已结算不能取消
            LOGGER.warn("[GameHub][cancel] round already settled: roundId={}, tx={}", v.roundId, v.transactionId);
            GameHubResponseUtil.sendBalanceOk(session, currentCents, v.currency);
            return;
        }
        if (gameNote.getStatus() == 3) { // 已取消幂等返回
            LOGGER.warn("[GameHub][cancel] round already cancelled: roundId={}, tx={}", v.roundId, v.transactionId);
            GameHubResponseUtil.sendBalanceOk(session, currentCents, v.currency);
            return;
        }

        // 3) 退款金额（展示币：元）
        final double refundAmount = BigDecimalUtils.round((v.amountCents == null ? 0L : v.amountCents) / 100.0, 2);

        final double finalRefundAmount = ScriptLoader.getInstance().functionScript("AgentGameScript",
                (IAgentGameScript script) -> script.usdTransformAmount(player, refundAmount));

        ScriptLoader.getInstance().consumerScript("AgentGameScript",
                (IAgentGameScript script) -> script.gamehubUpdateCancelGameNote(player, v.roundId, v.transactionId));

        // 6) 货币名称（容错）
        String currencyName = "";
        try {
            final Currency c = Currency.valueOf(gameNote.getCurrencyId());
            if (c != null) currencyName = c.name();
        } catch (Exception ignore) { /* ignore */ }

        // 7) 通知（退款/撤销）
        final NotifyData notifyData = new NotifyData();
        notifyData.setType(ChangeType.refund.getType())
                .setPid(player.getPlayerId())
                .setPlayerName(player.getPlayerName())
                .setNoteId(String.valueOf(gameNote.getNoteId()))
                .setCurrency(currencyName)
                .setBetAmount(gameNote.getBetAmount())
                .setWin(gameNote.getBetAmount() - finalRefundAmount)
                .setUpdSessionId(MsgUtil.getSessionID(session))
                .setGameId(gameNote.getGameId())
                .setCurrencyId(gameNote.getCurrencyId());

        ScriptLoader.getInstance().consumerScript(
                "AgentGameScript",
                (IAgentGameScript script) -> script.updateCurrency(notifyData)
        );
    }
}
