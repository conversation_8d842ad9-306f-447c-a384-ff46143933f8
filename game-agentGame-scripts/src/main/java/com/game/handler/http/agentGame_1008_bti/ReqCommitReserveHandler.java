package com.game.handler.http.agentGame_1008_bti;

import com.game.c_entity.middleplatform.C_BaseGamePlatform;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.MsgUtil;
import com.game.entity.game.GameNote;
import com.game.entity.player.Player;
import com.game.enums.AgentGame;
import com.game.enums.ChangeType;
import com.game.enums.Currency;
import com.game.gamesr.NotifyData;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.manager.DataAgentGameMrg;
import com.game.gamesr.scripts.IAgentGameScript;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.Map;

@IHandlerEntity(path = BtiConstants.Paths.COMMIT_RESERVE, desc = "投注确认")
public class ReqCommitReserveHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqCommitReserveHandler.class);

    @Override
    public void run() {
        try {
            final Map<String, Object> paramsMap = getParamsMap();
            LOGGER.info("ReqCommitReserveHandler，params：{}", JsonUtils.writeAsJson(paramsMap));

            final C_BaseGamePlatform c_baseGamePlatform = DataAgentGameMrg.getInstance()
                    .findC_BaseGamePlatformSupplierId(this.getClass().getSimpleName(), AgentGame.BTI.getType());

            // 没有设置就默认通过
            if (!c_baseGamePlatform.getIpBlacklist().isEmpty()) {
                String ip = MsgUtil.getClientIp(session);
                final boolean inWhiteList = ScriptLoader.getInstance().functionScript("AgentGameScript",
                        (IAgentGameScript script) -> script.isIpBlacklist(ip, AgentGame.BTI.getType()));
                if (!inWhiteList) {
                    LOGGER.warn("not in IP whitelist: {}", ip);
                    BtiUtils.sendErrorResponse(session, BtiConstants.ErrorCode.TOKEN_NOT_VALID, "server error");
                    return;
                }
            }

            final String custId = (String) paramsMap.get("cust_id");
            final String reserveId = (String) paramsMap.get("reserve_id");
            final String purchaseId = (String) paramsMap.get("purchase_id");

            final Player player = BtiUtils.getPlayer(custId);
            if (player == null) {
                BtiUtils.sendSuccessResponse(session, "Player not found", 0.00);
                return;
            }

            final double realBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.realBalance(player));

            double currentBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.amountTransformUsd(player, realBalance));

            GameNote gameNote = AgentGameMrg.getInstance().findRoundIdGameNote(player, reserveId);
            if (gameNote == null) {
                LOGGER.warn("bet transaction id not found: {}", reserveId);
                BtiUtils.sendSuccessResponse(session, "bet transaction id not found", currentBalance);
                return;
            }

            if (gameNote.isBtiCommit()) {
                LOGGER.warn("Duplicate bet detected: {}", reserveId);
                BtiUtils.sendSuccessResponse(session, "Duplicate bet detected", currentBalance);
                return;
            }

            final double originBetAmount = gameNote.getBetAmount();
            double reserveAmount = gameNote.getBtiReserveMap().values().stream().mapToDouble(Double::parseDouble).sum();
            final double remainAmount = BigDecimalUtils.sub(originBetAmount, reserveAmount, 2);
            LOGGER.info("originBetAmount: {}, reserveAmount: {}, remainAmount: {}", originBetAmount, reserveAmount, remainAmount);

            if (remainAmount < -0.01) {
                // 仅当DebitReserve金额和Reserve金额差异为0.01 时，才可以忽略，因为系统会计算并向上舍入DebitReserve中的金额。
                LOGGER.warn("The difference in amount is less than -0.01: {}", reserveId);
                BtiUtils.sendSuccessResponse(session, "The difference in amount is less than -0.01", currentBalance);
                return;
            }

            ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.updateGameNoteReserve(player, reserveId, purchaseId, reserveAmount, reserveAmount));

            Currency currency = Currency.valueOf(gameNote.getCurrencyId());
            String currencyName = "";
            if (currency != null) {
                currencyName = currency.name();
            }

            NotifyData notifyData = new NotifyData();
            notifyData.setType(ChangeType.refund.getType())
                    .setPid(player.getPlayerId())
                    .setPlayerName(custId)
                    .setNoteId(gameNote.getNoteId() + "")
                    .setCurrency(currencyName)
                    .setBetAmount(originBetAmount)
                    .setWin(reserveAmount)
                    .setUpdSessionId(MsgUtil.getSessionID(session))
                    .setGameId(gameNote.getGameId())
                    .setCurrencyId(gameNote.getCurrencyId());

            ScriptLoader.getInstance().consumerScript("AgentGameScript",
                    (IAgentGameScript script) -> script.updateCurrency(notifyData));
        } catch (Exception e) {
            LOGGER.error("ReqCommitReserveHandler", e);
            BtiUtils.sendSuccessResponse(session, "server error", 0.00);
        }
    }

}
