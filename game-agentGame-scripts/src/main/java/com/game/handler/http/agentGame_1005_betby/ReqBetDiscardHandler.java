package com.game.handler.http.agentGame_1005_betby;

import com.alibaba.fastjson.JSON;
import com.game.c_entity.middleplatform.C_BaseGamePlatform;
import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.MsgUtil;
import com.game.entity.game.GameNote;
import com.game.entity.player.Player;
import com.game.enums.AgentGame;
import com.game.enums.ChangeType;
import com.game.enums.Currency;
import com.game.gamesr.NotifyData;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.manager.DataAgentGameMrg;
import com.game.gamesr.scripts.IAgentGameScript;
import com.game.handler.http.agentGame_1005_betby.BetbyConstants.ValidationResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;


@IHandlerEntity(path = "/betby/bet/discard", desc = "投注丢弃")
public class ReqBetDiscardHandler extends HttpHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(ReqBetDiscardHandler.class);

    // 请求参数类
    private static class BetDiscardRequest {
        private String fullPlayerId;
        // 由运营商端分配给玩家的唯一编码
        private String playerId;
        // 当 bet_make 请求被处理时由 Betby 分配的交易 ID
        private String transactionId;
        // 调用此方法的原因描述
        private String reason;

        // Getter and Setter
    }

    @Override
    public void run() {
        final Map<String, Object> responseMap = new LinkedHashMap<>();
        try {
            if (ConstantConfig.getInstance().isAgentDebug()) {
                LOGGER.info("betby BET_DISCARD request: {}", JsonUtils.writeAsJson(paramsMap));
            }

            final C_BaseGamePlatform c_baseGamePlatform = DataAgentGameMrg.getInstance()
                    .findC_BaseGamePlatformSupplierId(this.getClass().getSimpleName(), AgentGame.BETBY.getType());

            // 没有设置就默认通过
            if (!c_baseGamePlatform.getIpBlacklist().isEmpty()) {
                String ip = MsgUtil.getClientIp(session);
                final boolean inWhiteList = ScriptLoader.getInstance().functionScript("AgentGameScript",
                        (IAgentGameScript script) -> script.isIpBlacklist(ip, AgentGame.BETBY.getType()));
                if (!inWhiteList) {
                    LOGGER.warn("not in IP whitelist: {}", ip);
                    responseMap.put("code", 3000);
                    MsgUtil.responseHttp(responseMap, session);
                    return;
                }
            }

            // 1. 解析请求参数
            final BetDiscardRequest request = parseRequest((String) paramsMap.get("payload"));

            // 2. 验证请求
            ValidationResult validationResult = validateRequest(request);
            if (!validationResult.isSuccess()) {
                MsgUtil.responseHttp(responseMap, session);
                return;
            }

            // 执行业务逻辑（例如：记录丢弃原因、退还金额等）
            processBetDiscard(request, validationResult.getPlayer());
        } catch (Exception e) {
            LOGGER.error("ReqBetDiscardHandle error", e);
            MsgUtil.responseHttp(responseMap, session);
        }
    }

    // 解析请求
    private BetDiscardRequest parseRequest(String payLoad) {
        try {
            Map<String, Object> requestMap = JwtVerifier.verifyAndDecodeJWT(payLoad);
            if (ConstantConfig.getInstance().isAgentDebug()) {
                LOGGER.info("betby BET_DISCARD payload: {}", requestMap);
            }

            @SuppressWarnings("unchecked")
            Map<String, Object> paramsMap = (Map<String, Object>) requestMap.get("payload");

            BetDiscardRequest request = new BetDiscardRequest();
            String fullPlayerId = (String) paramsMap.get("ext_player_id");
            request.fullPlayerId = fullPlayerId;
            request.playerId = BetbyConstants.getPlayerId(fullPlayerId);
            request.transactionId = (String) paramsMap.get("transaction_id");
            request.reason = (String) paramsMap.get("reason");

            return request;
        } catch (Exception e) {
            LOGGER.error("Error parsing request: {}", e.getMessage());
            throw e;
        }
    }

    // 验证请求
    private ValidationResult validateRequest(BetDiscardRequest request) {
        try {
            // 验证bet_transaction_id和其他参数
            if (request.transactionId == null) {
                return ValidationResult.failure(2004, "Invalid request parameters");
            }

            // 验证玩家是否存在
            Player player = AgentGameMrg.getInstance().findDbPlayer(Long.parseLong(request.playerId));
            if (player == null) {
                return ValidationResult.failure(1006, "Player not found");
            }

            // 返回验证结果
            return ValidationResult.success(player);
        } catch (Exception e) {
            LOGGER.error("Validation error", e);
            return ValidationResult.failure(2004, "Validation error");
        }
    }

    // 处理投注丢弃的业务逻辑
    private void processBetDiscard(BetDiscardRequest request, Player player) {
        // 查找相应的游戏记录
        GameNote gameNote = AgentGameMrg.getInstance().findRoundIdGameNote(player, request.transactionId);
        if (gameNote == null) {
            LOGGER.warn("Transaction not found: {}", request.transactionId);

            // 将交易ID写入Redis并设置60秒超时时间
            try {
                // 构建Redis键
                String redisKey = "betby:discard:" + request.transactionId;

                // 使用函数式接口方式写入Redis
                RedisPoolManager.getInstance().function(jedis ->
                        jedis.sync().setex(redisKey, 60, request.transactionId)
                );
            } catch (Exception e) {
                LOGGER.error("Failed to store transaction in Redis: {}", e.getMessage(), e);
                // 即使Redis操作失败，我们也允许流程继续，以避免阻塞核心业务
            }

            final Map<String, Object> responseMap = new LinkedHashMap<>();
            MsgUtil.responseHttp(responseMap, session);
            return;
        }

        ScriptLoader.getInstance().consumerScript("AgentGameScript",
                (IAgentGameScript script) -> script.updateGameNoteStatus(player, request.transactionId, 3));

        Currency currency = Currency.valueOf(gameNote.getCurrencyId());
        String currencyName = "";
        if (currency != null) {
            currencyName = currency.name();
        }

        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("transactionId", request.transactionId);

        String dataJson = JSON.toJSONString(dataMap);

        final NotifyData notifyData = new NotifyData();
        notifyData.setType(ChangeType.refund.getType())
                .setPid(player.getPlayerId())
                .setPlayerName("")  // 复用refund接口，用于refund判断类型 TODO 也移到data中
                .setNoteId(gameNote.getNoteId() + "")
                .setCurrency(currencyName)
                .setBetAmount(gameNote.getBetAmount())
                .setWin(gameNote.getWin())
                .setData(dataJson)
                .setUpdSessionId(MsgUtil.getSessionID(session))
                .setGameId(gameNote.getGameId())
                .setCurrencyId(gameNote.getCurrencyId());

        ScriptLoader.getInstance().consumerScript("AgentGameScript",
                (IAgentGameScript script) -> script.updateCurrency(notifyData));
    }
}
