package com.game.handler.http.agentGame_1010_gamehub;

import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.JsonUtils;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

@IHandlerEntity(path = GameHubConstants.Paths.GAMEHUB, desc = "GameHub-单一URI分发")
public class ReqCommonHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqCommonHandler.class);

    @Override
    public void run() {
        try {
            if (ConstantConfig.getInstance().isAgentDebug()) {
                LOGGER.info("[GameHub][common] params: {}", JsonUtils.writeAsJson(paramsMap));
            }

            final String action = getAction(paramsMap);
            if (StringUtil.isNullOrEmpty(action)) {
                GameHubResponseUtil.sendError(session, GameHubConstants.ErrorCode.ERR001); // Unknown / missing action
                return;
            }

            switch (action.toLowerCase()) {
                case "balance" -> ReqBalanceHandler.process(session, paramsMap, /*handlerForPlatform*/ this.getClass().getSimpleName());
                case "bet"     -> ReqBetHandler.process(session, paramsMap, this.getClass().getSimpleName());
                case "win"     -> ReqWinHandler.process(session, paramsMap, this.getClass().getSimpleName());
                case "cancel"  -> ReqCancelHandler.process(session, paramsMap, this.getClass().getSimpleName());
                default -> {
                    LOGGER.warn("[GameHub][common] unsupported action: {}", action);
                    GameHubResponseUtil.sendError(session, GameHubConstants.ErrorCode.ERR001);
                }
            }

        } catch (Exception e) {
            LOGGER.error("[GameHub][common] error", e);
            GameHubResponseUtil.sendError(session, GameHubConstants.ErrorCode.ERR001);
        }
    }

    private static String getAction(Map<String, Object> params) {
        Object v = params.get("action");
        return v == null ? null : String.valueOf(v).trim();
    }
}
