package com.game.handler.http.agentGame_1008_bti;

import com.game.c_entity.middleplatform.C_BaseGamePlatform;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.*;
import com.game.entity.game.GameNote;
import com.game.entity.player.Player;
import com.game.enums.AgentGame;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.manager.DataAgentGameMrg;
import com.game.gamesr.scripts.IAgentGameScript;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.Map;

@IHandlerEntity(path = BtiConstants.Paths.DEBIT_RESERVE, desc = "注单咨询")
public class ReqDebitReserveHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqDebitReserveHandler.class);

    @Override
    public void run() {
        try {
            final Map<String, Object> paramsMap = getParamsMap();
            LOGGER.info("ReqDebitReserveHandler，params：{}", JsonUtils.writeAsJson(paramsMap));

            final C_BaseGamePlatform c_baseGamePlatform = DataAgentGameMrg.getInstance()
                    .findC_BaseGamePlatformSupplierId(this.getClass().getSimpleName(), AgentGame.BTI.getType());

            // 没有设置就默认通过
            if (!c_baseGamePlatform.getIpBlacklist().isEmpty()) {
                String ip = MsgUtil.getClientIp(session);
                final boolean inWhiteList = ScriptLoader.getInstance().functionScript("AgentGameScript",
                        (IAgentGameScript script) -> script.isIpBlacklist(ip, AgentGame.BTI.getType()));
                if (!inWhiteList) {
                    LOGGER.warn("not in IP whitelist: {}", ip);
                    BtiUtils.sendErrorResponse(session, BtiConstants.ErrorCode.TOKEN_NOT_VALID, "server error");
                    return;
                }
            }

            final String custId = (String) paramsMap.get("cust_id");
            final String reserveId = (String) paramsMap.get("reserve_id");
            // todo 不使用这个
            final String purchaseId = (String) paramsMap.get("purchase_id");
            final String reqId = (String) paramsMap.get("req_id");
            final String amount = (String) paramsMap.get("amount");

            final Player player = BtiUtils.getPlayer(custId);
            if (player == null) {
                BtiUtils.sendSuccessResponse(session, "Player not found", 0.00);
                return;
            }

            final double realBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.realBalance(player));

            final double currentBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.amountTransformUsd(player, realBalance));

            GameNote gameNote = AgentGameMrg.getInstance().findRoundIdGameNote(player, reserveId);
            if (gameNote == null) {
                LOGGER.warn("bet transaction id not found: {}", reserveId);
                BtiUtils.sendSuccessResponse(session, "bet transaction id not found", currentBalance);
                return;
            }

            // 检查是否是重复请求
            if (gameNote.getTransactionIds().contains(reqId)) {
                LOGGER.info("Duplicate transaction detected: {}. Returning idempotent response.", reqId);
                BtiUtils.sendSuccessResponse(session, "Duplicate transaction detected", gameNote.getBalance());
                return;
            }

            LOGGER.info("BTI Reserve Map: {}", gameNote.getBtiReserveMap());

            final double reserveAmount = gameNote.getBtiReserveMap().values().stream()
                    .mapToDouble(Double::parseDouble).sum()
                    + Double.parseDouble(amount);

            LOGGER.info("reserveAmount: {}", reserveAmount);
            double betAmount = gameNote.getBetAmount();
            LOGGER.info("betAmount: {}", betAmount);

            if (reserveAmount > betAmount && BigDecimalUtils.sub(reserveAmount, betAmount, 2) > 0.01) {
                LOGGER.info("Total DebitReserve amount larger than Reserve amount: {} > {}.", reserveAmount, gameNote.getBetAmount());
                BtiUtils.sendSuccessResponse(session, "Total DebitReserve amount larger than Reserve amount", gameNote.getBalance());
                return;
            }

            ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.updateGameNoteReserveMap(player, reserveId, reqId, amount));

            BtiUtils.sendSuccessResponse(session, gameNote.getBalance());
        } catch (Exception e) {
            LOGGER.error("ReqDebitReserveHandler", e);
            BtiUtils.sendSuccessResponse(session, "server error", 0.00);
        }
    }

}
