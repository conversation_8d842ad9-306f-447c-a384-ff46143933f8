package com.game.handler.http.agentGame_1008_bti;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTCreator;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.TokenExpiredException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.game.engine.utils.TimeUtil;
import com.game.gamesr.utils.JWTUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

public class BtiJWTUtils {
    private static final Logger LOGGER = LoggerFactory.getLogger(JWTUtils.class);

    private static final String key = "38f6fe699382f0b2410189b44548e2e6";

    public static String jwtToken(JWData jwData) {
        //获取jwt生成器
        final JWTCreator.Builder jwtBuilder = JWT.create();

        //由于该生成器设置Header的参数为一个<String, Object>的Map,
        //所以我们提前准备好
        final Map<String, Object> headers = new HashMap<>();

        headers.put("typ", "jwt");   //设置token的type为jwt
        headers.put("alg", "hs256");  //表明加密的算法为HS256

        //开始生成token
        //我们将之前准备好的header设置进去

        //token生成完毕，可以发送给客户端了，前端可以使用
        //localStorage.setItem("your_token", token)进行存储，在
        final JWTCreator.Builder builder = jwtBuilder.withHeader(headers);
        //接下来为设置PayLoad,Claim中的键值对可自定义
        //设置用户id
        builder.withClaim("userId", jwData.userId);
        builder.withClaim("currency", jwData.currency);
        builder.withClaim("playerName", jwData.playerName);
        builder.withClaim("bonus", jwData.bonus);

//        builder.withExpiresAt(new Date(4102415999000L)); // 2099-12-31 23:59:59 GMT
        //token失效时间，这里为一天后失效
        builder.withExpiresAt(new Date(jwData.expires));
        //设置该jwt的发行时间，一般为当前系统时间
        builder.withIssuedAt(new Date(TimeUtil.currentTimeMillis()));

        //进行签名，选择加密算法，以一个字符串密钥为参数
        return builder.sign(Algorithm.HMAC256(key));
    }

    public static Map<String, Object> verify(String token) {
        final Map<String, Object> params = new LinkedHashMap<>();
        if (token == null) return params;

        final var require = JWT.require(Algorithm.HMAC256(key)).build();
        DecodedJWT decode;
        try {
            decode = require.verify(token);
        } catch (Exception e) {
            if (e instanceof TokenExpiredException) {
                params.put("error", 11);
                return params;
            } else {
                LOGGER.error("", e);
            }
            return params;
        }

        params.put("userId", decode.getClaim("userId").asLong());
        params.put("currency", decode.getClaim("currency").asString());
        params.put("playerName", decode.getClaim("playerName").asString());
        params.put("bonus", Boolean.TRUE.equals(decode.getClaim("bonus").asBoolean()));
        params.put("expires", decode.getExpiresAt().getTime());

        return params;
    }

    public static class JWData {
        public long userId;
        public String currency;
        public String playerName;
        public long expires;
        public boolean bonus;
    }
}
