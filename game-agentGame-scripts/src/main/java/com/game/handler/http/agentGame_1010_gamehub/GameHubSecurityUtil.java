package com.game.handler.http.agentGame_1010_gamehub;

import io.netty.channel.Channel;
import io.netty.util.internal.StringUtil;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.InetSocketAddress;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * GameHub/GAP 安全校验工具
 * - IP 白名单校验
 * - HMAC-SHA256 签名校验（去掉 hash，按参数名升序拼接 key=value&...）
 *
 * 文档依据：Security（IP 白名单 + HMAC-SHA256）
 * - 去掉 hash、按参数名排序后参与签名
 */
public final class GameHubSecurityUtil {

    private GameHubSecurityUtil() {}

    /** 从 Channel 获取对端 IP（支持直接连接兜底；如经网关，建议上游提前写入真实IP） */
    public static String extractRemoteIp(Channel session) {
        try {
            Object addr = session.remoteAddress();
            if (addr instanceof InetSocketAddress isa) {
                return isa.getAddress().getHostAddress();
            }
        } catch (Exception ignored) {}
        return "0.0.0.0";
    }

    /** IP 白名单校验：whitelist 可为精确 IP；如需 CIDR 请自行扩展 */
    public static boolean checkIpWhitelist(String ip, Collection<String> whitelist) {
        if (whitelist == null || whitelist.isEmpty()) return true; // 若未配置白名单，则放行（按你们策略调整）
        if (StringUtil.isNullOrEmpty(ip)) return false;
        for (String allow : whitelist) {
            if (ip.equals(allow)) return true;
        }
        return false;
    }

    /** 计算 HMAC-SHA256（hex 小写） */
    public static String hmacSha256Hex(String payload, String secret) throws Exception {
        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacSHA256"));
        byte[] raw = mac.doFinal(payload.getBytes(StandardCharsets.UTF_8));
        return toHex(raw);
    }

    /** 去掉 hash 后，按参数名升序拼接成 payload：key=value&key2=value2...（value 直接用原文，不再 URL 编码） */
    public static String buildSortedPayload(Map<String, ?> params) {
        List<String> keys = new ArrayList<>();
        for (String k : params.keySet()) {
            if ("hash".equalsIgnoreCase(k)) continue;
            keys.add(k);
        }
        Collections.sort(keys, String.CASE_INSENSITIVE_ORDER);
        StringBuilder sb = new StringBuilder();
        boolean first = true;
        for (String k : keys) {
            if (!first) sb.append("&");
            first = false;
            Object v = params.get(k);
            sb.append(k).append("=").append(v == null ? "" : String.valueOf(v));
        }
        return sb.toString();
    }

    /** 一次性完成签名校验：返回 true=通过 */
    public static boolean verifySignature(Map<String, ?> params, String providedHash, String secret) {
        if (StringUtil.isNullOrEmpty(providedHash) || StringUtil.isNullOrEmpty(secret)) return false;
        try {
            String payload = buildSortedPayload(params);
            String calc = hmacSha256Hex(payload, secret);
            return calc.equalsIgnoreCase(providedHash);
        } catch (Exception e) {
            return false;
        }
    }

    private static String toHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder(bytes.length * 2);
        for (byte b : bytes) sb.append(String.format("%02x", b));
        return sb.toString();
    }
}
