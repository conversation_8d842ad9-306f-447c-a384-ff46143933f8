package com.game.handler.http.agentGame_1010_gamehub;

import com.game.c_entity.middleplatform.C_BaseGamePlatform;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.enums.AgentGame;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.manager.DataAgentGameMrg;
import com.game.gamesr.scripts.IAgentGameScript;
import io.netty.channel.Channel;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.InetSocketAddress;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * GameHub/GAP 安全与通用校验工具（单一入口）
 *
 * 功能：
 * 1) HMAC-SHA256 签名计算/校验（去掉 hash，按参数名升序拼接 key=value&...）
 * 2) IP 白名单校验（注意：平台字段 getIpBlacklist 实际为白名单，历史命名遗留）
 * 3) 平台配置加载（含 secretKey）
 * 4) 通用参数/交易字段/金额字段 解析与校验（可配置）
 * 5) 玩家存在性校验
 *
 * 使用：
 *   GameHubSecurityUtil.Validation v = GameHubSecurityUtil.validate(
 *       paramsMap, session, this.getClass().getSimpleName(), "bet", GameHubSecurityUtil.Options.forBet());
 *   if (!v.ok) { // 映射 v.error → 错误码后回包
 *       ...
 *       return;
 *   }
 *   // v.player, v.cfg, v.transactionId, v.roundId, v.amountCents ...
 */
public final class GameHubSecurityUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(GameHubSecurityUtil.class);

    private GameHubSecurityUtil() {}

    /* ======================== 低层工具：IP / HMAC / payload ======================== */

    /** 从 Channel 获取对端 IP（经网关时建议上游已写入真实 IP 到 Header，再由业务侧读取并覆盖） */
    public static String extractRemoteIp(Channel session) {
        try {
            Object addr = session.remoteAddress();
            if (addr instanceof InetSocketAddress isa) {
                return isa.getAddress().getHostAddress();
            }
        } catch (Exception ignored) {}
        return "0.0.0.0";
    }

    /** IP 白名单匹配：仅支持精确匹配；如需 CIDR/网段可自行扩展 */
    public static boolean checkIpWhitelist(String ip, Collection<String> whitelist) {
        if (whitelist == null || whitelist.isEmpty()) return true;  // 未配置时放行
        if (StringUtil.isNullOrEmpty(ip)) return false;
        for (String allow : whitelist) {
            if (ip.equals(allow)) return true;
        }
        return false;
    }

    /** HMAC-SHA256（hex 小写） */
    public static String hmacSha256Hex(String payload, String secret) throws Exception {
        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacSHA256"));
        byte[] raw = mac.doFinal(payload.getBytes(StandardCharsets.UTF_8));
        return toHex(raw);
    }

    /** 去掉 hash，按参数名升序拼接：key=value&key2=value2...（value 原文，不做 URL 编码） */
    public static String buildSortedPayload(Map<String, ?> params) {
        List<String> keys = new ArrayList<>();
        for (String k : params.keySet()) {
            if ("hash".equalsIgnoreCase(k)) continue;
            keys.add(k);
        }
        keys.sort(String.CASE_INSENSITIVE_ORDER);
        StringBuilder sb = new StringBuilder();
        boolean first = true;
        for (String k : keys) {
            if (!first) sb.append("&");
            first = false;
            Object v = params.get(k);
            sb.append(k).append("=").append(v == null ? "" : String.valueOf(v));
        }
        return sb.toString();
    }

    /** 一次性签名校验：true=通过 */
    public static boolean verifySignature(Map<String, ?> params, String providedHash, String secret) {
        if (StringUtil.isNullOrEmpty(providedHash) || StringUtil.isNullOrEmpty(secret)) return false;
        try {
            String payload = buildSortedPayload(params);
            String calc = hmacSha256Hex(payload, secret);
            return calc.equalsIgnoreCase(providedHash);
        } catch (Exception e) {
            return false;
        }
    }

    private static String toHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder(bytes.length * 2);
        for (byte b : bytes) sb.append(String.format("%02x", b));
        return sb.toString();
    }

    /* ======================== 通用校验：Options / Validation / validate(...) ======================== */

    /**
     * 协议差异选项
     */
    public static final class Options {
        /** 是否需要交易字段（transaction_id / game_id / round_id / ext_round_finished / ext_round_id） */
        public boolean requireTransaction = false;
        /** 是否需要金额（amount，单位分） */
        public boolean requireAmount = false;
        /** ext_round_id 是否必填（部分实现允许可选） */
        public boolean extRoundIdRequired = true;

        public static Options forBalance() {
            Options o = new Options();
            o.requireTransaction = false;
            o.requireAmount = false;
            o.extRoundIdRequired = false;
            return o;
        }
        public static Options forBet() {
            Options o = new Options();
            o.requireTransaction = true;
            o.requireAmount = true;
            o.extRoundIdRequired = true;
            return o;
        }
        public static Options forWin() {
            Options o = new Options();
            o.requireTransaction = true;
            o.requireAmount = true;
            o.extRoundIdRequired = true;
            return o;
        }
        public static Options forCancel(boolean amountRequired) { // 某些实现 cancel 允许不传金额
            Options o = new Options();
            o.requireTransaction = true;
            o.requireAmount = amountRequired;
            o.extRoundIdRequired = true;
            return o;
        }
    }

    /**
     * 校验结果（包含解析后的字段，业务可直接使用）
     */
    public static final class Validation {
        public final boolean ok;
        public final String error; // ok=false 时的原因（用于上层映射文档错误码）

        public final Player player;
        public final C_BaseGamePlatform cfg;

        // 基础字段
        public final String action;
        public final long playerId;
        public final String currency;
        public final String hash;

        // 交易字段（按需）
        public final String transactionId;
        public final String gameId;
        public final String roundId;
        public final String extRoundId;
        public final Integer extRoundFinished; // 0/1
        public final Long amountCents;         // 分

        private Validation(boolean ok, String error,
                           Player player, C_BaseGamePlatform cfg,
                           String action, long playerId, String currency, String hash,
                           String transactionId, String gameId, String roundId, String extRoundId,
                           Integer extRoundFinished, Long amountCents) {
            this.ok = ok;
            this.error = error;
            this.player = player;
            this.cfg = cfg;
            this.action = action;
            this.playerId = playerId;
            this.currency = currency;
            this.hash = hash;
            this.transactionId = transactionId;
            this.gameId = gameId;
            this.roundId = roundId;
            this.extRoundId = extRoundId;
            this.extRoundFinished = extRoundFinished;
            this.amountCents = amountCents;
        }

        public static Validation fail(String err) {
            return new Validation(false, err, null, null,
                    null, -1, null, null,
                    null, null, null, null, null, null);
        }

        public static Validation ok(Player p, C_BaseGamePlatform cfg,
                                    String action, long playerId, String currency, String hash,
                                    String transactionId, String gameId, String roundId, String extRoundId,
                                    Integer extRoundFinished, Long amountCents) {
            return new Validation(true, null, p, cfg,
                    action, playerId, currency, hash,
                    transactionId, gameId, roundId, extRoundId, extRoundFinished, AmountCentsSafe(AmountCentsSafe(amountCents)));
        }

        private static Long AmountCentsSafe(Long v) { return v; }
    }

    /**
     * 通用校验入口
     *
     * @param paramsMap        请求参数
     * @param session          Channel（用于取真实 IP 做白名单判断）
     * @param handlerClassName Handler 类名（用于查平台配置）
     * @param expectedAction   期望的 action（balance / bet / win / cancel）
     * @param opts             选项（是否需要交易字段/金额，ext_round_id 是否必填等）
     * @return Validation（ok=false 时，error 为原因字符串）
     */
    public static Validation validate(Map<String, Object> paramsMap,
                                      Channel session,
                                      String handlerClassName,
                                      String expectedAction,
                                      Options opts) {

        // 1) 基础字段
        final String action   = (String) paramsMap.get("action");
        final String pidStr   = (String) paramsMap.get("player_id");
        final String currency = (String) paramsMap.get("currency");
        final String hash     = (String) paramsMap.get("hash");

        if (!expectedAction.equalsIgnoreCase(action)
                || StringUtil.isNullOrEmpty(pidStr)
                || StringUtil.isNullOrEmpty(currency)
                || StringUtil.isNullOrEmpty(hash)) {
            return Validation.fail("missing_or_invalid_basic_params");
        }

        final long playerId;
        try {
            playerId = Long.parseLong(pidStr);
        } catch (Exception e) {
            return Validation.fail("invalid_player_id");
        }

        // 2) 交易字段（可选）
        String transactionId = null, gameId = null, roundId = null, extRoundId = null;
        Integer extFinished = null;
        Long amountCents = null;

        if (opts.requireTransaction) {
            transactionId = (String) paramsMap.get("transaction_id");
            gameId        = (String) paramsMap.get("game_id");
            roundId       = (String) paramsMap.get("round_id");
            extRoundId    = (String) paramsMap.get("ext_round_id");
            String extFin = (String) paramsMap.get("ext_round_finished");

            boolean extOk = opts.extRoundIdRequired ? !StringUtil.isNullOrEmpty(extRoundId) : true;
            if (StringUtil.isNullOrEmpty(transactionId)
                    || StringUtil.isNullOrEmpty(gameId)
                    || StringUtil.isNullOrEmpty(roundId)
                    || !extOk
                    || StringUtil.isNullOrEmpty(extFin)) {
                return Validation.fail("missing_transaction_params");
            }
            try {
                extFinished = Integer.parseInt(extFin);
                if (extFinished != 0 && extFinished != 1) return Validation.fail("invalid_ext_round_finished");
            } catch (Exception e) {
                return Validation.fail("invalid_ext_round_finished");
            }
        }

        if (opts.requireAmount) {
            final String amountStr = (String) paramsMap.get("amount");
            if (StringUtil.isNullOrEmpty(amountStr)) return Validation.fail("missing_amount");
            try {
                amountCents = Long.parseLong(amountStr);
                if (amountCents < 0) return Validation.fail("invalid_amount");
            } catch (Exception e) {
                return Validation.fail("invalid_amount");
            }
        }

        // 3) 平台配置
        final C_BaseGamePlatform cfg = DataAgentGameMrg.getInstance()
                .findC_BaseGamePlatformSupplierId(handlerClassName, AgentGame.GAMEHUB.getType());
        if (cfg == null || StringUtil.isNullOrEmpty(cfg.getSecretKey())) {
            return Validation.fail("platform_config_not_found");
        }

        // 4) IP 白名单（注意：getIpBlacklist 实为白名单）
        if (!cfg.getIpBlacklist().isEmpty()) {
            final String ip = MsgUtil.getClientIp(session);
            final boolean ok = ScriptLoader.getInstance().functionScript(
                    "AgentGameScript",
                    (IAgentGameScript script) -> script.isIpBlacklist(ip, AgentGame.GAMEHUB.getType()));
            if (!ok) return Validation.fail("ip_not_allowed:" + ip);
        }

        // 5) HMAC
        if (!verifySignature(paramsMap, hash, cfg.getSecretKey())) {
            return Validation.fail("hmac_mismatch");
        }

        // 6) 玩家
        final Player player = AgentGameMrg.getInstance().findDbPlayer(playerId);
        if (player == null) {
            return Validation.fail("player_not_found");
        }

        return new Validation(true, null, player, cfg,
                action, playerId, currency, hash,
                transactionId, gameId, roundId, extRoundId, extFinished, amountCents);
    }
}
