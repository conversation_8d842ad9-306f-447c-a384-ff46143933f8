package com.game.handler.http.agentGame_1005_betby;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.auth0.jwt.interfaces.JWTVerifier;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.game.c_entity.middleplatform.C_BaseGamePlatform;
import com.game.enums.AgentGame;
import com.game.gamesr.manager.DataAgentGameMrg;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

public class JwtVerifier {
    private static final Logger log = LoggerFactory.getLogger(JwtVerifier.class);

    /**
     * 验证并解析JWT令牌，返回payload部分的内容
     *
     * @param jwt JWT令牌字符串
     * @return 包含载荷数据的Map
     */
    public static Map<String, Object> verifyAndDecodeJWT(String jwt) {
        try {
            C_BaseGamePlatform c_baseGamePlatform = DataAgentGameMrg.getInstance()
                    .findC_BaseGamePlatformSupplierId(BetbyJWTUtils.class.getSimpleName(), AgentGame.BETBY.getType());

            String publicKeyBase64 = c_baseGamePlatform.getToken();

            // 从Base64字符串加载RSA公钥
            RSAPublicKey rsaPublicKey = loadPublicKeyFromRawBase64(publicKeyBase64);

            // 创建JWT验证器
            Algorithm algorithm = Algorithm.RSA256(rsaPublicKey, null);
            JWTVerifier verifier = JWT.require(algorithm).build();

            // 验证JWT并获取解码后的内容
            DecodedJWT decodedJWT = verifier.verify(jwt);

            // 获取载荷部分的JSON字符串
            String payloadJson = new String(Base64.getUrlDecoder().decode(decodedJWT.getPayload()), StandardCharsets.UTF_8);

            // 解析JSON为Map
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.readValue(payloadJson, new TypeReference<Map<String, Object>>() {});

        } catch (Exception e) {
            log.error("JWT验证失败，尝试不验证签名直接解析: {}", e.getMessage());

            try {
                return decodeJWTWithoutVerification(jwt);
            } catch (Exception ex) {
                log.error("无签名验证的JWT解析也失败: {}", ex.getMessage());
                return new HashMap<>();
            }
        }
    }

    private static RSAPublicKey loadPublicKeyFromRawBase64(String base64PublicKey) throws Exception {
        // 直接解码Base64编码的公钥（无BEGIN/END标签，也无换行）
        byte[] encoded = Base64.getDecoder().decode(base64PublicKey);

        // 创建公钥规范
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(encoded);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        return (RSAPublicKey) keyFactory.generatePublic(keySpec);
    }

    /**
     * 不验证签名，只解析JWT的payload部分
     */
    public static Map<String, Object> decodeJWTWithoutVerification(String jwt) {
        try {
            // 分割JWT字符串
            String[] parts = jwt.split("\\.");
            if (parts.length != 3) {
                throw new IllegalArgumentException("Invalid JWT format");
            }

            // 解码payload部分
            String payload = new String(Base64.getUrlDecoder().decode(parts[1]), StandardCharsets.UTF_8);

            // 解析JSON到Map
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.readValue(payload, new TypeReference<Map<String, Object>>() {});

        } catch (Exception e) {
            log.error("无签名验证的JWT解析失败: {}", e.getMessage());
            return new HashMap<>();
        }
    }
}