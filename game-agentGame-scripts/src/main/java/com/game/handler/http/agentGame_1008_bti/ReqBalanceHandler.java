package com.game.handler.http.agentGame_1008_bti;

import com.game.c_entity.middleplatform.C_BaseGamePlatform;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.HttpUtils11;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.enums.AgentGame;
import com.game.enums.Currency;
import com.game.gamesr.manager.AgentGameMrg;
import com.game.gamesr.manager.DataAgentGameMrg;
import com.game.gamesr.scripts.IAgentGameScript;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;
import java.util.Map;

@IHandlerEntity(path = BtiConstants.Paths.BALANCE, desc = "查询余额")
public class ReqBalanceHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqBalanceHandler.class);

    @Override
    public void run() {
        try {
            final Map<String, Object> paramsMap = getParamsMap();
            LOGGER.info("ReqValidateTokenHandler，params：{}", JsonUtils.writeAsJson(paramsMap));

            final C_BaseGamePlatform c_baseGamePlatform = DataAgentGameMrg.getInstance()
                    .findC_BaseGamePlatformSupplierId(this.getClass().getSimpleName(), AgentGame.BTI.getType());

            // 没有设置就默认通过
            if (!c_baseGamePlatform.getIpBlacklist().isEmpty()) {
                String ip = MsgUtil.getClientIp(session);
                final boolean inWhiteList = ScriptLoader.getInstance().functionScript("AgentGameScript",
                        (IAgentGameScript script) -> script.isIpBlacklist(ip, AgentGame.BTI.getType()));
                if (!inWhiteList) {
                    LOGGER.warn("not in IP whitelist: {}", ip);
                    BtiUtils.sendErrorResponse(session, BtiConstants.ErrorCode.TOKEN_NOT_VALID, "server error");
                    return;
                }
            }

            final String authToken = (String) paramsMap.get("token");
            if (StringUtil.isNullOrEmpty(authToken)) {
                LOGGER.warn("token not valid: {}", authToken);
                respond(false, "0");
                return;
            }

            final Map<String, Object> verify = BtiJWTUtils.verify(authToken);
            if (verify.isEmpty()) {
                LOGGER.warn("token not valid: {}", authToken);
                respond(false, "0");
                return;
            }

            LOGGER.warn("token: {}", authToken);

            final Player player;
            try {
                long playerIdLong = (long) verify.get("userId");
                player = AgentGameMrg.getInstance().findDbPlayer(playerIdLong);
                if (player == null) {
                    throw new NumberFormatException("Player not found");
                }
            } catch (NumberFormatException e) {
                LOGGER.warn("Invalid playerId or not found");
                respond(false, "0");
                return;
            }

            boolean bonus = (boolean) verify.get("bonus");

            // TODO 为了测试 正常流程不应该有
            String currencyName = (String) verify.get("currency");
            LOGGER.info("verify currency: {}", currencyName);
            LOGGER.info("playerid: {}, currencyid: {}", player.getPlayerId(), player.getCurrencyId());

            Currency currency = Currency.valueOf(currencyName);

            // 单独查询余额带bonus
            player.setBonus(bonus);
            player.setPlayerCurrencyId(currency.getCurrencyId());
            player.setGameCurrencyId(currency.getCurrencyId());

            final double realBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.realBalance(player));

            final double currentBalance = ScriptLoader.getInstance().functionScript("AgentGameScript",
                    (IAgentGameScript script) -> script.amountTransformUsd(player, realBalance));

            LOGGER.info("realBalance: {}, currentBalance: {}", realBalance, currentBalance);

            respond(true, String.format("%.2f", currentBalance));
        } catch (Exception e) {
            LOGGER.error("RefreshBalanceHandler error", e);
            respond(false, "0");
        }
    }

    private void respond(Boolean success, String balance) {
        try {
            final Map<String, Object> responseMap = new LinkedHashMap<>();
            String status = success ? "success" : "failure";
            responseMap.put("status", status);
            responseMap.put("balance", balance);
            MsgUtil.responseHttp(responseMap, session);
        } catch (Exception e) {
            LOGGER.error("Respond JSON Error", e);
        }
    }

}
