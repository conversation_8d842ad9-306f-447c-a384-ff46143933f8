package com.game.hall.mrg;

import com.game.c_entity.merchant.C_GameApi;
import com.game.entity.AgentGameInfo;
import com.game.entity.player.Player;
import com.game.hall.main.HallServer;
import com.proto.CommonMessage;
import com.proto.InnerMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class CommonMrg {

    private static final Logger LOGGER = LoggerFactory.getLogger(CommonMrg.class);

    private static final CommonMrg INSTANCE = new CommonMrg();

    public static CommonMrg getInstance() {
        return INSTANCE;
    }

    public static int totalPage(int total, int pageSize) {
        int totalPage = total / pageSize;
        if (total % pageSize > 0) {
            totalPage += 1;
        }
        return totalPage;
    }

    public static CommonMessage.DItemShow buildDItemShow(int currencyId, double amount) {
        return buildDItemShow(currencyId, amount, 2);
    }

    public static CommonMessage.DItemShow buildDItemShow(int currencyId, double amount, int rewardType) {
        return CommonMessage.DItemShow.newBuilder()
                .setItemId(currencyId)
                .setNum(amount)
                .setRewardType(rewardType)
                .build();
    }

    /**
     * int32 gameId                   = 1; //游戏id
     * int32 platformId               = 2; //平台id
     * string platformGameId          = 3; //平台游戏id
     * string gameProvider            = 4; //游戏品牌
     * string name                    = 5; //游戏名字
     * int32 type                     = 6; //游戏类型
     * int32 subType                  = 7; //游戏子类型
     * repeated int32 supportDevices  = 8; //支持设备 1.pc 2.mobile 3.pad
     * repeated int32 supportCurrency = 9; //支持货币
     * int32 tag                      =10; //标签 1.热门 2.最新 3.推荐
     * string fileUrl                 =11; //图片地址
     * int32 status                   =12; //状态 1.正常 2.关闭 3.维护
     * int32 onlineNum                =13; //在线人数
     * bool favorites                 =14; //是否收藏
     * string rtp                     =15; //rtp
     * string desc                    =16; //描述
     * int32 maxMul                   =17; //最大倍数
     * int32 gameFluctuate            =18; //游戏波动 1.高 2.中 3.低
     *
     * @param player
     * @param c_gameApi
     * @return
     */
    public static CommonMessage.GameApiInfo buildGameApiInfo(Player player, C_GameApi c_gameApi) {
        final CommonMessage.GameApiInfo.Builder gameApiInfo = CommonMessage.GameApiInfo.newBuilder()
                .setGameId(c_gameApi.getGameId())
                .setPlatformGameId(c_gameApi.getPlatformGameId())
                .setGameProvider(c_gameApi.getPlatformName())
                .setName(c_gameApi.getGameName())
                .setType(c_gameApi.getType())
                .addAllSubType(c_gameApi.getSubType())
                .addAllSupportCurrency(c_gameApi.getSupportCurrency())
                .setTag(c_gameApi.getTag())
                .setSeqNum(c_gameApi.getSeq())
                .setFileUrl(c_gameApi.getFileUrl())
                .setStatus(c_gameApi.getStatus())
                .setPlatformId(c_gameApi.getPlatformId())
                .setFavorites(player != null && player.getFavoritesGame().contains(c_gameApi.getGameId()))
                .setRtp(c_gameApi.getRtp())
                .setDesc(c_gameApi.getDescribe())
                .setMaxMul(c_gameApi.getMaxMul())
                .setGameFluctuate(c_gameApi.getGameFluctuate())
                .setFreePlay(c_gameApi.isFreePlay())
                .setBonus(c_gameApi.isBonus())
                .setInnerLink(c_gameApi.getInnerLink());
        final AgentGameInfo agentGameInfo = ServerMrg.getInstance().getAgentGameInfo(c_gameApi.getGameId());
        if (agentGameInfo != null) {
            gameApiInfo.setOnlineNum(agentGameInfo.getOnline());
        } else {
            gameApiInfo.setOnlineNum(1);
        }
        return gameApiInfo.build();
    }


    public void notifyUpdateGameInfo(int type, int gameId) {
        final InnerMessage.InnerNotifyUpdateGameInfoMessage.Builder res = InnerMessage.InnerNotifyUpdateGameInfoMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.InnerNotifyUpdateGameInfo_VALUE)
                .setType(type)
                .setGameId(gameId);
        HallServer.getInstance().getHallTcpClient2Proxy().sendMsg(res.build());
    }
}
