package com.game.hall.mrg;

import com.game.c_entity.merchant.C_Activity;
import com.game.c_entity.merchant.C_ContinuousDeposit;
import com.game.c_entity.merchant.C_LuckSpin;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.TimeUtil;
import com.game.entity.activity.WinTicketsNote;
import com.game.entity.player.Player;
import com.game.enums.ActivityType;
import com.game.enums.LuckSpinType;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.IGameScript;
import com.game.hall.script.activity.IActivityScript;
import com.game.hall.script.activity.ILuckSpinScript;
import com.proto.ActivityMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 每日 每周 有初始化数据的需要实现onLogin
 * 指定活动时间 只需要实现reset
 * <p>
 * 1.手动 2.完成发放
 * 1.手动 2.结算发放
 * <p>
 * 1.自动 2.完成发放
 * 1.自动 2.结算发放
 */
public class ActivityMrg {
    private static final Logger LOGGER = LoggerFactory.getLogger(ActivityMrg.class);

    public static final int MULTIPLE = 10000;

    public static final int RANK_RECHARGE = 1;
    public static final int RANK_WAGERED = 2;
    public static final int RANK_WIN = 3;
    public static final int RANK_MULTIPLE = 4;
    public static final int RANK_AFFILIATE = 5;

    public static final int LUCK_SPIN_REFERRAL = 1000;
    public static final int LUCK_SPIN_VIP = 1001;//1.luck 2.super 3.mega
    public static final int LUCK_SPIN_DAILY = 1002;
    public static final int LUCK_SPIN_WEEKLY = 1003;
    public static final int DAILY_CONTEST = 2000;
    public static final int WEEKLY_RAFFLE = 3000;

    public static final int MANY_DEPOSIT = 4000;
    public static final int DEPOSIT = 5000;
    public static final int WAGERED = 6000;
    public static final int RANK = 7000;
    public static final int COMPENSATE = 8000;
    public static final int FREE_GIVE = 9000;
    public static final int MANUAL_AWARD = 10000;
    public static final int RED_ENVELOPE_RAIN = 11000;
    public static final int REWARD_BOX = 12000;
    public static final int MYSTERY_BONUS = 13000;
    public static final int PIGGY_BANK = 14000;
    public static final int CONTINUOUS_DEPOSIT = 15000;
    public static final int FIRSTCHARGE_SIGNIN = 16000;
    public static final int RECHARGE_RECOVER = 17000;
    public static final int WAGERED_REBATES = 18000;
    public static final int FIRSTDEPOSIT_INIVTEBONUS = 19000;
    public static final int FIRSTDEPOSIT_GIVEUP = 20000;

    private static final ActivityMrg instance = new ActivityMrg();

    public static ActivityMrg getInstance() {
        return instance;
    }

    public static void gameEveryDayReset() {
        ScriptLoader.getInstance().consumerScript("GameScript",
                IGameScript::gameEveryDayReset);
    }

    public static void gameEveryWeeklyReset() {
        ScriptLoader.getInstance().consumerScript("GameScript",
                (IGameScript::gameEveryWeeklyReset));
    }

    public static String getGameId() {
        final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        return dateFormat.format(TimeUtil.getDayOfWeekEndTimestamp(DayOfWeek.SUNDAY));
    }

    public static ActivityMessage.TicketsInfo buildTicketsInfo(WinTicketsNote winTicketsNote) {
        return ActivityMessage.TicketsInfo.newBuilder()
                .setNo(winTicketsNote.getNo())
                .setGameId(winTicketsNote.getGameId())
                .setTicketNumbers(winTicketsNote.getTicketNumbers())
                .setCurrencyId(winTicketsNote.getCurrencyId())
                .setPrize(winTicketsNote.getPrize())
                .setPlayerName(winTicketsNote.getPlayerName())
                .setHeadId(winTicketsNote.getHeadId())
                .build();
    }

    /**************************************************************** 活动 ****************************************************************/
    public static void activityClearData(Player player) {
        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(PlayerMrg.class.getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }
        for (Map.Entry<Integer, List<C_Activity>> entry : merchantData.getC_activityListMap().entrySet()) {
            final List<C_Activity> c_activityList = entry.getValue();
            for (C_Activity c_activity : c_activityList) {
                if (c_activity.getActivityType() == ActivityType.ManualAward.getType()) {
                    continue;
                }
                final String nameScript = Objects.requireNonNull(ActivityType.valueOf(c_activity.getActivityType())).getName();
                ScriptLoader.getInstance().consumerScript(nameScript,
                        (IActivityScript script) -> script.clearData(player, c_activity));
            }
        }
    }

    public static void activityRest(Player player, int settlementCycle) {
        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(PlayerMrg.class.getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }
        for (Map.Entry<Integer, List<C_Activity>> entry : merchantData.getC_activityListMap().entrySet()) {
            final List<C_Activity> c_activityList = entry.getValue();
            for (C_Activity c_activity : c_activityList) {
                if (c_activity.getActivityType() == ActivityType.ManualAward.getType()) {
                    continue;
                }
                if (c_activity.getSettlementCycle() != settlementCycle) {
                    continue;
                }
                final String nameScript = Objects.requireNonNull(ActivityType.valueOf(c_activity.getActivityType())).getName();
                ScriptLoader.getInstance().consumerScript(nameScript,
                        (IActivityScript script) -> script.resetData(player, c_activity));
            }
        }
    }

    public static void activityInit(Player player) {
        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(PlayerMrg.class.getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }
        for (Map.Entry<Integer, List<C_Activity>> entry : merchantData.getC_activityListMap().entrySet()) {
            final List<C_Activity> c_activityList = entry.getValue();
            for (C_Activity c_activity : c_activityList) {
                if (c_activity.getActivityType() == ActivityType.ManualAward.getType()) {
                    continue;
                }
                final String nameScript = Objects.requireNonNull(ActivityType.valueOf(c_activity.getActivityType())).getName();
                ScriptLoader.getInstance().consumerScript(nameScript,
                        (IActivityScript script) -> script.initData(player, c_activity));
            }
        }
    }

    public static void continuousDepositRest(Player player, int settlementCycle) {
        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(PlayerMrg.class.getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }
        final C_ContinuousDeposit c_continuousDeposit = merchantData.findC_ContinuousDeposit(ActivityMrg.class.getSimpleName(), ActivityMrg.CONTINUOUS_DEPOSIT);
        if (c_continuousDeposit == null) {
            return;
        }

        if (c_continuousDeposit.getResetCycle() != settlementCycle) {
            return;
        }

        ScriptLoader.getInstance().consumerScript("ContinuousDepositScript",
                (IActivityScript script) -> script.resetData(player, null));
    }

    /**************************************************************** 幸运转盘 ****************************************************************/

    public static void luckSpinInit(Player player) {
        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(PlayerMrg.class.getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }
        for (Map.Entry<Integer, List<C_LuckSpin>> entry : merchantData.getC_luckSpinMap().entrySet()) {
            final List<C_LuckSpin> c_luckSpinList = entry.getValue();
            for (C_LuckSpin c_luckSpin : c_luckSpinList) {
                final String nameScript = Objects.requireNonNull(LuckSpinType.valueOf(c_luckSpin.getTurntableType())).getName();
                ScriptLoader.getInstance().consumerScript(nameScript,
                        (ILuckSpinScript script) -> script.initData(player, c_luckSpin));
            }
        }
    }

    public static void luckSpinRest(Player player, int activityId) {
        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(PlayerMrg.class.getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }
        for (Map.Entry<Integer, List<C_LuckSpin>> entry : merchantData.getC_luckSpinMap().entrySet()) {
            if (entry.getKey() != activityId) {
                continue;
            }
            final List<C_LuckSpin> c_luckSpinList = entry.getValue();
            for (C_LuckSpin c_luckSpin : c_luckSpinList) {
                final String nameScript = Objects.requireNonNull(LuckSpinType.valueOf(c_luckSpin.getTurntableType())).getName();
                ScriptLoader.getInstance().consumerScript(nameScript,
                        (ILuckSpinScript script) -> script.resetData(player, c_luckSpin));
            }
        }
    }

    public static void luckSpinExecute(Player player, int currencyId, double wageredAmount, double rechargeAmount) {
        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(PlayerMrg.class.getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }
        for (Map.Entry<Integer, List<C_LuckSpin>> entry : merchantData.getC_luckSpinMap().entrySet()) {
            final List<C_LuckSpin> c_luckSpinList = entry.getValue();
            for (C_LuckSpin c_luckSpin : c_luckSpinList) {
                final String nameScript = Objects.requireNonNull(LuckSpinType.valueOf(c_luckSpin.getTurntableType())).getName();
                ScriptLoader.getInstance().consumerScript(nameScript,
                        (ILuckSpinScript script) -> script.luckSpinExecute(player, c_luckSpin, currencyId, wageredAmount, rechargeAmount));
            }
        }
    }
}
