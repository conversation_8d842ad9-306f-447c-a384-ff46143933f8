package com.game.handler.tcp;

import com.game.billingsr.main.BillingServer;
import com.game.billingsr.manager.BillingMrg;
import com.game.dao.order.WithdrawOrderDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.log.GameLog;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.JsonUtils;
import com.game.entity.order.WithdrawInfo;
import com.game.entity.order.WithdrawOrder;
import com.game.entity.player.PayInfo;
import com.game.entity.player.Player;
import com.game.entity.player.WithdrawAccount;
import com.game.entity.player.WithdrawStandard;
import com.game.entity.player.stats.Stats;
import com.game.enums.ErrorCode;
import com.game.enums.OrderType;
import com.game.manager.EntityDaoMrg;
import com.proto.BillingMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


@IHandlerEntity(mid = MIDMessage.MID.ReqCreateWithdrawOrder_VALUE, msg = BillingMessage.ReqCreateWithdrawOrderMessage.class)
public class BillingReqCreateWithdrawOrderHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(BillingReqCreateWithdrawOrderHandler.class);

    @Override
    public void run() {
        final BillingMessage.ResCreateWithdrawOrderMessage.Builder res = BillingMessage.ResCreateWithdrawOrderMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResCreateWithdrawOrder_VALUE);
        try {
            final Player player = BillingMrg.getInstance().findDbPlayer(this.pid);
            if (player == null) {
                LOGGER.warn("playerId：{}，not exits", pid);
                res.setError(ErrorCode.Account_NotExist.getCode());
                replyWithUdpSessionId(res.build());
                return;
            }

            final BillingMessage.ReqCreateWithdrawOrderMessage req = (BillingMessage.ReqCreateWithdrawOrderMessage) getMessage();
            final int currencyId = req.getCurrencyId();

            final int currencyType = currencyId / 1000;
            ErrorCode errorCode = ErrorCode.Success;
            switch (currencyType) {
                case 1://法币
                    errorCode = fiatWithdraw(player, req);
                    break;
                case 2://虚拟币
                    errorCode = cryptoWithdraw(player, req);
                    break;
            }

            res.setError(errorCode.getCode());
            replyWithUdpSessionId(res.build());
        } catch (Exception e) {
            LOGGER.error("ReqCreateWithDrawOrderHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            replyWithUdpSessionId(res.build());
        }
    }

    private ErrorCode cryptoWithdraw(Player player, BillingMessage.ReqCreateWithdrawOrderMessage req) {
        try {
            final long orderId = req.getOrderId();
            final double fee = req.getFee();
            final double balance = req.getBalance();
            final double drawStandard = req.getDrawStandard();
            final double bettingVolume = req.getBettingVolume();
            final BillingMessage.WithdrawCrypto withdrawCrypto = req.getWithdrawCrypto();
            final int currencyId = withdrawCrypto.getCurrencyId();
            final double amount = withdrawCrypto.getAmount();
            final String walletAddress = withdrawCrypto.getWalletAddress();
            final String netWork = withdrawCrypto.getNetwork();
            final int exchangeCurrency = req.getExchangeCurrency();

            final WithdrawInfo withdrawInfo = new WithdrawInfo();
            withdrawInfo.setFee(fee);
            withdrawInfo.setActualAmount(BigDecimalUtils.sub(amount, fee, 4));

            final WithdrawInfo.WithdrawAccount withdrawAccount = new WithdrawInfo.WithdrawAccount();
            withdrawAccount.setExtend(walletAddress);
            withdrawAccount.setExtend_1(netWork);

            final WithdrawOrder withdrawOrder = new WithdrawOrder(orderId);
            BillingMrg.getInstance().createOrderInfo(player, withdrawOrder, OrderType.WITHDRAW, currencyId, amount);
            withdrawOrder.setHallId(player.getHallId());
            withdrawOrder.setPaymentMethod(401);
            withdrawOrder.setWithdrawAccount(JsonUtils.writeAsJson(withdrawAccount));
            withdrawOrder.setWithdrawInfo(JsonUtils.writeAsJson(withdrawInfo));

            BillingMrg.getInstance().addOrderInfo(withdrawOrder);
            EntityDaoMrg.getInstance().getDao(WithdrawOrderDao.class).insert(withdrawOrder);

            final GameLog gameLog = BillingMrg.newGameLog("platform_withdrawOrder", withdrawOrder);
            gameLog.append("site", player.getWebSite())
                    .append("agentId", player.getAgentId())
                    .append("channelId", player.getChannelId())
                    .append("mediaId", player.getMediaId())
                    .append("adId", player.getAdId())
                    .append("region", player.getRegisterRegion())
                    .append("hallId", player.getHallId())
                    .append("paymentMethod", 401)
                    .append("balance", balance)
                    .append("drawStandard", drawStandard)
                    .append("bettingVolume", bettingVolume)
                    .append("network", withdrawCrypto.getNetwork())
                    .append("exchangeCurrency", exchangeCurrency)
                    .append("withdrawAccount", JsonUtils.writeAsJson(withdrawAccount))
                    .append("withdrawInfo", withdrawOrder.getWithdrawInfo());

            final Stats stats = player.getStats(exchangeCurrency);
            final WithdrawInfo.RechargeCondition rechargeCondition = new WithdrawInfo.RechargeCondition();
            rechargeCondition.setTotalRechargeTimes(stats.getTotalRechargeTimes());
            rechargeCondition.setTotalRechargeAmount(stats.getTotalRechargeAmount());
            rechargeCondition.setDailyRechargeTimes(stats.getDailyRechargeTimes());
            gameLog.append("rechargeCondition", JsonUtils.writeAsJson(rechargeCondition));

            final WithdrawInfo.BetCondition betCondition = new WithdrawInfo.BetCondition();
            betCondition.setRealityBetTimes(stats.getTotalBetTimes());
            gameLog.append("betTimesCondition", JsonUtils.writeAsJson(betCondition));

            final WithdrawStandard withdrawStandard = player.getWithdrawStandard(exchangeCurrency);
            final WithdrawInfo.TurnoverCondition turnoverCondition = new WithdrawInfo.TurnoverCondition();
            turnoverCondition.setDrawStandard(withdrawStandard.getDrawStandard());
            turnoverCondition.setBettingTurnover(withdrawStandard.getBettingVolume());
            gameLog.append("turnoverCondition", JsonUtils.writeAsJson(turnoverCondition));

            final WithdrawInfo.WithdrawCondition withdrawCondition = new WithdrawInfo.WithdrawCondition();
            withdrawCondition.setTotalWithdrawTimes(stats.getTotalWithdrawTimes());
            withdrawCondition.setDailyFreeWithdrawTimes(player.getDailyFeeTimes());//已使用的提现次数
            withdrawCondition.setDailyWithdrawTimes(stats.getDailyWithdrawTimes());
            gameLog.append("withdrawCondition", JsonUtils.writeAsJson(withdrawCondition));
            BillingServer.getInstance().getLogProducerMrg().send(gameLog);
        } catch (Exception e) {
            LOGGER.error("cryptoWithdraw", e);
            return ErrorCode.Internal_Server_Error;
        }
        return ErrorCode.Success;
    }

    private ErrorCode fiatWithdraw(Player player, BillingMessage.ReqCreateWithdrawOrderMessage req) {
        try {
            final long orderId = req.getOrderId();
            final double fee = req.getFee();
            final double balance = req.getBalance();
            final double drawStandard = req.getDrawStandard();
            final double bettingVolume = req.getBettingVolume();
            final BillingMessage.WithdrawFiat withdrawFiat = req.getWithdrawFiat();

            final long withdrawAccountId = StringUtil.isNullOrEmpty(withdrawFiat.getWithdrawAccountId()) ? 0 : Long.parseLong(withdrawFiat.getWithdrawAccountId());
            final String currencyIdPayment = withdrawFiat.getCurrencyId() + "_" + withdrawFiat.getPaymentMethod() + "_" + withdrawFiat.getChannel();
            final WithdrawAccount pWithdrawAccount = player.getWithdrawAccountMap().get(currencyIdPayment);
            final PayInfo payInfo = pWithdrawAccount.getPayInfoMap().get(withdrawAccountId);

            final WithdrawInfo.WithdrawAccount withdrawAccount = new WithdrawInfo.WithdrawAccount();
            withdrawAccount.setExtend(payInfo.getExtend());
            withdrawAccount.setExtend_1(payInfo.getExtend_1());
            withdrawAccount.setExtend_2(payInfo.getExtend_2());
            withdrawAccount.setExtend_3(payInfo.getExtend_3());

            final int currencyId = withdrawFiat.getCurrencyId();
            final double amount = withdrawFiat.getAmount();

            final WithdrawInfo withdrawInfo = new WithdrawInfo();
            withdrawInfo.setFee(fee);
            withdrawInfo.setActualAmount(BigDecimalUtils.sub(amount, fee, 4));

            final WithdrawOrder withdrawOrder = new WithdrawOrder(orderId);
            BillingMrg.getInstance().createOrderInfo(player, withdrawOrder, OrderType.WITHDRAW, currencyId, amount);
            withdrawOrder.setHallId(player.getHallId());
            withdrawOrder.setPaymentMethod(withdrawFiat.getPaymentMethod());
            withdrawOrder.setWithdrawAccount(JsonUtils.writeAsJson(withdrawAccount));
            withdrawOrder.setWithdrawInfo(JsonUtils.writeAsJson(withdrawInfo));

            BillingMrg.getInstance().addOrderInfo(withdrawOrder);
            EntityDaoMrg.getInstance().getDao(WithdrawOrderDao.class).insert(withdrawOrder);

            final GameLog gameLog = BillingMrg.newGameLog("platform_withdrawOrder", withdrawOrder);
            gameLog.append("site", player.getWebSite())
                    .append("paymentMethod", withdrawFiat.getPaymentMethod())
                    .append("balance", balance)
                    .append("drawStandard", drawStandard)
                    .append("bettingVolume", bettingVolume)
                    .append("withdrawAccount", withdrawOrder.getWithdrawAccount())
                    .append("withdrawInfo", withdrawOrder.getWithdrawInfo())
                    .append("agentId", player.getAgentId())
                    .append("channelId", player.getChannelId())
                    .append("withdrawChannel", withdrawFiat.getChannel())
                    .append("mediaId", player.getMediaId())
                    .append("adId", player.getAdId())
                    .append("region", player.getRegisterRegion())
                    .append("exchangeCurrency", currencyId);

            final Stats stats = player.getStats(currencyId);
            final WithdrawInfo.RechargeCondition rechargeCondition = new WithdrawInfo.RechargeCondition();
            rechargeCondition.setTotalRechargeTimes(stats.getTotalRechargeTimes());
            rechargeCondition.setDailyRechargeTimes(stats.getDailyRechargeTimes());
            rechargeCondition.setTotalRechargeAmount(stats.getTotalRechargeAmount());
            gameLog.append("rechargeCondition", JsonUtils.writeAsJson(rechargeCondition));

            final WithdrawInfo.BetCondition betCondition = new WithdrawInfo.BetCondition();
            betCondition.setRealityBetTimes(stats.getTotalBetTimes());
            gameLog.append("betTimesCondition", JsonUtils.writeAsJson(betCondition));

            WithdrawStandard withdrawStandard = player.getWithdrawStandard(currencyId);
            final WithdrawInfo.TurnoverCondition turnoverCondition = new WithdrawInfo.TurnoverCondition();
            turnoverCondition.setDrawStandard(withdrawStandard.getDrawStandard());
            turnoverCondition.setBettingTurnover(withdrawStandard.getBettingVolume());
            gameLog.append("turnoverCondition", JsonUtils.writeAsJson(turnoverCondition));

            final WithdrawInfo.WithdrawCondition withdrawCondition = new WithdrawInfo.WithdrawCondition();
            withdrawCondition.setTotalWithdrawTimes(stats.getTotalWithdrawTimes());
            withdrawCondition.setDailyWithdrawTimes(stats.getDailyWithdrawTimes());
            withdrawCondition.setDailyFreeWithdrawTimes(player.getDailyFeeTimes());
            gameLog.append("withdrawCondition", JsonUtils.writeAsJson(withdrawCondition));
            BillingServer.getInstance().getLogProducerMrg().send(gameLog);
        } catch (Exception e) {
            LOGGER.error("fiatWithdraw", e);
            return ErrorCode.Internal_Server_Error;
        }
        return ErrorCode.Success;
    }

}
