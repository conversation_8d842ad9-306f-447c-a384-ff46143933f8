package com.game.enums;

public enum MerchantConfigType {
    Activity(201),//活动
    Banner(202),//广告
    MaintainNotice(203),//维护公告
    CollectionWallet(204),//钱包地址
    Currency(205),//货币
    //    ExchangeRate(206),//汇率
    GameApi(207),//游戏列表
    GameChannel(208),//游戏频道
    GamePlatform(209),//游戏平台
    Global(210),//全局变量
    PlatformRecharge(211),//充值
    PlatformWithdraw(212),//提现
    Popup(214),//弹窗
    PubMail(215),//邮件
    RechargeTurnover(217),//充值打码量
    RechargeWithdrawLimit(218),//充值提现限制
    Region(219),//地区
    VipClub(220),//vip
    HelpCenter(222),//帮助中心
    ReferralReward(223),//推荐奖励
    InvitationCashBack(224),//邀请返水
    LuckSpin(225),//幸运转盘
    DailyContest(226),//每日竞赛
    WeeklyRaffle(227),//每日抽奖
    Quest(228),//任务
    GameTurnover(229),//游戏打码
    CustomerService(230),//客服
    WinTicketNumbers(231),//中奖票号
    BottomMenu(232),//底部菜单
    WebSiteInfo(233),//站点信息
    QualityAssurance(234),//QA
    InvitationPoster(235),//邀请海报
    FreeGameTurnOver(236),//免费游戏打码
    InvitationLinks(237),//邀请连接
    RedemptionCode(238),//兑换码
    ThreePartyLogin(239),//三方登录
    FunctionEnabled(240),//功能开启
    QuickAccess(241),//快速入口
    RedEnvelopeRain(242),//红包雨
    RewardBox(243),//推荐宝箱
    MysteryBonus(244),//神秘奖金
    PiggyBank(245),//存钱罐
    ContinuousDeposit(246),//连续充值
    Pwa(247),//pwa
    ActivityTag(248),//活动标签
    WebSite(249),//站点
    PhoneMsg(250),//短信
    MailSmtp(251),//邮件
    BigWin(252),//大赢家
    SubChannelGameApi(253),//子频道游戏列表
    RegisterRetrievePop(254),//注册挽回弹窗
    DailyRechargePop(255),//每日充值弹窗
    FirstChargePop(256),//首次充值弹窗
    GamePop(257),//游戏弹窗
    FirstChargeSignIn(258),//首充签到
    CrazyBox(259),//抽卡
    CrazyBoxQuest(260),//抽卡任务
    Advertise(261),//广告
    RechargeRecover(262),//充值返奖
    GameKillRate(263),//游戏杀率
    WageredRebates(264),//投注返利
    FirstDepositInviteBonus(265),//首充邀请
    RechargeExchange(266),//充值兑换
    WithdrawExchange(267),//提现兑换
    CurrencyTraderWithdraw(273),//币商提现
    TgReward(274),//tg通知
    ;

    private final int type;

    MerchantConfigType(int type) {
        this.type = type;
    }

    public int getType() {
        return type;
    }

    public static MerchantConfigType valueOf(int type) {
        for (MerchantConfigType merchantConfigType : MerchantConfigType.values()) {
            if (merchantConfigType.getType() == type) {
                return merchantConfigType;
            }
        }
        throw new IllegalArgumentException("ReloadConfigType：" + type + "不存在");
    }
}
