package com.game.enums;

/**
 * QUEST-任务
 * *      Weekly Quest
 * *      Daily Quest
 * SPIN-转盘
 * *      Referral Spin
 * *      Daily Spin
 * *      VIP Spin
 * PROMO-活动
 * *      First Deposit
 * *      Second Deposit
 * *      Third Deposit
 * *      Accumulated Deposit -累充
 * *      Single Deposit-单次充值
 * *      Wagering Event- 投注活动
 * *      Deposit Ranking
 * *      Wagering Ranking
 * *      Winning Ranking
 * *      Multiplier Ranking
 * *      Invitation Ranking
 * *      Daily Relief
 * *      Weekly Relief
 * *      Free Event
 * AFFILIATE-推广
 * *    Commission Rewards
 * *    Team Rewards
 * *     Referral Rewards
 * VIP-VIP奖励
 * Monthly Cashback
 * Weekly Cashback
 * Recharge
 * Level Up
 */
public enum TransactionFrom {
    None(0, ""),
    Quest_Daily(100, "Daily Quest"),//日常任务
    Quest_Weekly(101, "Weekly Quest"),//周常任务

    LuckSpin_Referral(200, "Referral Spin"),//推广转盘
    LuckSpin_Daily(201, "Daily Spin"),//日转盘
    LuckSpin_Weekly(202, "Weekly Spin"),//周转盘
    LuckSpin_Vip(203, "Vip Spin"),//vip转盘

    Promotion_FirstDeposit(300, "First Deposit"),//首充
    Promotion_SecondDeposit(301, "Second Deposit"),//二充
    Promotion_ThirdDeposit(302, "Third Deposit"),//三充

    FreeSpin(400, "Free Games"),//免费Spin

    Vip_LevelUp(500, "Level Up"),//Vip升级
    Vip_Recharge(600, "Recharge"),//Vip充电
    Vip_WeeklyCashBack(700, "Weekly CashBack"),//Vip周返水
    Vip_MonthlyCashBack(800, "Monthly CashBack"),//Vip月返水
    Vip_DayCashBack(900, "Day CashBack"),//Vip日返水

    Promotion_AccumulatedDeposit(2000, "Accumulated Deposit"),//累计充
    Promotion_SingleDeposit(2001, "Single Deposit"),//单次充
    Promotion_WageringEvent(2002, "Wagering Event"),//下注活动
    Promotion_SignUpGift(2003, "SignUp Gift"),//注册活动
    Promotion_LoginGift(2004, "Login Gift"),//登录活动
    Promotion_ReturnGift(2005, "Return Gift"),//回归活动
    Promotion_DailyRelief(2006, "Daily Relief"),//每日救济
    Promotion_WeeklyRelief(2007, "Weekly Relief"),//每周救济
    Promotion_DepositRank(2008, "Deposit Ranking"),//充值排行活动
    Promotion_WageringRank(2009, "Wagering Ranking"),//下注排行活动
    Promotion_WinningRank(2010, "Winning Ranking"),//派彩排行活动
    Promotion_MultiplierRank(2011, "Multiplier Ranking"),//派彩倍数排行活动
    Promotion_InvitationRank(2012, "Invitation Ranking"),//邀请排行活动

    Affiliate_CommissionRewards(2100, "1st-Lvl Rewards"),//推广上级
    Affiliate_TeamRewards(2101, "2nd-Lvl Rewards"),//推广团长
    Affiliate_ReferralRewards(2102, "3rd-Lvl Rewards"),//推广奖励
    Affiliate_RedemptionCodeRewards(2103, "Redeem Code"),
    Affiliate_ThreeLevelRewards(2104, "ThreeLevel Rewards"),//推广三级
    Affiliate_InvitedRewards(2105, "Invited Rewards"),//被邀请奖励

    DailyContest(2200, "DailyContest"),//每日竞赛
    WeeklyRaffle(2300, "WeeklyRaffle"),//每周抽奖
    BonusRain(2400, "Bonus Rain"),//红包雨
    RewardBox(2500, "Reward Box"),//推荐宝箱
    MysteryBonus(2600, "Mystery Bonus"),//神秘奖金
    PiggyBank(2700, "Piggy Bank"),//存钱罐

    VipSignIn(2800, "Vip SignIn"),//vip 签到
    Vip_Daily(2801, "Daily Rewards"),
    Vip_Weekly(2802, "Weekly Rewards"),
    Vip_Monthly(2803, "Monthly Rewards"),

    ContinuousDeposit(2900, "Continuous Deposit"),
    Pwa(3000, "Pwa Rewards"),
    FirstChargeSignIn(3100, "FirstCharge Checkin"),
    RechargeRecover(3200, "Recover Missed Rewards"),
    WageredRebates(3300, "Wagered Rebates"),
    FirstDepositInviteBonus(3400, "First Deposit Invite Bonus"),
    FirstDepositGiveUp(3500, "First Deposit Rescue"),
    Tg_Share(3600, "Tg Share"),
    ;

    private final int type;

    private final String name;

    TransactionFrom(int type, String name) {
        this.type = type;
        this.name = name;
    }

    public int getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public static TransactionFrom valuesOf(int transactionFrom) {
        for (TransactionFrom type : TransactionFrom.values()) {
            if (type.getType() == transactionFrom) {
                return type;
            }
        }
        throw new IllegalArgumentException("TransactionFrom，error：" + transactionFrom);
    }
}
