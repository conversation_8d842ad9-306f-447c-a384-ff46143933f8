package com.game.enums;

/**
 * *10 礼码
 * 大类 1.法币 2.虚拟币
 */
public enum Currency {
    BRL(1000),//巴西雷亚尔
    INR(1001),//印度卢比
    USD(1002),//美元
    MXN(1003),//马币
    PHP(1004),//菲律宾比索

    USDT(2000),//泰达币
    ;

    private final int currencyId;

    Currency(int currencyId) {
        this.currencyId = currencyId;
    }

    public int getCurrencyId() {
        return currencyId;
    }

    public static Currency valueOf(int currencyId) {
        for (Currency currency : Currency.values()) {
            if (currency.getCurrencyId() == currencyId) {
                return currency;
            }
        }
        return null;
    }

}
