package com.game.enums;

public enum ActivityType {
    <PERSON><PERSON><PERSON>(1, "转盘"),
    DailyContest(2, "每日竞赛"),
    WeeklyRaffle(3, "每周抽奖"),
    ManyDeposit(4, "ManyDepositScript"),
    Deposit(5, "DepositScript"),
    Wagered(6, "WageredScript"),
    Rank(7, "RankScript"),
    Compensate(8, "CompensateScript"),
    FreeGive(9, "FreeGiveScript"),
    ManualAward(10, "ManualAwardScript"),
    FirstDepositGiveUp(20, "FirstDepositGiveUpScript"),
    ;

    private final int type;

    private final String name;

    ActivityType(int type, String name) {
        this.type = type;
        this.name = name;
    }

    public int getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public static ActivityType valueOf(int type) {
        for (ActivityType activityType : ActivityType.values()) {
            if (activityType.getType() == type) {
                return activityType;
            }
        }
        return null;
    }
}
