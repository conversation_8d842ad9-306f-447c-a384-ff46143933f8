package com.game.c_entity.merchant;

import com.game.dao.game.GameNoteDao;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.TimeUtil;
import com.game.entity.game.GameNote;
import com.game.entity.game.GameNoteFields;
import com.game.manager.EntityDaoMrg;
import com.game.po.data.IDataChecker;
import io.netty.util.internal.StringUtil;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Document(collection = "c_platformWithdraw")
public class C_PlatformWithdraw implements IDataChecker {

    @Id
    private ObjectId _id;

    //商户
    private String business_no;

    //货币id
    private int currencyId;

    //货币种类 1.法币 2.加密
    private int currencyType;

    //类别 1.Bank transfer 2.E-wallet 3.Mobile Payment
    private int category;

    //渠道
    private int channel;

    //支付方式
    private int paymentMethod;

    //网络协议
    private String network;

    //网络协议
    private String networkShow;

    //提现固定手续费
    private double fixedWithdrawFee;

    //提现最低手续费
    private double minimumWithdrawFee;

    //充值无流水手续费
    private double chargeWithdrawFeeRate;

    //提现手续费费率
    private double withdrawFeeRate;

    //单笔提现下限
    private int singleWithdrawLowerLimit;

    //单笔提现上限
    private int singleWithdrawUpperLimit;

    //提现信息
    private List<String> withdrawInfos = new ArrayList<>();

    //充值无流水时间
    private long chargeTime;

    //是否开放
    private boolean open;

    private long deleteTime;

    private transient final Map<String, WithdrawInfo> withdrawInfoMap = new LinkedHashMap<>();

    @Override
    public boolean check() throws Exception {
        if (withdrawInfos != null && !withdrawInfos.isEmpty()) {
            for (String withdraw : withdrawInfos) {
                final WithdrawInfo withdrawInfo = JsonUtils.readFromJson(withdraw, WithdrawInfo.class);
                withdrawInfoMap.put(withdrawInfo.name, withdrawInfo);
            }
        }
        return true;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public int getCurrencyId() {
        return currencyId;
    }

    public void setCurrencyId(int currencyId) {
        this.currencyId = currencyId;
    }

    public int getCurrencyType() {
        return currencyType;
    }

    public void setCurrencyType(int currencyType) {
        this.currencyType = currencyType;
    }

    public int getChannel() {
        return channel;
    }

    public void setChannel(int channel) {
        this.channel = channel;
    }

    public int getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(int paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public String getNetwork() {
        return StringUtil.isNullOrEmpty(network) ? "" : network;
    }

    public void setNetwork(String network) {
        this.network = network;
    }

    public String getNetworkShow() {
        return StringUtil.isNullOrEmpty(networkShow) ? "" : networkShow;
    }

    public void setNetworkShow(String networkShow) {
        this.networkShow = networkShow;
    }

    public double getFixedWithdrawFee() {
        return fixedWithdrawFee;
    }

    public void setFixedWithdrawFee(double fixedWithdrawFee) {
        this.fixedWithdrawFee = fixedWithdrawFee;
    }

    public double getMinimumWithdrawFee() {
        return minimumWithdrawFee;
    }

    public void setMinimumWithdrawFee(double minimumWithdrawFee) {
        this.minimumWithdrawFee = minimumWithdrawFee;
    }

    public double getWithdrawFeeRate() {
        return withdrawFeeRate;
    }

    public void setWithdrawFeeRate(double withdrawFeeRate) {
        this.withdrawFeeRate = withdrawFeeRate;
    }

    public boolean isOpen() {
        return open;
    }

    public void setOpen(boolean open) {
        this.open = open;
    }

    public int getCategory() {
        return category;
    }

    public void setCategory(int category) {
        this.category = category;
    }

    public int getSingleWithdrawLowerLimit() {
        return singleWithdrawLowerLimit;
    }

    public void setSingleWithdrawLowerLimit(int singleWithdrawLowerLimit) {
        this.singleWithdrawLowerLimit = singleWithdrawLowerLimit;
    }

    public int getSingleWithdrawUpperLimit() {
        return singleWithdrawUpperLimit;
    }

    public void setSingleWithdrawUpperLimit(int singleWithdrawUpperLimit) {
        this.singleWithdrawUpperLimit = singleWithdrawUpperLimit;
    }

    public void setWithdrawInfos(List<String> withdrawInfos) {
        this.withdrawInfos = withdrawInfos;
    }

    public List<String> getWithdrawInfos() {
        return withdrawInfos;
    }

    public long getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(long deleteTime) {
        this.deleteTime = deleteTime;
    }

    public Map<String, WithdrawInfo> getWithdrawInfoMap() {
        return withdrawInfoMap;
    }

    public double getChargeWithdrawFeeRate() {
        return chargeWithdrawFeeRate;
    }

    public void setChargeWithdrawFeeRate(double chargeWithdrawFeeRate) {
        this.chargeWithdrawFeeRate = chargeWithdrawFeeRate;
    }

    public long getChargeTime() {
        return chargeTime;
    }

    public void setChargeTime(long chargeTime) {
        this.chargeTime = chargeTime;
    }

    public static class WithdrawInfo {
        public String name;
        public List<String> params = new ArrayList<>();
        public String type;

        public String getName() {
            return StringUtil.isNullOrEmpty(name) ? "" : name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public List<String> getParams() {
            return params;
        }

        public void setParams(List<String> params) {
            this.params = params;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }
    }

    public double calculateFee(long playerId, int currencyId, long chargeTime, double amount) {
        if (TimeUtil.currentTimeMillis() < chargeTime + this.chargeTime) {
            final GameNote gameNote = EntityDaoMrg.getInstance().getDao(GameNoteDao.class)
                    .aggregateBet(playerId, currencyId, GameNoteFields.validBets, chargeTime, chargeTime + this.chargeTime);
            if (gameNote.getValidBets() == 0) {
                final double free = BigDecimalUtils.add(this.fixedWithdrawFee, BigDecimalUtils.mul(amount, this.chargeWithdrawFeeRate, 9), 9);
                return Math.max(free, this.minimumWithdrawFee);
            }
        }

        final double free = BigDecimalUtils.add(this.fixedWithdrawFee, BigDecimalUtils.mul(amount, this.withdrawFeeRate, 9), 9);
        return Math.max(free, this.minimumWithdrawFee);
    }
}
