package com.game.c_entity.merchant;

import com.game.engine.utils.JsonUtils;
import com.game.po.data.IDataChecker;
import io.netty.util.internal.StringUtil;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.*;

@Document(collection = "c_gameApi")
public class C_GameApi implements IDataChecker {

    @Id
    private ObjectId _id;

    //商户
    @Indexed
    private String business_no;

    //游戏id
    @Indexed
    private int gameId;

    //平台id
    @Indexed
    private int platformId;

    //游戏类型
    @Indexed
    private int type;

    //游戏平台名字
    private String platformName;

    //平台游戏id
    private String platformGameId;

    //支持货币
    private List<Integer> supportCurrency = new ArrayList<>();

    //游戏多语言数据
    private List<String> gameApiInfoList = new ArrayList<>();

    //序号
    public int seq;

    //标签 1.热门 2.推荐 3.最新
    public int tag;

    //自定义标签
    public List<Integer> customTag = new ArrayList<>();

    //最大倍数
    private int maxMul;

    //游戏波动 1.高 2.中 3.低
    private int gameFluctuate;

    //游戏子类型
    private List<Integer> subType = new ArrayList<>();

    //图片地址
    private String fileUrl;

    //是否试玩
    private boolean freePlay;

    //中台状态 1.正常 2.关闭 3.维护
    private int baseStatus;

    //状态 1.正常 2.关闭 3.维护
    private int status;

    //赠金 ture带入 false不带入
    private boolean bonus;

    //rtp
    private double rtpPool;

    private long deleteTime;

    private long updateTime;

    private String innerLink;

    private transient final Map<Integer, GameApiInfo> languageGameApiInfoMap = new LinkedHashMap<>();

    @Override
    public boolean check() throws Exception {
        if (gameApiInfoList != null && !gameApiInfoList.isEmpty()) {
            for (String gameApi : this.gameApiInfoList) {
                final GameApiInfo gameApiInfo = JsonUtils.readFromJson(gameApi, GameApiInfo.class);
                languageGameApiInfoMap.put(gameApiInfo.getLanguage(), gameApiInfo);
            }
        }
        return true;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        C_GameApi c_gameApi = (C_GameApi) o;
        return gameId == c_gameApi.gameId;
    }

    @Override
    public int hashCode() {
        return Objects.hash(gameId);
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public int getGameId() {
        return gameId;
    }

    public void setGameId(int gameId) {
        this.gameId = gameId;
    }

    public String getPlatformGameId() {
        return StringUtil.isNullOrEmpty(platformGameId) ? "" : platformGameId;
    }

    public void setPlatformGameId(String platformGameId) {
        this.platformGameId = platformGameId;
    }

    public int getPlatformId() {
        return platformId;
    }

    public void setPlatformId(int platformId) {
        this.platformId = platformId;
    }

    public String getPlatformName() {
        return StringUtil.isNullOrEmpty(platformName) ? "" : platformName;
    }

    public void setPlatformName(String platformName) {
        this.platformName = platformName;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public List<Integer> getSubType() {
        return subType;
    }

    public void setSubType(List<Integer> subType) {
        this.subType = subType;
    }

    public String getFileUrl() {
        return StringUtil.isNullOrEmpty(fileUrl) ? "" : fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public boolean isFreePlay() {
        return freePlay;
    }

    public void setFreePlay(boolean freePlay) {
        this.freePlay = freePlay;
    }

    public int getBaseStatus() {
        return baseStatus;
    }

    public void setBaseStatus(int baseStatus) {
        this.baseStatus = baseStatus;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public boolean isBonus() {
        return bonus;
    }

    public void setBonus(boolean bonus) {
        this.bonus = bonus;
    }

    public double getRtpPool() {
        return rtpPool;
    }

    public void setRtpPool(double rtpPool) {
        this.rtpPool = rtpPool;
    }

    public long getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(long deleteTime) {
        this.deleteTime = deleteTime;
    }

    public long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(long updateTime) {
        this.updateTime = updateTime;
    }

    public List<String> getGameApiInfoList() {
        return gameApiInfoList;
    }

    public void setGameApiInfoList(List<String> gameApiInfoList) {
        this.gameApiInfoList = gameApiInfoList;
    }

    public Map<Integer, GameApiInfo> getLanguageGameApiInfoMap() {
        return languageGameApiInfoMap;
    }

    //游戏rtp
    @Transient
    private String rtp;
    //游戏名字
    @Transient
    private String gameName;
    //描述
    @Transient
    private String describe;

    public boolean setLanguageGameApiData(int language) {
        final GameApiInfo gameApiInfo = this.getLanguageGameApiInfoMap().get(language);
        if (gameApiInfo == null) {
            return false;
        }
        this.rtp = gameApiInfo.rtp;
        this.gameName = gameApiInfo.gameName;
        this.describe = gameApiInfo.describe;
        return true;
    }

    public String getGameName() {
        return StringUtil.isNullOrEmpty(gameName) ? "" : gameName;
    }

    public void setGameName(String gameName) {
        this.gameName = gameName;
    }

    public int getSeq() {
        return seq;
    }

    public void setSeq(int seq) {
        this.seq = seq;
    }

    public List<Integer> getSupportCurrency() {
        return supportCurrency;
    }

    public void setSupportCurrency(List<Integer> supportCurrency) {
        this.supportCurrency = supportCurrency;
    }

    public int getTag() {
        return tag;
    }

    public void setTag(int tag) {
        this.tag = tag;
    }

    public List<Integer> getCustomTag() {
        return customTag;
    }

    public void setCustomTag(List<Integer> customTag) {
        this.customTag = customTag;
    }

    public String getRtp() {
        return StringUtil.isNullOrEmpty(rtp) ? "" : rtp;
    }

    public void setRtp(String rtp) {
        this.rtp = rtp;
    }

    public int getMaxMul() {
        return maxMul;
    }

    public void setMaxMul(int maxMul) {
        this.maxMul = maxMul;
    }

    public int getGameFluctuate() {
        return gameFluctuate;
    }

    public void setGameFluctuate(int gameFluctuate) {
        this.gameFluctuate = gameFluctuate;
    }

    public String getDescribe() {
        return StringUtil.isNullOrEmpty(describe) ? "" : describe;
    }

    public void setDescribe(String describe) {
        this.describe = describe;
    }

    public String getInnerLink() {
        return StringUtil.isNullOrEmpty(innerLink) ? "" : innerLink;
    }

    public void setInnerLink(String innerLink) {
        this.innerLink = innerLink;
    }

    public static class GameApiInfo {
        //语言
        public int language;

        //游戏名字
        public String gameName;

        //游戏rtp
        public String rtp;

        //描述
        public String describe;

        public int getLanguage() {
            return language;
        }

        public void setLanguage(int language) {
            this.language = language;
        }

        public String getGameName() {
            return StringUtil.isNullOrEmpty(gameName) ? "" : gameName;
        }

        public void setGameName(String gameName) {
            this.gameName = gameName;
        }

        public String getRtp() {
            return rtp;
        }

        public void setRtp(String rtp) {
            this.rtp = rtp;
        }

        public String getDescribe() {
            return StringUtil.isNullOrEmpty(describe) ? "" : describe;
        }

        public void setDescribe(String describe) {
            this.describe = describe;
        }
    }
}
