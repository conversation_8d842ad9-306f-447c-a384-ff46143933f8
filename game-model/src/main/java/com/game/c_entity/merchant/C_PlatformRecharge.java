package com.game.c_entity.merchant;

import com.game.engine.utils.JsonUtils;
import com.game.po.data.IDataChecker;
import io.netty.util.internal.StringUtil;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Document(collection = "c_platformRecharge")
public class C_PlatformRecharge implements IDataChecker {

    @Id
    private ObjectId _id;

    //商户
    private String business_no;

    //货币id
    private int currencyId;

    //货币种类 1.法币 2.加密
    private int currencyType;

    //类别 1.Bank transfer 2.E-wallet 3.Mobile Payment
    private int category;

    //支付金额
    private List<String> payAmountData = new ArrayList<>();

    //渠道
    private int channel;

    //支付方式
    private int paymentMethod;

    //支持方式名字
    private String paymentMethodName;

    //支付方式log
    private String paymentMethodLog;

    //网络协议
    private String network;

    //网络协议
    private String networkShow;

    //单笔充值上限
    private int singleRechargeUpperLimit;

    //单笔充值下限
    private int singleRechargeLowerLimit;

    //充值信息
    private List<String> rechargeInfos;

    private int sort;

    //是否开放
    private boolean open;

    private long deleteTime;

    private transient final Map<String, List<String>> rechargeInfoMap = new LinkedHashMap<>();

    private transient final Map<Double, PayAmountInfo> payAmountInfoMap = new LinkedHashMap<>();

    @Override
    public boolean check() throws Exception {
        if (rechargeInfos != null && !rechargeInfos.isEmpty()) {
            for (String recharge : rechargeInfos) {
                final RechargeInfo rechargeInfo = JsonUtils.readFromJson(recharge, RechargeInfo.class);
                rechargeInfoMap.put(rechargeInfo.getName(), rechargeInfo.params);
            }
        }

        if (payAmountData != null && !payAmountData.isEmpty()) {
            for (String payAmount : payAmountData) {
                final PayAmountInfo payAmountInfo = JsonUtils.readFromJson(payAmount, PayAmountInfo.class);
                payAmountInfoMap.put(payAmountInfo.amount, payAmountInfo);
            }
        }
        return true;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public int getCurrencyId() {
        return currencyId;
    }

    public void setCurrencyId(int currencyId) {
        this.currencyId = currencyId;
    }

    public int getCurrencyType() {
        return currencyType;
    }

    public void setCurrencyType(int currencyType) {
        this.currencyType = currencyType;
    }

    public int getChannel() {
        return channel;
    }

    public void setChannel(int channel) {
        this.channel = channel;
    }

    public int getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(int paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public List<String> getPayAmountData() {
        return payAmountData;
    }

    public void setPayAmountData(List<String> payAmountData) {
        this.payAmountData = payAmountData;
    }

    public String getNetwork() {
        return StringUtil.isNullOrEmpty(network) ? "" : network;
    }

    public void setNetwork(String network) {
        this.network = network;
    }

    public String getNetworkShow() {
        return StringUtil.isNullOrEmpty(networkShow) ? "" : networkShow;
    }

    public void setNetworkShow(String networkShow) {
        this.networkShow = networkShow;
    }

    public boolean isOpen() {
        return open;
    }

    public void setOpen(boolean open) {
        this.open = open;
    }

    public int getCategory() {
        return category;
    }

    public void setCategory(int category) {
        this.category = category;
    }

    public String getPaymentMethodName() {
        return StringUtil.isNullOrEmpty(paymentMethodName) ? "" : paymentMethodName;
    }

    public void setPaymentMethodName(String paymentMethodName) {
        this.paymentMethodName = paymentMethodName;
    }

    public String getPaymentMethodLog() {
        return StringUtil.isNullOrEmpty(paymentMethodLog) ? "" : paymentMethodLog;
    }

    public void setPaymentMethodLog(String paymentMethodLog) {
        this.paymentMethodLog = paymentMethodLog;
    }

    public int getSingleRechargeUpperLimit() {
        return singleRechargeUpperLimit;
    }

    public void setSingleRechargeUpperLimit(int singleRechargeUpperLimit) {
        this.singleRechargeUpperLimit = singleRechargeUpperLimit;
    }

    public int getSingleRechargeLowerLimit() {
        return singleRechargeLowerLimit;
    }

    public void setSingleRechargeLowerLimit(int singleRechargeLowerLimit) {
        this.singleRechargeLowerLimit = singleRechargeLowerLimit;
    }

    public List<String> getRechargeInfos() {
        return rechargeInfos;
    }

    public void setRechargeInfos(List<String> rechargeInfos) {
        this.rechargeInfos = rechargeInfos;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public long getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(long deleteTime) {
        this.deleteTime = deleteTime;
    }

    public Map<String, List<String>> getRechargeInfoMap() {
        return rechargeInfoMap;
    }

    public Map<Double, PayAmountInfo> getPayAmountInfoMap() {
        return payAmountInfoMap;
    }

    public static class RechargeInfo {
        public String name;
        public List<String> params = new ArrayList<>();

        public String getName() {
            return StringUtil.isNullOrEmpty(name) ? "" : name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public List<String> getParams() {
            return params;
        }

        public void setParams(List<String> params) {
            this.params = params;
        }
    }

    public static class PayAmountInfo {
        public double amount;
        public int tag; //1.hot

        public double getAmount() {
            return amount;
        }

        public void setAmount(double amount) {
            this.amount = amount;
        }

        public int getTag() {
            return tag;
        }

        public void setTag(int tag) {
            this.tag = tag;
        }
    }
}
