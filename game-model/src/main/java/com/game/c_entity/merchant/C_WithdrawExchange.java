package com.game.c_entity.merchant;

import com.game.po.data.IDataChecker;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "c_withdrawExchange")
public class C_WithdrawExchange implements IDataChecker {

    @Id
    private ObjectId _id;

    //商户
    private String business_no;

    //货币id
    private int currencyId;

    //兑换币种
    private int exchangeCurrency;

    //网络协议
    private String network;

    //是否开放
    private boolean open;

    //渠道
    private int channel;

    //比例
    private double rate;

    private long deleteTime;

    @Override
    public boolean check() throws Exception {
        return true;
    }

    public int getCurrencyId() {
        return currencyId;
    }

    public void setCurrencyId(int currencyId) {
        this.currencyId = currencyId;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public int getExchangeCurrency() {
        return exchangeCurrency;
    }

    public void setExchangeCurrency(int exchangeCurrency) {
        this.exchangeCurrency = exchangeCurrency;
    }

    public String getNetwork() {
        return network;
    }

    public void setNetwork(String network) {
        this.network = network;
    }

    public boolean isOpen() {
        return open;
    }

    public void setOpen(boolean open) {
        this.open = open;
    }

    public int getChannel() {
        return channel;
    }

    public void setChannel(int channel) {
        this.channel = channel;
    }

    public double getRate() {
        return rate;
    }

    public void setRate(double rate) {
        this.rate = rate;
    }

    public long getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(long deleteTime) {
        this.deleteTime = deleteTime;
    }
}
