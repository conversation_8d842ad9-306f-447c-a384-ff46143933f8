package com.game.c_entity.merchant;

import com.game.po.data.IDataChecker;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "c_agent")
public class C_Agent implements IDataChecker {

    @Id
    private ObjectId _id;

    private int c_id;

    //商户号
    private String business_no;

    //代理id
    private int proxyId;

    private String proxyName;

    //总代理
    private String generalAgent;

    //上级代理
    private String superiorAgent;

    //权重
    private int weight;

    public int getC_id() {
        return c_id;
    }

    public void setC_id(int c_id) {
        this.c_id = c_id;
    }

    public int getProxyId() {
        return proxyId;
    }

    public void setProxyId(int proxyId) {
        this.proxyId = proxyId;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public String getProxyName() {
        return proxyName;
    }

    public void setProxyName(String proxyName) {
        this.proxyName = proxyName;
    }

    public String getGeneralAgent() {
        return generalAgent;
    }

    public void setGeneralAgent(String generalAgent) {
        this.generalAgent = generalAgent;
    }

    public String getSuperiorAgent() {
        return superiorAgent;
    }

    public void setSuperiorAgent(String superiorAgent) {
        this.superiorAgent = superiorAgent;
    }

    public int getWeight() {
        return weight;
    }

    public void setWeight(int weight) {
        this.weight = weight;
    }
}
