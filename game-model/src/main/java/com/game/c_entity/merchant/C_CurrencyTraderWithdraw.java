package com.game.c_entity.merchant;

import com.game.po.data.IDataChecker;
import io.netty.util.internal.StringUtil;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "c_currencyTraderWithdraw")
public class C_CurrencyTraderWithdraw implements IDataChecker {

    @Id
    private ObjectId _id;

    //商户
    private String business_no;

    //币商id
    private int currencyTraderId;

    //提现手续费费率
    private double withdrawFeeRate;

    //单笔提现下限
    private int singleWithdrawLowerLimit;

    //单笔提现上限
    private int singleWithdrawUpperLimit;

    //1.币商 2.代理
    private int type;

    //头像
    private String head;

    //名字
    private String name;

    //社媒连接
    private String mediaLink;

    //社媒icon
    private String mediaIcon;

    //联系方式
    private String contactDetails;

    //提现次数
    private int withdrawTimes;

    //是否开放
    private boolean open;

    @Override
    public boolean check() throws Exception {
        return true;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public int getCurrencyTraderId() {
        return currencyTraderId;
    }

    public void setCurrencyTraderId(int currencyTraderId) {
        this.currencyTraderId = currencyTraderId;
    }

    public double getWithdrawFeeRate() {
        return withdrawFeeRate;
    }

    public void setWithdrawFeeRate(double withdrawFeeRate) {
        this.withdrawFeeRate = withdrawFeeRate;
    }

    public int getSingleWithdrawLowerLimit() {
        return singleWithdrawLowerLimit;
    }

    public void setSingleWithdrawLowerLimit(int singleWithdrawLowerLimit) {
        this.singleWithdrawLowerLimit = singleWithdrawLowerLimit;
    }

    public int getSingleWithdrawUpperLimit() {
        return singleWithdrawUpperLimit;
    }

    public void setSingleWithdrawUpperLimit(int singleWithdrawUpperLimit) {
        this.singleWithdrawUpperLimit = singleWithdrawUpperLimit;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getName() {
        return StringUtil.isNullOrEmpty(name) ? "" : name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHead() {
        return StringUtil.isNullOrEmpty(head) ? "" : head;
    }

    public void setHead(String head) {
        this.head = head;
    }

    public String getMediaLink() {
        return StringUtil.isNullOrEmpty(mediaLink) ? "" : mediaLink;
    }

    public void setMediaLink(String mediaLink) {
        this.mediaLink = mediaLink;
    }

    public String getMediaIcon() {
        return StringUtil.isNullOrEmpty(mediaIcon) ? "" : mediaIcon;
    }

    public void setMediaIcon(String mediaIcon) {
        this.mediaIcon = mediaIcon;
    }

    public String getContactDetails() {
        return StringUtil.isNullOrEmpty(contactDetails) ? "" : contactDetails;
    }

    public void setContactDetails(String contactDetails) {
        this.contactDetails = contactDetails;
    }

    public int getWithdrawTimes() {
        return withdrawTimes;
    }

    public void setWithdrawTimes(int withdrawTimes) {
        this.withdrawTimes = withdrawTimes;
    }

    public boolean isOpen() {
        return open;
    }

    public void setOpen(boolean open) {
        this.open = open;
    }

}
