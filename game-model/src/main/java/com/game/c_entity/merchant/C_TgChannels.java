package com.game.c_entity.merchant;

import com.game.po.data.IDataChecker;
import io.netty.util.internal.StringUtil;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.List;

@Document(collection = "c_tgChannels")
public class C_TgChannels implements IDataChecker {

    @Id
    private ObjectId _id;

    private int c_id;

    private String business_no;

    private String channelName;

    private String channelId;

    private List<Integer> languages = new ArrayList<>();

    private String icon;

    private String channelAddress;

    private String remarks;

    @Override
    public boolean check() throws Exception {
        return true;
    }

    public List<Integer> getLanguages() {
        return languages;
    }

    public void setLanguages(List<Integer> languages) {
        this.languages = languages;
    }

    public int getC_id() {
        return c_id;
    }

    public void setC_id(int c_id) {
        this.c_id = c_id;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public String getChannelName() {
        return StringUtil.isNullOrEmpty(channelName) ? "" : channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }

    public String getChannelId() {
        return StringUtil.isNullOrEmpty(channelId) ? "" : channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public String getIcon() {
        return StringUtil.isNullOrEmpty(icon) ? "" : icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getChannelAddress() {
        return StringUtil.isNullOrEmpty(channelAddress) ? "" : channelAddress;
    }

    public void setChannelAddress(String channelAddress) {
        this.channelAddress = channelAddress;
    }

    public String getRemarks() {
        return StringUtil.isNullOrEmpty(remarks) ? "" : remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }
}
