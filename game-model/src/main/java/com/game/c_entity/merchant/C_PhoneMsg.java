package com.game.c_entity.merchant;

import com.game.po.data.IDataChecker;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "c_phoneMsg")
public class C_PhoneMsg implements IDataChecker {

    @Id
    private ObjectId _id;

    //商户号
    private String business_no;

    //三方名字
    private String threePartyName;

    private String appId;

    private String apiUrl;

    private String apiKey;

    private String apiSecret;

    //内容
    private String content;

    private boolean open;

    private boolean baseOpen;

    private int sort;

    @Override
    public boolean check() throws Exception {
        return true;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getThreePartyName() {
        return threePartyName;
    }

    public void setThreePartyName(String threePartyName) {
        this.threePartyName = threePartyName;
    }

    public String getApiUrl() {
        return apiUrl;
    }

    public void setApiUrl(String apiUrl) {
        this.apiUrl = apiUrl;
    }

    public String getApiKey() {
        return apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    public String getApiSecret() {
        return apiSecret;
    }

    public void setApiSecret(String apiSecret) {
        this.apiSecret = apiSecret;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public boolean isOpen() {
        return open;
    }

    public void setOpen(boolean open) {
        this.open = open;
    }

    public boolean isBaseOpen() {
        return baseOpen;
    }

    public void setBaseOpen(boolean baseOpen) {
        this.baseOpen = baseOpen;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }
}
