package com.game.c_entity.merchant;

import com.game.po.data.IDataChecker;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.List;

@Document(collection = "c_tgNotify")
public class C_TgNotify implements IDataChecker {

    @Id
    private ObjectId _id;

    //商户
    private String business_no;

    private List<Integer> types =  new ArrayList<>();

    //推送名字
    private String tgName;

    //1.领取通知 2.定时通知 3.活动参与 4.活动信息
    private int notifyType;

    //周期
    private List<Integer> cycle = new ArrayList<>();

    //推送时间(毫秒)
    private long pushTime;

    private List<Integer> notifyTarget = new ArrayList<>();

    //跳转连接
    private String jumpLink;

    //tg连接
    private String tgLink;

    private int currencyId;

    private double amount;

    //奖励类型 1.cash 2.bonus
    private int rewardType;

    //打码
    private int turnoverMul;

    private String msgBanner;

    private String text;

    private boolean open;

    @Override
    public boolean check() throws Exception {
        return true;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public List<Integer> getTypes() {
        return types;
    }

    public void setTypes(List<Integer> types) {
        this.types = types;
    }

    public double getAmount() {
        return amount;
    }

    public void setAmount(double amount) {
        this.amount = amount;
    }

    public List<Integer> getNotifyTarget() {
        return notifyTarget;
    }

    public void setNotifyTarget(List<Integer> notifyTarget) {
        this.notifyTarget = notifyTarget;
    }

    public String getTgName() {
        return tgName;
    }

    public void setTgName(String tgName) {
        this.tgName = tgName;
    }

    public int getNotifyType() {
        return notifyType;
    }

    public void setNotifyType(int notifyType) {
        this.notifyType = notifyType;
    }

    public List<Integer> getCycle() {
        return cycle;
    }

    public void setCycle(List<Integer> cycle) {
        this.cycle = cycle;
    }

    public long getPushTime() {
        return pushTime;
    }

    public void setPushTime(long pushTime) {
        this.pushTime = pushTime;
    }

    public String getJumpLink() {
        return jumpLink;
    }

    public void setJumpLink(String jumpLink) {
        this.jumpLink = jumpLink;
    }

    public String getTgLink() {
        return tgLink;
    }

    public void setTgLink(String tgLink) {
        this.tgLink = tgLink;
    }

    public int getCurrencyId() {
        return currencyId;
    }

    public void setCurrencyId(int currencyId) {
        this.currencyId = currencyId;
    }

    public int getRewardType() {
        return rewardType;
    }

    public void setRewardType(int rewardType) {
        this.rewardType = rewardType;
    }

    public int getTurnoverMul() {
        return turnoverMul;
    }

    public void setTurnoverMul(int turnoverMul) {
        this.turnoverMul = turnoverMul;
    }

    public String getMsgBanner() {
        return msgBanner;
    }

    public void setMsgBanner(String msgBanner) {
        this.msgBanner = msgBanner;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public boolean isOpen() {
        return open;
    }

    public void setOpen(boolean open) {
        this.open = open;
    }
}
