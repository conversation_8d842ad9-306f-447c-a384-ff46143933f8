package com.game.c_entity.merchant;

import com.game.engine.utils.JsonUtils;
import com.game.po.data.IDataChecker;
import io.netty.util.internal.StringUtil;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Document(collection = "c_mobile")
public class C_Mobile implements IDataChecker {

    @Id
    private ObjectId _id;

    private int c_id;

    private String menuIcon;

    private int sort;

    private int isJump;

    //跳转类型 1.内连 2.外链
    private int jumpType;

    //弹框类型 1.任务 2.转盘 3.充值 4.客服
    private int popupLinks;

    //内部链接
    private String innerLinks;

    //外链接
    private String externalLinks;

    private List<String> ruleData = new ArrayList<>();

    private transient final Map<Integer, Rule> rulesMap = new HashMap<>();

    @Override
    public boolean check() throws Exception {
        for (String data : ruleData) {
            final Rule rule = JsonUtils.readFromJson(data, Rule.class);
            rulesMap.put(rule.language, rule);
        }
        return true;
    }

    public int getC_id() {
        return c_id;
    }

    public void setC_id(int c_id) {
        this.c_id = c_id;
    }

    public String getMenuIcon() {
        return StringUtil.isNullOrEmpty(menuIcon) ? "" : menuIcon;
    }

    public void setMenuIcon(String menuIcon) {
        this.menuIcon = menuIcon;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public int getIsJump() {
        return isJump;
    }

    public void setIsJump(int isJump) {
        this.isJump = isJump;
    }

    public int getJumpType() {
        return jumpType;
    }

    public void setJumpType(int jumpType) {
        this.jumpType = jumpType;
    }

    public int getPopupLinks() {
        return popupLinks;
    }

    public void setPopupLinks(int popupLinks) {
        this.popupLinks = popupLinks;
    }

    public String getInnerLinks() {
        return StringUtil.isNullOrEmpty(innerLinks) ? "" : innerLinks;
    }

    public void setInnerLinks(String innerLinks) {
        this.innerLinks = innerLinks;
    }

    public String getExternalLinks() {
        return StringUtil.isNullOrEmpty(externalLinks) ? "" : externalLinks;
    }

    public void setExternalLinks(String externalLinks) {
        this.externalLinks = externalLinks;
    }

    public List<String> getRuleData() {
        return ruleData;
    }

    public void setRuleData(List<String> ruleData) {
        this.ruleData = ruleData;
    }

    public Map<Integer, Rule> getRulesMap() {
        return rulesMap;
    }

    public static class Rule {
        private int language;

        private String menuName;

        public int getLanguage() {
            return language;
        }

        public void setLanguage(int language) {
            this.language = language;
        }

        public String getMenuName() {
            return StringUtil.isNullOrEmpty(menuName) ? "" : menuName;
        }

        public void setMenuName(String menuName) {
            this.menuName = menuName;
        }
    }
}
