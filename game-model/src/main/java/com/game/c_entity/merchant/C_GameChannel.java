package com.game.c_entity.merchant;

import com.game.engine.utils.JsonUtils;
import com.game.po.data.IDataChecker;
import io.netty.util.internal.StringUtil;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Document(collection = "c_gameChannel")
public class C_GameChannel implements IDataChecker {
    @Id
    private ObjectId _id;

    //商户
    private String business_no;

    //频道 1 自增
    private int channelId;

    //频道排序
    private int channelSort;

    //1.casino 2.sport 3.Lottery
    private int channelType;

    private String channelIcon;

    private String channelIcon1;

    //子频道 channel*100 自增
    private int subChannel;

    //子频道排序
    private int subChannelSort;

    //1.厂商 2.游戏 3.最近 4.收藏 5.推荐
    private int subChannelType;

    private String subChannelIcon;

    private String subChannelIcon1;

    //子频道内链
    private String subChannelInner;

    //平台
    private List<Integer> platformId = new ArrayList<>();

    //标签 1.热门 2.最新 3.推荐
    private List<Integer> tag = new ArrayList<>();

    //自定义标签
    private List<Integer> customTag = new ArrayList<>();

    //游戏子类型 1.jp 3.mega 2.buy in
    private List<Integer> subType = new ArrayList<>();

    //是否显示菜单界面
    private boolean menuShow;

    //首页内容
    private boolean homeContextShow;
    //首页内容排序
    private int homeContextSort;

    //首页菜单
    private boolean homeMenuShow;
    //首页菜单排序
    private int homeMenuSort;

    //频道首页
    private boolean channelHomeShow;
    //频道首页排序
    private int channelHomeSort;

    private String channelHomeIcon;

    private String channelHomeIcon1;

    //是否开启
    private boolean open;

    private List<String> subChannelData = new ArrayList<>();

    private List<String> descData = new ArrayList<>();

    private transient Map<Integer, DescInfo> descInfoMap = new HashMap<>();

    @Override
    public boolean check() throws Exception {
        if (!descData.isEmpty()) {
            for (String data : descData) {
                final DescInfo descInfo = JsonUtils.readFromJson(data, DescInfo.class);
                descInfoMap.put(descInfo.language, descInfo);
            }
        }
        return true;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public int getChannelId() {
        return channelId;
    }

    public void setChannelId(int channelId) {
        this.channelId = channelId;
    }

    public int getChannelType() {
        return channelType;
    }

    public void setChannelType(int channelType) {
        this.channelType = channelType;
    }

    public String getChannelIcon() {
        return StringUtil.isNullOrEmpty(channelIcon) ? "" : channelIcon;
    }

    public void setChannelIcon(String channelIcon) {
        this.channelIcon = channelIcon;
    }

    public String getChannelIcon1() {
        return StringUtil.isNullOrEmpty(channelIcon1) ? "" : channelIcon1;
    }

    public void setChannelIcon1(String channelIcon1) {
        this.channelIcon1 = channelIcon1;
    }

    public int getSubChannel() {
        return subChannel;
    }

    public void setSubChannel(int subChannel) {
        this.subChannel = subChannel;
    }

    public int getSubChannelType() {
        return subChannelType;
    }

    public void setSubChannelType(int subChannelType) {
        this.subChannelType = subChannelType;
    }

    public String getSubChannelIcon() {
        return StringUtil.isNullOrEmpty(subChannelIcon) ? "" : subChannelIcon;
    }

    public void setSubChannelIcon(String subChannelIcon) {
        this.subChannelIcon = subChannelIcon;
    }

    public String getSubChannelIcon1() {
        return StringUtil.isNullOrEmpty(subChannelIcon1) ? "" : subChannelIcon1;
    }

    public void setSubChannelIcon1(String subChannelIcon1) {
        this.subChannelIcon1 = subChannelIcon1;
    }

    public String getSubChannelInner() {
        return StringUtil.isNullOrEmpty(subChannelInner) ? "" : subChannelInner;
    }

    public void setSubChannelInner(String subChannelInner) {
        this.subChannelInner = subChannelInner;
    }

    public List<Integer> getPlatformId() {
        return platformId;
    }

    public void setPlatformId(List<Integer> platformId) {
        this.platformId = platformId;
    }

    public List<Integer> getTag() {
        return tag;
    }

    public void setTag(List<Integer> tag) {
        this.tag = tag;
    }

    public List<Integer> getCustomTag() {
        return customTag;
    }

    public void setCustomTag(List<Integer> customTag) {
        this.customTag = customTag;
    }

    public List<Integer> getSubType() {
        return subType;
    }

    public void setSubType(List<Integer> subType) {
        this.subType = subType;
    }

    public boolean isOpen() {
        return open;
    }

    public void setOpen(boolean open) {
        this.open = open;
    }

    public boolean isMenuShow() {
        return menuShow;
    }

    public void setMenuShow(boolean menuShow) {
        this.menuShow = menuShow;
    }

    public boolean isHomeContextShow() {
        return homeContextShow;
    }

    public void setHomeContextShow(boolean homeContextShow) {
        this.homeContextShow = homeContextShow;
    }

    public boolean isChannelHomeShow() {
        return channelHomeShow;
    }

    public void setChannelHomeShow(boolean channelHomeShow) {
        this.channelHomeShow = channelHomeShow;
    }

    public int getChannelSort() {
        return channelSort;
    }

    public void setChannelSort(int channelSort) {
        this.channelSort = channelSort;
    }

    public int getSubChannelSort() {
        return subChannelSort;
    }

    public void setSubChannelSort(int subChannelSort) {
        this.subChannelSort = subChannelSort;
    }

    public int getHomeContextSort() {
        return homeContextSort;
    }

    public void setHomeContextSort(int homeContextSort) {
        this.homeContextSort = homeContextSort;
    }

    public boolean isHomeMenuShow() {
        return homeMenuShow;
    }

    public void setHomeMenuShow(boolean homeMenuShow) {
        this.homeMenuShow = homeMenuShow;
    }

    public int getHomeMenuSort() {
        return homeMenuSort;
    }

    public void setHomeMenuSort(int homeMenuSort) {
        this.homeMenuSort = homeMenuSort;
    }

    public String getChannelHomeIcon() {
        return StringUtil.isNullOrEmpty(channelHomeIcon) ? "" : channelHomeIcon;
    }

    public void setChannelHomeIcon(String channelHomeIcon) {
        this.channelHomeIcon = channelHomeIcon;
    }

    public String getChannelHomeIcon1() {
        return StringUtil.isNullOrEmpty(channelHomeIcon1) ? "" : channelHomeIcon1;
    }

    public void setChannelHomeIcon1(String channelHomeIcon1) {
        this.channelHomeIcon1 = channelHomeIcon1;
    }

    public int getChannelHomeSort() {
        return channelHomeSort;
    }

    public void setChannelHomeSort(int channelHomeSort) {
        this.channelHomeSort = channelHomeSort;
    }

    public Map<Integer, DescInfo> getDescInfoMap() {
        return descInfoMap;
    }

    public void setDescInfoMap(Map<Integer, DescInfo> descInfoMap) {
        this.descInfoMap = descInfoMap;
    }

    public List<String> getSubChannelData() {
        return subChannelData;
    }

    public void setSubChannelData(List<String> subChannelData) {
        this.subChannelData = subChannelData;
    }

    public List<String> getDescData() {
        return descData;
    }

    public void setDescData(List<String> descData) {
        this.descData = descData;
    }

    public static class SubChannelInfo {
        public int subId;

        //子频道内容
        public String subChannelContext;

        //子频道内链
        public String subChannelInner;

        public int getSubId() {
            return subId;
        }

        public void setSubId(int subId) {
            this.subId = subId;
        }

        public String getSubChannelContext() {
            return StringUtil.isNullOrEmpty(subChannelContext) ? "" : subChannelContext;
        }

        public void setSubChannelContext(String subChannelContext) {
            this.subChannelContext = subChannelContext;
        }

        public String getSubChannelInner() {
            return StringUtil.isNullOrEmpty(subChannelInner) ? "" : subChannelInner;
        }

        public void setSubChannelInner(String subChannelInner) {
            this.subChannelInner = subChannelInner;
        }
    }

    public static class DescInfo {
        public int language;

        //频道名字
        public String channelName;

        //子频道名字
        public String subChannelName;

        public String getChannelName() {
            return StringUtil.isNullOrEmpty(channelName) ? "" : channelName;
        }

        public void setChannelName(String channelName) {
            this.channelName = channelName;
        }

        public String getSubChannelName() {
            return StringUtil.isNullOrEmpty(subChannelName) ? "" : subChannelName;
        }

        public void setSubChannelName(String subChannelName) {
            this.subChannelName = subChannelName;
        }
    }

}
