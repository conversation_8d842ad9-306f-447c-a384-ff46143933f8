package com.game.entity.game;

import com.game.engine.utils.TimeUtil;
import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

@AutoFields
@PersistentEntity
@SerializableClass
@Document(collection = "game_note")
public class GameNote {

    private String business_no;

    private long noteId;

    //回合Id
    private String roundId;

    //交易id
    private Set<String> transactionIds = new HashSet<>();

    private long playerId;

    private String headId;

    private String playerName;

    private int platformId;

    private int gameId;

    //游戏类型 101.原创 102.电子 103.视讯 201.体育
    private int gameType;

    //货币id
    private int currencyId;

    //bonus
    private boolean bonus;

    //进入游戏货币
    private int gameCurrencyId;

    //下注金额
    private double betAmount;

    //有效下注金额
    private double validBets;

    //usd
    private double usdValidBets;

    //赢分
    private double win;

    //之前余额
    private double beforeBalance;

    //余额
    private double balance;

    //1.未结算 2.结算 3.退款
    private int status;

    //创建时间
    private long createTime;

    private int activePlayerCount;

    private boolean sportClose;

    private String remark = "";

    private String betbySettleData = "";

    private Map<String, String> btiReserveMap = new HashMap<>();

    private Map<String, String> btiEventMap = new HashMap<>();

    private boolean btiCommit = false;

    private String btiPurchaseId;

    public GameNote() {
    }

    public GameNote(long noteId) {
        this.noteId = noteId;
        this.createTime = TimeUtil.currentTimeMillis();
    }

    public long getNoteId() {
        return noteId;
    }

    public void setNoteId(long noteId) {
        this.noteId = noteId;
    }

    public Set<String> getTransactionIds() {
        return transactionIds;
    }

    public void setTransactionIds(Set<String> transactionIds) {
        this.transactionIds = transactionIds;
    }

    public String getRoundId() {
        return roundId;
    }

    public void setRoundId(String roundId) {
        this.roundId = roundId;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(long playerId) {
        this.playerId = playerId;
    }

    public String getHeadId() {
        return headId;
    }

    public void setHeadId(String headId) {
        this.headId = headId;
    }

    public String getPlayerName() {
        return playerName;
    }

    public void setPlayerName(String playerName) {
        this.playerName = playerName;
    }

    public int getPlatformId() {
        return platformId;
    }

    public void setPlatformId(int platformId) {
        this.platformId = platformId;
    }

    public int getGameId() {
        return gameId;
    }

    public void setGameId(int gameId) {
        this.gameId = gameId;
    }

    public int getGameType() {
        return gameType;
    }

    public void setGameType(int gameType) {
        this.gameType = gameType;
    }

    public int getCurrencyId() {
        return currencyId;
    }

    public void setCurrencyId(int currencyId) {
        this.currencyId = currencyId;
    }

    public boolean isBonus() {
        return bonus;
    }

    public void setBonus(boolean bonus) {
        this.bonus = bonus;
    }

    public int getGameCurrencyId() {
        return gameCurrencyId;
    }

    public void setGameCurrencyId(int gameCurrencyId) {
        this.gameCurrencyId = gameCurrencyId;
    }

    public double getBetAmount() {
        return betAmount;
    }

    public void setBetAmount(double betAmount) {
        this.betAmount = betAmount;
    }

    public double getValidBets() {
        return validBets;
    }

    public void setValidBets(double validBets) {
        this.validBets = validBets;
    }

    public double getUsdValidBets() {
        return usdValidBets;
    }

    public void setUsdValidBets(double usdValidBets) {
        this.usdValidBets = usdValidBets;
    }

    public double getWin() {
        return win;
    }

    public void setWin(double win) {
        this.win = win;
    }

    public double getBeforeBalance() {
        return beforeBalance;
    }

    public void setBeforeBalance(double beforeBalance) {
        this.beforeBalance = beforeBalance;
    }

    public double getBalance() {
        return balance;
    }

    public void setBalance(double balance) {
        this.balance = balance;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public int getActivePlayerCount() {
        return activePlayerCount;
    }

    public void setActivePlayerCount(int activePlayerCount) {
        this.activePlayerCount = activePlayerCount;
    }

    public boolean isSportClose() {
        return sportClose;
    }

    public void setSportClose(boolean sportClose) {
        this.sportClose = sportClose;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getBetbySettleData() {
        return betbySettleData;
    }

    public void setBetbySettleData(String betbySettleData) {
        this.betbySettleData = betbySettleData;
    }

    public Map<String, String> getBtiReserveMap() { return btiReserveMap; }

    public void setBtiReserveMap(Map<String, String> btiReserveMap) { this.btiReserveMap = btiReserveMap; }

    public void putBtiReserveMap(String reqId, String amount) { btiReserveMap.put(reqId, amount); }

    public Map<String, String> getBtiEventMap() { return btiEventMap; }

    public void setBtiEventMap(Map<String, String> btiEventMap) { this.btiEventMap = btiEventMap; }

    public void putBtiEventMap(String reqId, String eventData) { btiEventMap.put(reqId, eventData); }

    public boolean isBtiCommit() { return btiCommit; }

    public void setBtiCommit(boolean btiCommit) { this.btiCommit = btiCommit; }

    public String getBtiPurchaseId() { return btiPurchaseId; }

    public void setBtiPurchaseId(String btiPurchaseId) { this.btiPurchaseId = btiPurchaseId; }
}
