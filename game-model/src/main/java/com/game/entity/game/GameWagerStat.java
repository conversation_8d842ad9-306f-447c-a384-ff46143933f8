package com.game.entity.game;

import com.game.engine.math.BigDecimalUtils;

/**
 * Casino_Original(101, "原创"),
 * Casino_Slots(102, "电子"),
 * Casino_Live(103, "赌场视讯"),
 * Casino_Poker(104, "棋牌牌桌"),
 * Casino_Fish(105, "捕鱼"),
 * Casino_Arcade(106, "街机"),
 * Casino_Bingo(107, "宾果"),
 */
public class GameWagerStat {
    private double totalWager;
    private double totalWager3rdParty;
    private double totalWagerOriginal;
    private double totalWagerSlots;
    private double totalWagerLive;
    private double totalWagerPoker;
    private double totalWagerFish;
    private double totalWagerArcade;
    private double totalWagerBingo;

    private double totalWagerSports;

    private int totalActivePlayerCount;

    public double getTotalWager() {
        return totalWager;
    }

    public GameWagerStat setTotalWager(double totalWager) {
        this.totalWager = totalWager;
        return this;
    }

    public double getTotalWagerOriginal() {
        return totalWagerOriginal;
    }

    public GameWagerStat setTotalWagerOriginal(double totalWagerOriginal) {
        this.totalWagerOriginal = totalWagerOriginal;
        return this;
    }

    public double getTotalWagerSlots() {
        return totalWagerSlots;
    }

    public GameWagerStat setTotalWagerSlots(double totalWagerSlots) {
        this.totalWagerSlots = totalWagerSlots;
        return this;
    }

    public double getTotalWagerLive() {
        return totalWagerLive;
    }

    public GameWagerStat setTotalWagerLive(double totalWagerLive) {
        this.totalWagerLive = totalWagerLive;
        return this;
    }

    public double getTotalWagerPoker() {
        return totalWagerPoker;
    }

    public GameWagerStat setTotalWagerPoker(double totalWagerPoker) {
        this.totalWagerPoker = totalWagerPoker;
        return this;
    }

    public double getTotalWagerFish() {
        return totalWagerFish;
    }

    public GameWagerStat setTotalWagerFish(double totalWagerFish) {
        this.totalWagerFish = totalWagerFish;
        return this;
    }

    public double getTotalWagerArcade() {
        return totalWagerArcade;
    }

    public GameWagerStat setTotalWagerArcade(double totalWagerArcade) {
        this.totalWagerArcade = totalWagerArcade;
        return this;
    }

    public double getTotalWagerBingo() {
        return totalWagerBingo;
    }

    public GameWagerStat setTotalWagerBingo(double totalWagerBingo) {
        this.totalWagerBingo = totalWagerBingo;
        return this;
    }


    public double getTotalWagerSports() {
        return totalWagerSports;
    }

    public GameWagerStat setTotalWagerSports(double totalWagerSports) {
        this.totalWagerSports = totalWagerSports;
        return this;
    }

    public int getTotalActivePlayerCount() {
        return totalActivePlayerCount;
    }

    public GameWagerStat setTotalActivePlayerCount(int totalActivePlayerCount) {
        this.totalActivePlayerCount = totalActivePlayerCount;
        return this;
    }

    public double getTotalWager3rdParty() {
        final double value = BigDecimalUtils.add(this.totalWagerOriginal, this.totalWagerSlots, 9);
        final double value1 = BigDecimalUtils.add(this.totalWagerLive, this.totalWagerPoker, 9);
        final double value2 = BigDecimalUtils.add(this.totalWagerFish, this.totalWagerArcade, 9);
        final double value3 = BigDecimalUtils.add(this.totalWagerBingo, this.totalWagerSports, 9);
        return BigDecimalUtils.add(
                BigDecimalUtils.add(value, value1, 9)
                , BigDecimalUtils.add(value2, value3, 9)
                , 9);
    }

    public void setTotalWager3rdParty(double totalWager3rdParty) {
        this.totalWager3rdParty = totalWager3rdParty;
    }

}
