package com.game.entity.account.wallet;

import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;
import org.springframework.data.mongodb.core.mapping.Field;

@AutoFields
@PersistentEntity
@SerializableClass
public class Web3Wallet {

    @Field("wallet_address")
    private String walletAddress;  // 钱包地址

    @Field("chain_namespace")
    private String chainNamespace; // 链命名空间（如 Ethereum, Binance）

    @Field("chain_id")
    private int chainId;           // 链ID

    @Field("is_authorized")
    private boolean isAuthorized;  // 是否授权

    @Field("target_address")
    private String targetAddress;  // 授权目标地址

    // 无参构造函数
    public Web3Wallet() {
    }

    // 构造方法
    public Web3Wallet(String walletAddress, String chainNamespace, int chainId, boolean isAuthorized, String targetAddress) {
        this.walletAddress = walletAddress;
        this.chainNamespace = chainNamespace;
        this.chainId = chainId;
        this.isAuthorized = isAuthorized;
        this.targetAddress = targetAddress;
    }

    // Getter and Setter
    public String getWalletAddress() {
        return walletAddress;
    }

    public void setWalletAddress(String walletAddress) {
        this.walletAddress = walletAddress;
    }

    public String getChainNamespace() {
        return chainNamespace;
    }

    public void setChainNamespace(String chainNamespace) {
        this.chainNamespace = chainNamespace;
    }

    public int getChainId() {
        return chainId;
    }

    public void setChainId(int chainId) {
        this.chainId = chainId;
    }

    public boolean isAuthorized() {
        return isAuthorized;
    }

    public void setAuthorized(boolean authorized) {
        isAuthorized = authorized;
    }

    public String getTargetAddress() {
        return targetAddress;
    }

    public void setTargetAddress(String targetAddress) {
        this.targetAddress = targetAddress;
    }
}
