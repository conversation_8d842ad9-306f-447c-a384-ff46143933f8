package com.game.entity.account;

import com.game.engine.utils.TimeUtil;
import com.game.entity.account.email.Email;
import com.game.entity.account.phone.Phone;
import com.game.entity.account.wallet.Web3Wallet;
import com.game.redis.RedisUtils;
import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.*;

@AutoFields
@PersistentEntity
@SerializableClass
@Document(collection = "account")
public class Account {

    private static final Logger LOGGER = LoggerFactory.getLogger(Account.class);

    @Id
    private long _id;
    //商户
    private String business_no;
    //账号Id
    private long accountId;
    //创建时间
    private long createTime;
    //注册ip
    private String registerIp;
    //推荐人码
    private String referralCode;
    //域名
    private String host;
    //代理id
    private int agentId;
    //渠道id
    private int channelId;
    //媒体id
    private int mediaId;
    //广告id
    private int adId;
    //币商
    private int currencyTraderId;
    //活动
    private String activity;
    //登录次数
    private long loginTimes;
    //当天日期
    private String date;
    //今日登录次数
    private int dailyLoginTimes;
    //邮件订阅
    private boolean emailSubscribe;

    //地区
    private String region;

    //邮件信息
    private Email emailInfo = new Email();
    //电话
    private Phone phoneInfo = new Phone();
    //账号
    private String account;
    private String password;

    //fb
    private String pixelId;
    private String fbToken;

    private String clickId;
    private String KWaiPixelId;
    private String KWaiToken;

    private Web3Wallet wallet;

    //三方信息
    private Map<Integer, ThreePartyInfo> threePartyInfoMap = new LinkedHashMap<>();

    public Account(long accountId) {
        this._id = accountId;
        this.accountId = accountId;
        this.createTime = TimeUtil.currentTimeMillis();
    }

    public Account() {
    }

    public long get_id() {
        return _id;
    }

    public void set_id(long _id) {
        this._id = _id;
    }

    public long getAccountId() {
        return accountId;
    }

    public void setAccountId(long accountId) {
        this.accountId = accountId;
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public String getRegisterIp() {
        return registerIp;
    }

    public void setRegisterIp(String registerIp) {
        this.registerIp = registerIp;
    }

    public String getReferralCode() {
        return referralCode;
    }

    public void setReferralCode(String referralCode) {
        this.referralCode = referralCode;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public int getAgentId() {
        return agentId;
    }

    public void setAgentId(int agentId) {
        this.agentId = agentId;
    }

    public int getChannelId() {
        return channelId;
    }

    public void setChannelId(int channelId) {
        this.channelId = channelId;
    }

    public int getMediaId() {
        return mediaId;
    }

    public void setMediaId(int mediaId) {
        this.mediaId = mediaId;
    }

    public int getAdId() {
        return adId;
    }

    public void setAdId(int adId) {
        this.adId = adId;
    }

    public int getCurrencyTraderId() {
        return currencyTraderId;
    }

    public void setCurrencyTraderId(int currencyTraderId) {
        this.currencyTraderId = currencyTraderId;
    }

    public String getActivity() {
        return activity;
    }

    public void setActivity(String activity) {
        this.activity = activity;
    }

    public long getLoginTimes() {
        return loginTimes;
    }

    public void setLoginTimes(long loginTimes) {
        this.loginTimes = loginTimes;
        RedisUtils.saveToRedis(this.accountId, AccountFields.loginTimes, String.valueOf(loginTimes));
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    public int getDailyLoginTimes() {
        return dailyLoginTimes;
    }

    public void setDailyLoginTimes(int dailyLoginTimes) {
        this.dailyLoginTimes = dailyLoginTimes;
    }

    public boolean isEmailSubscribe() {
        return emailSubscribe;
    }

    public void setEmailSubscribe(boolean emailSubscribe) {
        this.emailSubscribe = emailSubscribe;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public Email getEmailInfo() {
        return emailInfo;
    }

    public void setEmailInfo(Email emailInfo) {
        this.emailInfo = emailInfo;
    }

    public Phone getPhoneInfo() {
        return phoneInfo;
    }

    public void setPhoneInfo(Phone phoneInfo) {
        this.phoneInfo = phoneInfo;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getFbToken() {
        return fbToken;
    }

    public void setFbToken(String fbToken) {
        this.fbToken = fbToken;
    }

    public String getPixelId() {
        return pixelId;
    }

    public void setPixelId(String pixelId) {
        this.pixelId = pixelId;
    }

    public String getClickId() {
        return clickId;
    }

    public void setClickId(String clickId) {
        this.clickId = clickId;
    }

    public String getKWaiPixelId() {
        return KWaiPixelId;
    }

    public void setKWaiPixelId(String KWaiPixelId) {
        this.KWaiPixelId = KWaiPixelId;
    }

    public String getKWaiToken() {
        return KWaiToken;
    }

    public void setKWaiToken(String KWaiToken) {
        this.KWaiToken = KWaiToken;
    }

    public Map<Integer, ThreePartyInfo> getThreePartyInfoMap() {
        return threePartyInfoMap;
    }

    public void setThreePartyInfoMap(Map<Integer, ThreePartyInfo> threePartyInfoMap) {
        this.threePartyInfoMap = threePartyInfoMap;
    }

    public void addThreePartyInfo(int threeParty, String account, String threePartyId) {
        final ThreePartyInfo threePartyInfo = new ThreePartyInfo();
        threePartyInfo.setThreeParty(threeParty);
        threePartyInfo.setAccount(account);
        threePartyInfo.setThreePartyId(threePartyId);
        this.threePartyInfoMap.put(threeParty, threePartyInfo);
    }

    public void removeThreePartyInfo(int threeParty) {
        this.threePartyInfoMap.remove(threeParty);
    }

    // Getter and Setter for web3WalletInfo
    public Web3Wallet getWallet() {
        return wallet;
    }

    public void setWallet(Web3Wallet wallet) {
        this.wallet = wallet;
    }
}
