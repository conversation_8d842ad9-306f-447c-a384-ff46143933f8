package com.game.entity.order;

import com.game.engine.utils.TimeUtil;
import org.springframework.data.annotation.Transient;

public abstract class Order {

    private String business_no;

    private long orderId;

    //1.recharge 2.withdraw
    private int type;

    private int paymentMethod;

    private int currencyId;

    private int hallId;

    private int currencyTraderId;

    //金额
    private double amounts;

    private long playerId;

    //创建时间
    private long createTime;

    //1.complete 2.processing 3.failed 4.canceled
    private int status;

    //支付结束时间
    private long payEndTime;

    //fb
    private String event_name;
    private String pixelId;
    private String fbToken;

    private long expiredTime;

    Order() {
    }

    Order(long orderId) {
        this.orderId = orderId;
        this.createTime = TimeUtil.currentTimeMillis();
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public long getOrderId() {
        return orderId;
    }

    public void setOrderId(long orderId) {
        this.orderId = orderId;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(int paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public int getCurrencyId() {
        return currencyId;
    }

    public void setCurrencyId(int currencyId) {
        this.currencyId = currencyId;
    }

    public int getHallId() {
        return hallId;
    }

    public void setHallId(int hallId) {
        this.hallId = hallId;
    }

    public int getCurrencyTraderId() {
        return currencyTraderId;
    }

    public void setCurrencyTraderId(int currencyTraderId) {
        this.currencyTraderId = currencyTraderId;
    }

    public double getAmounts() {
        return amounts;
    }

    public void setAmounts(double amounts) {
        this.amounts = amounts;
    }

    public long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(long playerId) {
        this.playerId = playerId;
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public long getPayEndTime() {
        return payEndTime;
    }

    public void setPayEndTime(long payEndTime) {
        this.payEndTime = payEndTime;
    }

    public String getPixelId() {
        return pixelId;
    }

    public void setPixelId(String pixelId) {
        this.pixelId = pixelId;
    }

    public String getFbToken() {
        return fbToken;
    }

    public void setFbToken(String fbToken) {
        this.fbToken = fbToken;
    }

    public String getEvent_name() {
        return event_name;
    }

    public void setEvent_name(String event_name) {
        this.event_name = event_name;
    }

    public long getExpiredTime() {
        return expiredTime;
    }

    public void setExpiredTime(long expiredTime) {
        this.expiredTime = expiredTime;
    }
}
