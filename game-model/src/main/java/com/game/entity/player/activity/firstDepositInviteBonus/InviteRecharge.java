package com.game.entity.player.activity.firstDepositInviteBonus;

import com.game.engine.math.BigDecimalUtils;
import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

@AutoFields
@PersistentEntity
@SerializableClass
public class InviteRecharge {

    private boolean finish;

    private long playerId;

    private int currencyId;

    private int vipLevel;

    private long firstDepositTime;

    private Map<Integer, Double> wageredMap = new HashMap<>();

    private Map<Integer, Double> chargeMap = new HashMap<>();

    private Set<Integer> receiveBonus = new HashSet<>();

    public boolean isFinish() {
        return finish;
    }

    public void setFinish(boolean finish) {
        this.finish = finish;
    }

    public long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(long playerId) {
        this.playerId = playerId;
    }

    public int getCurrencyId() {
        return currencyId;
    }

    public void setCurrencyId(int currencyId) {
        this.currencyId = currencyId;
    }

    public int getVipLevel() {
        return vipLevel;
    }

    public void setVipLevel(int vipLevel) {
        this.vipLevel = vipLevel;
    }

    public long getFirstDepositTime() {
        return firstDepositTime;
    }

    public void setFirstDepositTime(long firstDepositTime) {
        this.firstDepositTime = firstDepositTime;
    }

    public Map<Integer, Double> getChargeMap() {
        return chargeMap;
    }

    public void setChargeMap(Map<Integer, Double> chargeMap) {
        this.chargeMap = chargeMap;
    }

    public Map<Integer, Double> getWageredMap() {
        return wageredMap;
    }

    public void setWageredMap(Map<Integer, Double> wageredMap) {
        this.wageredMap = wageredMap;
    }

    public Set<Integer> getReceiveBonus() {
        return receiveBonus;
    }

    public void setReceiveBonus(Set<Integer> receiveBonus) {
        this.receiveBonus = receiveBonus;
    }

    public void incCharge(int currencyId, double charge) {
        double amount = this.chargeMap.getOrDefault(currencyId, 0.0);
        amount = BigDecimalUtils.add(amount, charge, 9);
        this.chargeMap.put(currencyId, amount);
    }

    public void incWagered(int currencyId, double wagered) {
        double amount = this.wageredMap.getOrDefault(currencyId, 0.0);
        amount = BigDecimalUtils.add(amount, wagered, 9);
        this.wageredMap.put(currencyId, amount);
    }
}
