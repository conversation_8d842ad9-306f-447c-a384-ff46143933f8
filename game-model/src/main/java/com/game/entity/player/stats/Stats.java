package com.game.entity.player.stats;

import com.game.engine.math.BigDecimalUtils;
import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;

@AutoFields
@PersistentEntity
@SerializableClass
public class Stats {
    //首充金额
    private double firstRechargeAmount;

    //充值次数
    private int totalRechargeTimes;

    //充值金额
    private double totalRechargeAmount;

    //提现次数
    private int totalWithdrawTimes;
    private int totalCurrencyTraderWithdrawTimes;

    //提现金额
    private double totalWithdrawAmount;
    private double totalCurrencyTraderWithdrawAmount;

    //投注次数
    private long totalBetTimes;

    //投注金额
    private double totalBetAmount;

    //有效投注金额
    private double totalValidBetAmount;

    //总赢分
    private double totalWin;

    //赢次数
    private long totalWinTimes;

    //今日充值次数
    private int dailyRechargeTimes;

    //今日充值金额
    private double dailyRechargeAmount;

    //周充值次数
    private int weeklyRechargeTimes;

    //周充值金额
    private double weeklyRechargeAmount;

    //今日提现次数
    private int dailyWithdrawTimes;
    private int dailyCurrencyTraderWithdrawTimes;

    //今日提现金额
    private double dailyWithdrawAmount;
    private double dailyCurrencyTraderWithdrawAmount;

    //今日投注次数
    private int dailyBetTimes;

    //今日投注金额
    private double dailyBetAmount;

    //今日赢分
    private double dailyWin;

    //系统赠送
    private double systemGift;

    public double getFirstRechargeAmount() {
        return firstRechargeAmount;
    }

    public void setFirstRechargeAmount(double firstRechargeAmount) {
        this.firstRechargeAmount = firstRechargeAmount;
    }

    public int getTotalRechargeTimes() {
        return totalRechargeTimes;
    }

    public void setTotalRechargeTimes(int totalRechargeTimes) {
        this.totalRechargeTimes = totalRechargeTimes;
    }

    public double getTotalRechargeAmount() {
        return totalRechargeAmount;
    }

    public void setTotalRechargeAmount(double totalRechargeAmount) {
        this.totalRechargeAmount = totalRechargeAmount;
    }

    public int getTotalWithdrawTimes() {
        return totalWithdrawTimes;
    }

    public void setTotalWithdrawTimes(int totalWithdrawTimes) {
        this.totalWithdrawTimes = totalWithdrawTimes;
    }

    public double getTotalWithdrawAmount() {
        return totalWithdrawAmount;
    }

    public void setTotalWithdrawAmount(double totalWithdrawAmount) {
        this.totalWithdrawAmount = totalWithdrawAmount;
    }

    public long getTotalBetTimes() {
        return totalBetTimes;
    }

    public void setTotalBetTimes(long totalBetTimes) {
        this.totalBetTimes = totalBetTimes;
    }

    public double getTotalBetAmount() {
        return totalBetAmount;
    }

    public void setTotalBetAmount(double totalBetAmount) {
        this.totalBetAmount = totalBetAmount;
    }

    public double getTotalValidBetAmount() {
        return totalValidBetAmount;
    }

    public void setTotalValidBetAmount(double totalValidBetAmount) {
        this.totalValidBetAmount = totalValidBetAmount;
    }

    public double getTotalWin() {
        return totalWin;
    }

    public void setTotalWin(double totalWin) {
        this.totalWin = totalWin;
    }

    public long getTotalWinTimes() {
        return totalWinTimes;
    }

    public void setTotalWinTimes(long totalWinTimes) {
        this.totalWinTimes = totalWinTimes;
    }

    public int getDailyRechargeTimes() {
        return dailyRechargeTimes;
    }

    public void setDailyRechargeTimes(int dailyRechargeTimes) {
        this.dailyRechargeTimes = dailyRechargeTimes;
    }

    public double getDailyRechargeAmount() {
        return dailyRechargeAmount;
    }

    public void setDailyRechargeAmount(double dailyRechargeAmount) {
        this.dailyRechargeAmount = dailyRechargeAmount;
    }

    public int getWeeklyRechargeTimes() {
        return weeklyRechargeTimes;
    }

    public void setWeeklyRechargeTimes(int weeklyRechargeTimes) {
        this.weeklyRechargeTimes = weeklyRechargeTimes;
    }

    public double getWeeklyRechargeAmount() {
        return weeklyRechargeAmount;
    }

    public void setWeeklyRechargeAmount(double weeklyRechargeAmount) {
        this.weeklyRechargeAmount = weeklyRechargeAmount;
    }

    public int getDailyWithdrawTimes() {
        return dailyWithdrawTimes;
    }

    public void setDailyWithdrawTimes(int dailyWithdrawTimes) {
        this.dailyWithdrawTimes = dailyWithdrawTimes;
    }

    public double getDailyWithdrawAmount() {
        return dailyWithdrawAmount;
    }

    public void setDailyWithdrawAmount(double dailyWithdrawAmount) {
        this.dailyWithdrawAmount = dailyWithdrawAmount;
    }

    public int getDailyBetTimes() {
        return dailyBetTimes;
    }

    public void setDailyBetTimes(int dailyBetTimes) {
        this.dailyBetTimes = dailyBetTimes;
    }

    public double getDailyBetAmount() {
        return dailyBetAmount;
    }

    public void setDailyBetAmount(double dailyBetAmount) {
        this.dailyBetAmount = dailyBetAmount;
    }

    public double getDailyWin() {
        return dailyWin;
    }

    public void setDailyWin(double dailyWin) {
        this.dailyWin = dailyWin;
    }

    public double getSystemGift() {
        return systemGift;
    }

    public void setSystemGift(double systemGift) {
        this.systemGift = systemGift;
    }

    public int getTotalCurrencyTraderWithdrawTimes() {
        return totalCurrencyTraderWithdrawTimes;
    }

    public void setTotalCurrencyTraderWithdrawTimes(int totalCurrencyTraderWithdrawTimes) {
        this.totalCurrencyTraderWithdrawTimes = totalCurrencyTraderWithdrawTimes;
    }

    public double getTotalCurrencyTraderWithdrawAmount() {
        return totalCurrencyTraderWithdrawAmount;
    }

    public void setTotalCurrencyTraderWithdrawAmount(double totalCurrencyTraderWithdrawAmount) {
        this.totalCurrencyTraderWithdrawAmount = totalCurrencyTraderWithdrawAmount;
    }

    public int getDailyCurrencyTraderWithdrawTimes() {
        return dailyCurrencyTraderWithdrawTimes;
    }

    public void setDailyCurrencyTraderWithdrawTimes(int dailyCurrencyTraderWithdrawTimes) {
        this.dailyCurrencyTraderWithdrawTimes = dailyCurrencyTraderWithdrawTimes;
    }

    public double getDailyCurrencyTraderWithdrawAmount() {
        return dailyCurrencyTraderWithdrawAmount;
    }

    public void setDailyCurrencyTraderWithdrawAmount(double dailyCurrencyTraderWithdrawAmount) {
        this.dailyCurrencyTraderWithdrawAmount = dailyCurrencyTraderWithdrawAmount;
    }

    public void incRechargeAmount(double amount) {
        this.totalRechargeTimes++;
        this.totalRechargeAmount = BigDecimalUtils.add(this.totalRechargeAmount, amount, 9);
        this.dailyRechargeTimes++;
        this.dailyRechargeAmount = BigDecimalUtils.add(this.dailyRechargeAmount, amount, 9);
        this.weeklyRechargeTimes++;
        this.weeklyRechargeAmount = BigDecimalUtils.add(this.weeklyRechargeAmount, amount, 9);
        if (this.totalRechargeTimes == 1) {
            this.firstRechargeAmount = amount;
        }
    }

    public void incWithdrawAmount(double amount) {
        this.totalWithdrawTimes++;
        this.totalWithdrawAmount = BigDecimalUtils.add(this.totalWithdrawAmount, amount, 9);
        this.dailyWithdrawTimes++;
        this.dailyWithdrawAmount = BigDecimalUtils.add(this.dailyWithdrawAmount, amount, 9);
    }

    public void incCurrencyTraderWithdrawAmount(double amount) {
        this.totalCurrencyTraderWithdrawTimes++;
        this.totalCurrencyTraderWithdrawAmount = BigDecimalUtils.add(this.totalCurrencyTraderWithdrawAmount, amount, 9);
        this.dailyCurrencyTraderWithdrawTimes++;
        this.dailyCurrencyTraderWithdrawAmount = BigDecimalUtils.add(this.dailyCurrencyTraderWithdrawAmount, amount, 9);
    }

    public void incBetAmount(double amount) {
        this.totalBetTimes++;
        this.totalBetAmount = BigDecimalUtils.add(this.totalBetAmount, amount, 9);
        this.dailyBetTimes++;
        this.dailyBetAmount = BigDecimalUtils.add(this.dailyBetAmount, amount, 9);
    }

    public void incValidBetAmount(double validAmount) {
        this.totalValidBetAmount = BigDecimalUtils.add(this.totalValidBetAmount, validAmount, 9);
    }

    public void incWin(double win) {
        this.totalWin = BigDecimalUtils.add(this.totalWin, win, 9);
        this.dailyWin = BigDecimalUtils.add(this.dailyWin, win, 9);
    }

    public void incTotalWinTimes() {
        this.totalWinTimes++;
    }

    public void incSystemGift(double systemGift) {
        this.systemGift = BigDecimalUtils.add(this.systemGift, systemGift, 9);
    }

    public void resetDaily() {
        this.dailyRechargeTimes = 0;
        this.dailyRechargeAmount = 0;
        this.dailyWithdrawTimes = 0;
        this.dailyWithdrawAmount = 0;
        this.dailyBetTimes = 0;
        this.dailyBetAmount = 0;
        this.dailyWin = 0;
    }

    public void resetWeekly() {
        this.weeklyRechargeTimes = 0;
        this.weeklyRechargeAmount = 0;
    }
}
