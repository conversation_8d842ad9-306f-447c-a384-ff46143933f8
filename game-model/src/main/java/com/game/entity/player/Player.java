package com.game.entity.player;

import com.game.dao.player.PlayerPromoteDao;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.mongo.ShardEntity;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.account.ThreePartyInfo;
import com.game.entity.game.GameNote;
import com.game.entity.player.activity.ActivityInfo;
import com.game.entity.player.activity.continuousDeposit.ContinuousDeposit;
import com.game.entity.player.activity.firstChargeSignIn.FirstChargeSignInInfo;
import com.game.entity.player.activity.firstDepositInviteBonus.FirstDepositInviteBonusInfo;
import com.game.entity.player.activity.mysteryBonus.MysteryBonusInfo;
import com.game.entity.player.activity.piggyBank.PiggyBankInfo;
import com.game.entity.player.activity.rechargeRecover.RechargeRecoverInfo;
import com.game.entity.player.activity.redEnvelopeRain.RedEnvelopeRainInfo;
import com.game.entity.player.activity.dailyContest.DailyContestInfo;
import com.game.entity.player.activity.rewardBox.RewardBoxInfo;
import com.game.entity.player.activity.wageredRebates.WageredRebatesInfo;
import com.game.entity.player.activity.weeklyRaffle.WeeklyRaffleInfo;
import com.game.entity.player.activity.luckSpin.LuckSpinInfo;
import com.game.entity.player.bonus.BonusInfo;
import com.game.entity.player.promote.PlayerPromote;
import com.game.entity.player.inbox.InboxInfo;
import com.game.entity.player.quest.QuestGoalComponent;
import com.game.entity.player.quest.QuestInfo;
import com.game.entity.player.stats.Stats;
import com.game.entity.player.stats.StatsInfo;
import com.game.entity.player.vip.VipClub;
import com.game.enums.redis.RedisHall;
import com.game.manager.EntityDaoMrg;
import com.game.redis.RedisUtils;
import com.google.protobuf.Message;
import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;
import io.netty.channel.Channel;
import io.netty.util.internal.StringUtil;
import it.unimi.dsi.fastutil.ints.IntList;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.*;

@AutoFields
@PersistentEntity
@SerializableClass
@Document(collection = "player")
public class Player extends ShardEntity {
    private static final Logger LOGGER = LoggerFactory.getLogger(Player.class);

    //<editor-fold defaultstate="collapsed" desc="创建角色初始数据">
    @Id
    private long _id;
    //玩家唯一id
    private long playerId;
    //域名
    private String host;
    //商户号
    private String business_no;
    //玩家名字
    private String playerName;
    //创建时间
    private long createTime;
    //机器人
    private boolean isRobot;
    //状态 1.正常 2.封号 3.冻结
    private int state;
    //地区
    private String registerRegion;
    //注册ip
    private String registerIp;
    //语言
    private int language;
    //邀请码
    private String invitationCode;
    //代理id
    private int agentId;
    //渠道id
    private int channelId;
    //媒体id
    private int mediaId;
    //广告id
    private int adId;
    //币商
    private int currencyTraderId;
    //时区
    private String timeZone;
    //</editor-fold>

    //<editor-fold defaultstate="collapsed" desc="登录大厅需要更新的玩家数据">
    //上次ip
    private String lastIp;
    //ip
    private String ip;
    //地区
    private String region;
    //大厅id
    private int hallId;
    //gateId
    private int gateId;
    //在线状态
    private boolean online;
    //登录时间
    private long loginTime;
    //登出时间
    private long logoutTime;
    //首次在线时间(秒)
    private long firstOnlineTime;
    //在线时长(秒)
    private long onlineTime;
    //邮件
    private String email;
    //邮件是否bind
    private boolean emailBind;
    //区域
    private String areaCode;
    //电话
    private String phone;
    //手机绑定
    private boolean phoneBind;
    //账号
    private String account;
    //货币id
    private int currencyId;
    //显示法币
    private int viewFiat;
    //头像
    private String headId;
    //上次刷新时间 - 每天0点，每天2点，每周等都根据该时间计算...
    private long lastRefreshTime;
    //2FA
    private String secretKey;
    private String qrCode;
    //验证
    private boolean verify2FA;
    //隐藏
    private boolean hideName;
    //首充时间
    private long firstRechargeTime;
    //充值时间
    private long rechargeTime;
    //变账
    private long lastChangeTime;
    //充值限制
    private boolean depositLimit;
    //提现限制
    private boolean withdrawalLimit;
    //基础认证 0.未认证 1.认证中 2.认证成功 3.认证失败
    private int basicVerification;
    //高级认证  0.未认证 1.认证中 2.认证成功 3.认证失败
    private int advancedVerification;
    //网站
    private String webSite;
    //注册设备
    private String regDevice;
    //注册model
    private String regModel;
    //mac
    private String mac;
    //设备
    private String device;
    //model
    private String model;
    //浏览器
    private String browser;
    //pwa
    private boolean receivePwa;
    private String pixelId;
    private String fbToken;
    private String clickId;
    private String KWaiPixelId;
    private String KWaiToken;
    //rechargeBonus false 开启bonus
    private boolean rechargeBonus;
    //邮件订阅
    private boolean emailSubscribe;
    //渠道 0.h5 1.pwa 2.apk
    private int channel;

    //游戏服务器id
    private int agentGameServerId;
    //游戏id
    private int gameId;
    //bonus
    private boolean bonus;
    //游戏平台
    private int platformId;
    //游戏类型
    private int gameType;
    //进入游戏货币
    private int gameCurrencyId;
    //选中货币
    private int playerCurrencyId;
    //</editor-fold>

    //<editor-fold defaultstate="collapsed" desc="大厅功能的数据">
    //点赞的游戏
    private List<Integer> likeGame = new ArrayList<>();
    //最近的游戏
    private List<Integer> recentGame = new ArrayList<>();
    //收藏的游戏
    private List<Integer> favoritesGame = new ArrayList<>();
    //使用兑换码
    private Set<String> redemptionCodes = new HashSet<>();
    //channel
    private Set<Integer> channels = new HashSet<>();

    //分享id
    private int tgShareTimes;
    //活动
    private ActivityInfo activityInfo = new ActivityInfo();
    //转盘
    private LuckSpinInfo luckSpinInfo = new LuckSpinInfo();
    //每日竞猜
    private DailyContestInfo dailyContestInfo = new DailyContestInfo();
    //每周抽奖
    private WeeklyRaffleInfo weeklyRaffleInfo = new WeeklyRaffleInfo();
    //红包雨
    private RedEnvelopeRainInfo redEnvelopeRainInfo = new RedEnvelopeRainInfo();
    //推荐宝箱
    private RewardBoxInfo rewardBoxInfo = new RewardBoxInfo();
    //神秘奖金
    private MysteryBonusInfo mysteryBonusInfo = new MysteryBonusInfo();
    //存钱罐
    private PiggyBankInfo piggyBankInfo = new PiggyBankInfo();
    //连续充值
    private ContinuousDeposit continuousDepositInfo = new ContinuousDeposit();
    //首充签到
    private FirstChargeSignInInfo firstChargeSignInInfo = new FirstChargeSignInInfo();
    //首充返奖
    private RechargeRecoverInfo rechargeRecoverInfo = new RechargeRecoverInfo();
    //投注双赢
    private WageredRebatesInfo wageredRebatesInfo = new WageredRebatesInfo();
    //首充邀请
    private FirstDepositInviteBonusInfo firstDepositInviteBonusInfo = new FirstDepositInviteBonusInfo();

    //vipClub
    private VipClub vipClub = new VipClub();
    //Bonus
    private BonusInfo bonusInfo = new BonusInfo();
    //玩家统计
    private StatsInfo statsInfo = new StatsInfo();
    //邮件
    private InboxInfo inboxInfo = new InboxInfo();
    //任务
    private QuestInfo questInfo = new QuestInfo();
    //货币
    private Map<Integer, Double> currencyMap = new HashMap<>(2);
    //提现标准
    private Map<Integer, WithdrawStandard> withdrawStandardMap = new HashMap<>();
    //打码量
    private Map<Long, TurnoverRecord> turnoverRecordMap = new LinkedHashMap<>();
    //提现账户
    private Map<String, WithdrawAccount> withdrawAccountMap = new LinkedHashMap<>();
    //三方绑定
    private Map<Integer, ThreePartyInfo> threePartyInfoMap = new LinkedHashMap<>();
    //免费游戏次数 //currencyId+gameId
    private Map<Integer, FreeGameInfo> freeGameInfoMap = new LinkedHashMap<>();
    //</editor-fold>

    //今日免手续费
    private int dailyFeeTimes;
    //币商今日免手续费
    private int dailyCurrencyTraderWithdrawFeeTimes;
    //提现中
    private int withdrawInProgress;

    //记录注单号
    @Transient
    private transient Set<Long> recodeNoteId = new LinkedHashSet<>();
    //游戏记录
    @Transient
    private transient final Map<String, GameNote> gameNoteMap = new LinkedHashMap<>();
    //任务系统辅助组件
    @Transient
    private transient QuestGoalComponent questGoalComponent = new QuestGoalComponent();

    //加载数据
    //推广
    @Transient
    private transient PlayerPromote playerPromote;
    @Transient
    private transient boolean register;
    @Transient
    private transient long intervalTimes;

    @Transient
    private transient long sessionId;

    //心跳 30分钟
    @Transient
    private transient long heartbeat;
    @Transient
    private transient Channel clientSession;

    Player() {
        super(-1);//仅仅用于DB或反序列化
    }

    public Player(long playerId) {
        super(playerId);
        this._id = playerId;
        this.playerId = playerId;
        this.createTime = TimeUtil.currentTimeMillis();
    }

    public long get_id() {
        return _id;
    }

    public void set_id(long _id) {
        this._id = _id;
    }

    public long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(long playerId) {
        this.playerId = playerId;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public void setBusiness_no(String business_no) {
        this.business_no = business_no;
    }

    public String getPlayerName() {
        return playerName;
    }

    public void setPlayerName(String playerName) {
        this.playerName = playerName;
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public boolean isRobot() {
        return isRobot;
    }

    public void setRobot(boolean robot) {
        isRobot = robot;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public String getRegisterRegion() {
        return StringUtil.isNullOrEmpty(registerRegion) ? "" : registerRegion;
    }

    public void setRegisterRegion(String registerRegion) {
        this.registerRegion = registerRegion;
    }

    public String getRegisterIp() {
        return StringUtil.isNullOrEmpty(registerIp) ? "" : registerIp;
    }

    public void setRegisterIp(String registerIp) {
        this.registerIp = registerIp;
    }

    public int getLanguage() {
        return language;
    }

    public void setLanguage(int language) {
        this.language = language;
    }

    public String getInvitationCode() {
        return invitationCode;
    }

    public void setInvitationCode(String invitationCode) {
        this.invitationCode = invitationCode;
    }

    public int getAgentId() {
        return agentId;
    }

    public void setAgentId(int agentId) {
        this.agentId = agentId;
    }

    public int getChannelId() {
        return channelId;
    }

    public void setChannelId(int channelId) {
        this.channelId = channelId;
    }

    public int getMediaId() {
        return mediaId;
    }

    public void setMediaId(int mediaId) {
        this.mediaId = mediaId;
    }

    public int getAdId() {
        return adId;
    }

    public void setAdId(int adId) {
        this.adId = adId;
    }

    public int getCurrencyTraderId() {
        return currencyTraderId;
    }

    public void setCurrencyTraderId(int currencyTraderId) {
        this.currencyTraderId = currencyTraderId;
    }

    public String getTimeZone() {
        return timeZone;
    }

    public void setTimeZone(String timeZone) {
        this.timeZone = timeZone;
    }

    public boolean isOnline() {
        return online;
    }

    public void setOnline(boolean online) {
        this.online = online;
    }

    public long getLoginTime() {
        return loginTime;
    }

    public void setLoginTime(long loginTime) {
        this.loginTime = loginTime;
    }

    public long getLogoutTime() {
        return logoutTime;
    }

    public void setLogoutTime(long logoutTime) {
        this.logoutTime = logoutTime;
    }

    public String getLastIp() {
        return StringUtil.isNullOrEmpty(lastIp) ? "" : lastIp;
    }

    public void setLastIp(String lastIp) {
        this.lastIp = lastIp;
    }

    public String getIp() {
        return StringUtil.isNullOrEmpty(ip) ? "" : ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getRegion() {
        return StringUtil.isNullOrEmpty(region) ? "" : region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public int getHallId() {
        return hallId;
    }

    public void setHallId(int hallId) {
        this.hallId = hallId;
        RedisUtils.saveToRedis(this.playerId, PlayerFields.hallId, String.valueOf(hallId));
    }

    public int getGateId() {
        return gateId;
    }

    public void setGateId(int gateId) {
        this.gateId = gateId;
    }

    public long getFirstOnlineTime() {
        return firstOnlineTime;
    }

    public void setFirstOnlineTime(long firstOnlineTime) {
        this.firstOnlineTime = firstOnlineTime;
    }

    public long getOnlineTime() {
        return onlineTime;
    }

    public void setOnlineTime(long onlineTime) {
        this.onlineTime = onlineTime;
    }

    public String getEmail() {
        return StringUtil.isNullOrEmpty(email) ? "" : email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public boolean isEmailBind() {
        return emailBind;
    }

    public void setEmailBind(boolean emailBind) {
        this.emailBind = emailBind;
    }

    public String getAreaCode() {
        return StringUtil.isNullOrEmpty(areaCode) ? "" : areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getPhone() {
        return StringUtil.isNullOrEmpty(phone) ? "" : phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public boolean isPhoneBind() {
        return phoneBind;
    }

    public void setPhoneBind(boolean phoneBind) {
        this.phoneBind = phoneBind;
    }

    public String getAccount() {
        return StringUtil.isNullOrEmpty(account) ? "" : account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public int getCurrencyId() {
        return currencyId;
    }

    public void setCurrencyId(int currencyId) {
        this.currencyId = currencyId;
    }

    public int getViewFiat() {
        return viewFiat;
    }

    public void setViewFiat(int viewFiat) {
        this.viewFiat = viewFiat;
    }

    public String getHeadId() {
        return headId;
    }

    public void setHeadId(String headId) {
        this.headId = headId;
    }

    public long getLastRefreshTime() {
        return lastRefreshTime;
    }

    public void setLastRefreshTime(long lastRefreshTime) {
        this.lastRefreshTime = lastRefreshTime;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    public String getQrCode() {
        return qrCode;
    }

    public void setQrCode(String qrCode) {
        this.qrCode = qrCode;
    }

    public boolean isVerify2FA() {
        return verify2FA;
    }

    public void setVerify2FA(boolean verify2FA) {
        this.verify2FA = verify2FA;
    }

    public int getPlatformId() {
        return platformId;
    }

    public void setPlatformId(int platformId) {
        this.platformId = platformId;
    }

    public int getGameCurrencyId() {
        return gameCurrencyId;
    }

    public void setGameCurrencyId(int gameCurrencyId) {
        this.gameCurrencyId = gameCurrencyId;
    }

    public int getPlayerCurrencyId() {
        return playerCurrencyId;
    }

    public void setPlayerCurrencyId(int playerCurrencyId) {
        this.playerCurrencyId = playerCurrencyId;
    }

    public int getAgentGameServerId() {
        return agentGameServerId;
    }

    public void setAgentGameServerId(int agentGameServerId) {
        this.agentGameServerId = agentGameServerId;
    }

    public int getGameId() {
        return gameId;
    }

    public void setGameId(int gameId) {
        this.gameId = gameId;
    }

    public boolean isBonus() {
        return bonus;
    }

    public void setBonus(boolean bonus) {
        this.bonus = bonus;
    }

    public int getGameType() {
        return gameType;
    }

    public void setGameType(int gameType) {
        this.gameType = gameType;
    }

    public boolean isHideName() {
        return hideName;
    }

    public void setHideName(boolean hideName) {
        this.hideName = hideName;
    }

    public long getFirstRechargeTime() {
        return firstRechargeTime;
    }

    public void setFirstRechargeTime(long firstRechargeTime) {
        this.firstRechargeTime = firstRechargeTime;
    }

    public long getRechargeTime() {
        return rechargeTime;
    }

    public void setRechargeTime(long rechargeTime) {
        this.rechargeTime = rechargeTime;
    }

    public long getLastChangeTime() {
        return lastChangeTime;
    }

    public void setLastChangeTime(long lastChangeTime) {
        this.lastChangeTime = lastChangeTime;
    }

    public boolean isDepositLimit() {
        return depositLimit;
    }

    public void setDepositLimit(boolean depositLimit) {
        this.depositLimit = depositLimit;
    }

    public boolean isWithdrawalLimit() {
        return withdrawalLimit;
    }

    public void setWithdrawalLimit(boolean withdrawalLimit) {
        this.withdrawalLimit = withdrawalLimit;
    }

    public int getBasicVerification() {
        return basicVerification;
    }

    public void setBasicVerification(int basicVerification) {
        this.basicVerification = basicVerification;
    }

    public int getAdvancedVerification() {
        return advancedVerification;
    }

    public void setAdvancedVerification(int advancedVerification) {
        this.advancedVerification = advancedVerification;
    }

    public String getWebSite() {
        return StringUtil.isNullOrEmpty(webSite) ? "" : webSite;
    }

    public void setWebSite(String webSite) {
        this.webSite = webSite;
    }

    public String getDevice() {
        return StringUtil.isNullOrEmpty(device) ? "" : device;
    }

    public void setDevice(String device) {
        this.device = device;
    }

    public String getRegDevice() {
        return regDevice;
    }

    public void setRegDevice(String regDevice) {
        this.regDevice = regDevice;
    }

    public String getRegModel() {
        return regModel;
    }

    public void setRegModel(String regModel) {
        this.regModel = regModel;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getBrowser() {
        return browser;
    }

    public void setBrowser(String browser) {
        this.browser = browser;
    }

    public String getMac() {
        return mac;
    }

    public void setMac(String mac) {
        this.mac = mac;
    }

    public boolean isReceivePwa() {
        return receivePwa;
    }

    public void setReceivePwa(boolean receivePwa) {
        this.receivePwa = receivePwa;
    }

    public String getPixelId() {
        return pixelId;
    }

    public void setPixelId(String pixelId) {
        this.pixelId = pixelId;
    }

    public String getFbToken() {
        return fbToken;
    }

    public String getClickId() {
        return clickId;
    }

    public void setClickId(String clickId) {
        this.clickId = clickId;
    }

    public String getKWaiPixelId() {
        return KWaiPixelId;
    }

    public void setKWaiPixelId(String KWaiPixelId) {
        this.KWaiPixelId = KWaiPixelId;
    }

    public String getKWaiToken() {
        return KWaiToken;
    }

    public void setKWaiToken(String KWaiToken) {
        this.KWaiToken = KWaiToken;
    }

    public void setFbToken(String fbToken) {
        this.fbToken = fbToken;
    }

    public boolean isRechargeBonus() {
        return rechargeBonus;
    }

    public void setRechargeBonus(boolean rechargeBonus) {
        this.rechargeBonus = rechargeBonus;
    }

    public boolean isEmailSubscribe() {
        return emailSubscribe;
    }

    public void setEmailSubscribe(boolean emailSubscribe) {
        this.emailSubscribe = emailSubscribe;
    }

    public int getChannel() {
        return channel;
    }

    public void setChannel(int channel) {
        this.channel = channel;
    }

    public Set<Long> getRecodeNoteId() {
        return recodeNoteId;
    }

    public void setRecodeNoteId(Set<Long> recodeNoteId) {
        this.recodeNoteId = recodeNoteId;
    }

    public Map<String, GameNote> getGameNoteMap() {
        return gameNoteMap;
    }

    public Set<String> getRedemptionCodes() {
        return redemptionCodes;
    }

    public void setRedemptionCodes(Set<String> redemptionCodes) {
        this.redemptionCodes = redemptionCodes;
    }

    public Set<Integer> getChannels() {
        return channels;
    }

    public void setChannels(Set<Integer> channels) {
        this.channels = channels;
    }

    public List<Integer> getFavoritesGame() {
        return favoritesGame;
    }

    public void setFavoritesGame(List<Integer> favoritesGame) {
        this.favoritesGame = favoritesGame;
    }

    public List<Integer> getRecentGame() {
        return recentGame;
    }

    public void setRecentGame(List<Integer> recentGame) {
        this.recentGame = recentGame;
    }

    public List<Integer> getLikeGame() {
        return likeGame;
    }

    public void setLikeGame(List<Integer> likeGame) {
        this.likeGame = likeGame;
    }

    public VipClub getVipClub() {
        return vipClub;
    }

    public void setVipClub(VipClub vipClub) {
        this.vipClub = vipClub;
    }

    public BonusInfo getBonusInfo() {
        return bonusInfo;
    }

    public void setBonusInfo(BonusInfo bonusInfo) {
        this.bonusInfo = bonusInfo;
    }

    public StatsInfo getStatsInfo() {
        return statsInfo;
    }

    public void setStatsInfo(StatsInfo statsInfo) {
        this.statsInfo = statsInfo;
    }

    public InboxInfo getInboxInfo() {
        return inboxInfo;
    }

    public void setInboxInfo(InboxInfo inboxInfo) {
        this.inboxInfo = inboxInfo;
    }

    public int getTgShareTimes() {
        return tgShareTimes;
    }

    public void setTgShareTimes(int tgShareTimes) {
        this.tgShareTimes = tgShareTimes;
    }

    public ActivityInfo getActivityInfo() {
        return activityInfo;
    }

    public void setActivityInfo(ActivityInfo activityInfo) {
        this.activityInfo = activityInfo;
    }

    public QuestInfo getQuestInfo() {
        return questInfo;
    }

    public void setQuestInfo(QuestInfo questInfo) {
        this.questInfo = questInfo;
    }

    public LuckSpinInfo getLuckSpinInfo() {
        return luckSpinInfo;
    }

    public void setLuckSpinInfo(LuckSpinInfo luckSpinInfo) {
        this.luckSpinInfo = luckSpinInfo;
    }

    public DailyContestInfo getDailyContestInfo() {
        return dailyContestInfo;
    }

    public void setDailyContestInfo(DailyContestInfo dailyContestInfo) {
        this.dailyContestInfo = dailyContestInfo;
    }

    public WeeklyRaffleInfo getWeeklyRaffleInfo() {
        return weeklyRaffleInfo;
    }

    public void setWeeklyRaffleInfo(WeeklyRaffleInfo weeklyRaffleInfo) {
        this.weeklyRaffleInfo = weeklyRaffleInfo;
    }

    public RedEnvelopeRainInfo getRedEnvelopeRainInfo() {
        return redEnvelopeRainInfo;
    }

    public void setRedEnvelopeRainInfo(RedEnvelopeRainInfo redEnvelopeRainInfo) {
        this.redEnvelopeRainInfo = redEnvelopeRainInfo;
    }

    public RewardBoxInfo getRewardBoxInfo() {
        return rewardBoxInfo;
    }

    public void setRewardBoxInfo(RewardBoxInfo rewardBoxInfo) {
        this.rewardBoxInfo = rewardBoxInfo;
    }

    public MysteryBonusInfo getMysteryBonusInfo() {
        return mysteryBonusInfo;
    }

    public void setMysteryBonusInfo(MysteryBonusInfo mysteryBonusInfo) {
        this.mysteryBonusInfo = mysteryBonusInfo;
    }

    public PiggyBankInfo getPiggyBankInfo() {
        return piggyBankInfo;
    }

    public void setPiggyBankInfo(PiggyBankInfo piggyBankInfo) {
        this.piggyBankInfo = piggyBankInfo;
    }

    public ContinuousDeposit getContinuousDepositInfo() {
        return continuousDepositInfo;
    }

    public void setContinuousDepositInfo(ContinuousDeposit continuousDepositInfo) {
        this.continuousDepositInfo = continuousDepositInfo;
    }

    public FirstChargeSignInInfo getFirstChargeSignInInfo() {
        return firstChargeSignInInfo;
    }

    public void setFirstChargeSignInInfo(FirstChargeSignInInfo firstChargeSignInInfo) {
        this.firstChargeSignInInfo = firstChargeSignInInfo;
    }

    public RechargeRecoverInfo getRechargeRecoverInfo() {
        return rechargeRecoverInfo;
    }

    public void setRechargeRecoverInfo(RechargeRecoverInfo rechargeRecoverInfo) {
        this.rechargeRecoverInfo = rechargeRecoverInfo;
    }

    public WageredRebatesInfo getWageredRebatesInfo() {
        return wageredRebatesInfo;
    }

    public void setWageredRebatesInfo(WageredRebatesInfo wageredRebatesInfo) {
        this.wageredRebatesInfo = wageredRebatesInfo;
    }

    public FirstDepositInviteBonusInfo getFirstDepositInviteBonusInfo() {
        return firstDepositInviteBonusInfo;
    }

    public void setFirstDepositInviteBonusInfo(FirstDepositInviteBonusInfo firstDepositInviteBonusInfo) {
        this.firstDepositInviteBonusInfo = firstDepositInviteBonusInfo;
    }

    public PlayerPromote getPlayerPromote() {
        if (playerPromote == null) {
            playerPromote = EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class).getById(playerId);
        }
        return playerPromote;
    }

    public void setPlayerPromote(PlayerPromote playerPromote) {
        this.playerPromote = playerPromote;
    }

    public boolean isRegister() {
        return register;
    }

    public void setRegister(boolean register) {
        this.register = register;
    }

    public long getIntervalTimes() {
        return intervalTimes;
    }

    public void setIntervalTimes(long intervalTimes) {
        this.intervalTimes = intervalTimes;
    }

    public Map<Integer, Double> getCurrencyMap() {
        return currencyMap;
    }

    public void setCurrencyMap(Map<Integer, Double> currencyMap) {
        this.currencyMap = currencyMap;
    }

    public Map<Integer, WithdrawStandard> getWithdrawStandardMap() {
        return withdrawStandardMap;
    }

    public void setWithdrawStandardMap(Map<Integer, WithdrawStandard> withdrawStandardMap) {
        this.withdrawStandardMap = withdrawStandardMap;
    }

    public Map<Long, TurnoverRecord> getTurnoverRecordMap() {
        return turnoverRecordMap;
    }

    public void setTurnoverRecordMap(Map<Long, TurnoverRecord> turnoverRecordMap) {
        this.turnoverRecordMap = turnoverRecordMap;
    }

    public Map<String, WithdrawAccount> getWithdrawAccountMap() {
        return withdrawAccountMap;
    }

    public void setWithdrawAccountMap(Map<String, WithdrawAccount> withdrawAccountMap) {
        this.withdrawAccountMap = withdrawAccountMap;
    }

    public Map<Integer, ThreePartyInfo> getThreePartyInfoMap() {
        return threePartyInfoMap;
    }

    public void setThreePartyInfoMap(Map<Integer, ThreePartyInfo> threePartyInfoMap) {
        this.threePartyInfoMap = threePartyInfoMap;
    }

    public Map<Integer, FreeGameInfo> getFreeGameInfoMap() {
        return freeGameInfoMap;
    }

    public void setFreeGameInfoMap(Map<Integer, FreeGameInfo> freeGameInfoMap) {
        this.freeGameInfoMap = freeGameInfoMap;
    }

    public int getDailyFeeTimes() {
        return dailyFeeTimes;
    }

    public void setDailyFeeTimes(int dailyFeeTimes) {
        this.dailyFeeTimes = dailyFeeTimes;
    }

    public int getDailyCurrencyTraderWithdrawFeeTimes() {
        return dailyCurrencyTraderWithdrawFeeTimes;
    }

    public void setDailyCurrencyTraderWithdrawFeeTimes(int dailyCurrencyTraderWithdrawFeeTimes) {
        this.dailyCurrencyTraderWithdrawFeeTimes = dailyCurrencyTraderWithdrawFeeTimes;
    }

    public int getWithdrawInProgress() {
        return withdrawInProgress;
    }

    public void setWithdrawInProgress(int withdrawInProgress) {
        this.withdrawInProgress = withdrawInProgress;
    }

    public QuestGoalComponent getQuestGoalComponent() {
        return questGoalComponent;
    }

    public void setQuestGoalComponent(QuestGoalComponent questGoalComponent) {
        this.questGoalComponent = questGoalComponent;
    }

    public long getSessionId() {
        return sessionId;
    }

    public void setSessionId(long sessionId) {
        this.sessionId = sessionId;
    }

    public long getHeartbeat() {
        return heartbeat;
    }

    public void setHeartbeat(long heartbeat) {
        this.heartbeat = heartbeat;
    }

    public Channel getClientSession() {
        return clientSession;
    }

    public void setClientSession(Channel clientSession) {
        this.clientSession = clientSession;
    }

    public boolean sendMsg(Message message) {
        return sendMsg(message, this.sessionId);
    }

    /**
     * 封号
     */
    public boolean isTitle() {
        return this.state == 2;
    }

    /**
     * 冻结
     *
     * @return
     */
    public boolean isFreeze() {
        return this.state == 3;
    }

    /**
     * 发送消息
     *
     * @param message
     * @param id
     * @return
     */
    private boolean sendMsg(Message message, long id) {
        if (!this.online) {
            return false;
        }
        if (this.clientSession != null && this.clientSession.isActive()) {
            MsgUtil.sendClientMsg(this.clientSession, message);
            return true;
        } else {
            LOGGER.warn("playerId：{}，clientSession = null {}", this.playerId, message.getClass().getSimpleName());
        }
        return false;
    }

    public void updateCurrency(IntList changedCurrencies) {
        long startTime = TimeUtil.currentTimeMillis();
        RedisPoolManager.getInstance().syncConsumer(commands -> {
                    try {
                        final Map<String, String> data = new HashMap<>();
                        for (int i = 0; i < changedCurrencies.size(); i++) {
                            final int currencyType = changedCurrencies.getInt(i);
                            final double currencyValue = this.currencyMap.getOrDefault(currencyType, 0d);
                            data.put(String.valueOf(currencyType), String.valueOf(currencyValue));
                        }
                        commands.hset(RedisHall.Platform_Role_Map_Currency.getKey(this.playerId), data);
                    } catch (Exception e) {
                        LOGGER.error("Batch currency update failed", e);
                    }
                }
        );
        final long cost = TimeUtil.currentTimeMillis() - startTime;
        if (cost > 10) {
            LOGGER.warn("updateCurrency，playerId：{}，cost time：{}", playerId, TimeUtil.currentTimeMillis() - startTime);
        }
    }

    public void syncRedisData() {
        final Map<String, String> currencyMap = RedisPoolManager.getInstance().function(jedis ->
                jedis.sync().hgetall(RedisHall.Platform_Role_Map_Currency.getKey(this.playerId)));
        if (currencyMap == null || currencyMap.isEmpty()) {
            return;
        }
        this.currencyMap.clear();
        for (Map.Entry<String, String> entry : currencyMap.entrySet()) {
            final int currencyType = Integer.parseInt(entry.getKey());
            final double currencyValue = RedisUtils.convert(entry.getValue());
            this.currencyMap.put(currencyType, currencyValue);
        }
    }

    public Stats getStats(int currencyId) {
        return this.statsInfo.getStatsMap().computeIfAbsent(currencyId, k -> new Stats());
    }

    public WithdrawStandard getWithdrawStandard(int currencyId) {
        return this.withdrawStandardMap.computeIfAbsent(currencyId, k -> new WithdrawStandard());
    }

    public void addThreePartyInfo(int threeParty, String account, String threePartyId) {
        final ThreePartyInfo threePartyInfo = new ThreePartyInfo();
        threePartyInfo.setThreeParty(threeParty);
        threePartyInfo.setAccount(account);
        threePartyInfo.setThreePartyId(threePartyId);
        this.threePartyInfoMap.put(threeParty, threePartyInfo);
    }

    public ThreePartyInfo removeThreePartyInfo(int threeParty) {
        return this.threePartyInfoMap.remove(threeParty);
    }

    public FreeGameInfo getFreeGameInfo(int currencyId) {
        FreeGameInfo freeGameInfo = this.freeGameInfoMap.get(currencyId);
        if (freeGameInfo == null) {
            freeGameInfo = new FreeGameInfo();
            this.freeGameInfoMap.put(currencyId, freeGameInfo);
        }
        return freeGameInfo;
    }

    public void resetDaily() {
        this.dailyFeeTimes = 0;
        this.redEnvelopeRainInfo.setReceiveTimes(0);
        this.dailyCurrencyTraderWithdrawFeeTimes = 0;
        this.tgShareTimes = 0;
    }

    public int getTotalRechargeTimes() {
        return this.statsInfo.getStatsMap().values().stream().mapToInt(Stats::getTotalRechargeTimes).sum();
    }

    public int getDailyRechargeTimes() {
        return this.statsInfo.getStatsMap().values().stream().mapToInt(Stats::getDailyRechargeTimes).sum();
    }

    public int getWeeklyRechargeTimes() {
        return this.statsInfo.getStatsMap().values().stream().mapToInt(Stats::getWeeklyRechargeTimes).sum();
    }

    public int getMonthlyRechargeTimes() {
        return this.statsInfo.getStatsMap().values().stream().mapToInt(Stats::getMonthlyRechargeTimes).sum();
    }

    public void resetGameInfo() {
        this.setGameId(0);
        this.setBonus(false);
        this.setPlatformId(0);
        this.setGameType(0);
        this.setGameCurrencyId(0);
        this.setPlayerCurrencyId(0);
    }

}
