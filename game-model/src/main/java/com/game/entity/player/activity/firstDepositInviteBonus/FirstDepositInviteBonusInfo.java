package com.game.entity.player.activity.firstDepositInviteBonus;

import com.game.engine.math.BigDecimalUtils;
import com.wjybxx.io.AutoFields;
import com.wjybxx.io.binary.SerializableClass;
import com.wjybxx.io.document.PersistentEntity;
import org.springframework.data.annotation.Transient;

import java.util.*;

@AutoFields
@PersistentEntity
@SerializableClass
public class FirstDepositInviteBonusInfo {

    private int c_id;

    private boolean start;

    private Map<Integer, Double> chargeMap = new HashMap<>();

    private Map<Long, InviteRecharge> inviteRechargeMap = new LinkedHashMap<>();

    private Map<Integer, Double> receiveBonusMap = new HashMap<>();

    private Set<Integer> receiveMails = new HashSet<>();

    public int getC_id() {
        return c_id;
    }

    public void setC_id(int c_id) {
        this.c_id = c_id;
    }

    public boolean isStart() {
        return start;
    }

    public void setStart(boolean start) {
        this.start = start;
    }

    public Map<Integer, Double> getChargeMap() {
        return chargeMap;
    }

    public void setChargeMap(Map<Integer, Double> chargeMap) {
        this.chargeMap = chargeMap;
    }

    public Map<Long, InviteRecharge> getInviteRechargeMap() {
        return inviteRechargeMap;
    }

    public void setInviteRechargeMap(Map<Long, InviteRecharge> inviteRechargeMap) {
        this.inviteRechargeMap = inviteRechargeMap;
    }

    public Set<Integer> getReceiveMails() {
        return receiveMails;
    }

    public void setReceiveMails(Set<Integer> receiveMails) {
        this.receiveMails = receiveMails;
    }

    public Map<Integer, Double> getReceiveBonusMap() {
        return receiveBonusMap;
    }

    public void setReceiveBonusMap(Map<Integer, Double> receiveBonusMap) {
        this.receiveBonusMap = receiveBonusMap;
    }

    public void incCharge(int currencyId, double charge) {
        double amount = this.chargeMap.getOrDefault(currencyId, 0.0);
        amount = BigDecimalUtils.add(amount, charge, 9);
        this.chargeMap.put(currencyId, amount);
    }

    public void incReceiveBonus(int currencyId, double bonus) {
        double amount = this.receiveBonusMap.getOrDefault(currencyId, 0.0);
        amount = BigDecimalUtils.add(amount, bonus, 9);
        this.receiveBonusMap.put(currencyId, amount);
    }

    public void reset() {
        this.c_id = 0;
        this.start = false;
        this.chargeMap.clear();
        this.inviteRechargeMap.clear();
        this.receiveBonusMap.clear();
        this.receiveMails.clear();
    }
}
