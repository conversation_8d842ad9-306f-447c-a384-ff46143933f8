package com.game.dao.activity;

import com.game.engine.mongo.*;
import com.game.entity.activity.FirstDepositInviteNote;
import com.game.entity.activity.FirstDepositInviteNoteFields;
import com.game.entity.bonus.BonusNoteFields;
import com.game.utils.VirtualThreadUtils;
import com.mongodb.client.MongoCollection;
import org.bson.Document;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.List;

public class FirstDepositInviteNoteDao implements EntityDao<FirstDepositInviteNote> {

    private final MongoCollection<FirstDepositInviteNote> collection;
    private final MongoTemplate ops;

    public FirstDepositInviteNoteDao(DBConnectionMrg dbConnectionMrg) {
        this.ops = dbConnectionMrg.getMongoTemplate();
        this.collection = dbConnectionMrg.getCollection("firstDepositInvite_note", entityClass());
        final IndexDescAll indexDescAll = IndexDescAll.createWithAllIndexs(
                IndexDescSingle.createIndex(new IndexTypeAndName(Long.class, BonusNoteFields.playerId)),
                IndexDescSingle.createIndex(new IndexTypeAndName(Long.class, BonusNoteFields.createTime))
        );
        final MongoCollection<Document> mongoCollection = dbConnectionMrg.getMongoTemplate().getCollection("bonus_note");
        ToolMongoIndexHelper.indexBuildWork(indexDescAll.getApplicationIndex(), null, mongoCollection);
    }

    @Override
    public Class<FirstDepositInviteNote> entityClass() {
        return FirstDepositInviteNote.class;
    }

    public void insert(FirstDepositInviteNote firstDepositInviteNote) {
        if (firstDepositInviteNote.getPlayerId() <= 0) {
            throw new IllegalArgumentException("firstDepositInviteNote.getPlayerId() <= 0");
        }
        VirtualThreadUtils.execute(() -> collection.insertOne(firstDepositInviteNote));
    }

    public List<FirstDepositInviteNote> loadFirstDepositInviteNote(String business_no, long start, long end, int limit) {
        final Query query = new Query();
        query.addCriteria(Criteria.where("business_no").is(business_no));
        if (start > 0 && end > 0) {
            query.addCriteria(Criteria.where(FirstDepositInviteNoteFields.createTime).gte(start).lt(end));
        }

        return ops.find(query.with(Sort.by(Sort.Direction.DESC, FirstDepositInviteNoteFields.createTime))
                .limit(limit), FirstDepositInviteNote.class);
    }

}
