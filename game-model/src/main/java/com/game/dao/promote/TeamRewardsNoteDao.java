package com.game.dao.promote;

import com.game.engine.mongo.*;
import com.game.engine.util.misc.Tuple2;
import com.game.entity.player.promote.*;
import com.game.utils.VirtualThreadUtils;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.Accumulators;
import com.mongodb.client.model.Aggregates;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.Sorts;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class TeamRewardsNoteDao implements EntityDao<TeamRewardsNote> {

    private final MongoCollection<TeamRewardsNote> collection;
    private final MongoTemplate ops;

    public TeamRewardsNoteDao(DBConnectionMrg dbConnectionMrg) {
        this.ops = dbConnectionMrg.getMongoTemplate();
        this.collection = dbConnectionMrg.getCollection("teamRewards_note", entityClass());
        final IndexDescAll indexDescAll = IndexDescAll.createWithAllIndexs(
                IndexDescSingle.createIndex(new IndexTypeAndName(Long.class, TeamRewardsNoteFields.playerId)),
                IndexDescSingle.createIndex(new IndexTypeAndName(Long.class, TeamRewardsNoteFields.time))
        );
        final MongoCollection<Document> mongoCollection = dbConnectionMrg.getMongoTemplate().getCollection("teamRewards_note");
        ToolMongoIndexHelper.indexBuildWork(indexDescAll.getApplicationIndex(), null, mongoCollection);
    }

    @Override
    public Class<TeamRewardsNote> entityClass() {
        return TeamRewardsNote.class;
    }


    public void insert(TeamRewardsNote teamRewardsNote) {
        if (teamRewardsNote.getPlayerId() <= 0) {
            throw new IllegalArgumentException("teamRewardsNote.getPlayerId() <= 0");
        }
        VirtualThreadUtils.execute(() -> collection.insertOne(teamRewardsNote));
    }

    public Tuple2<Integer, List<TeamRewardsNote>> findByTime(long playerId, long startTime, long endTime, int skip, int limit) {
        final Bson query = Filters.and(
                Filters.eq(TeamRewardsNoteFields.playerId, playerId),
                Filters.gte(TeamRewardsNoteFields.time, startTime),
                Filters.lt(TeamRewardsNoteFields.time, endTime)
        );
        final int count = (int) collection.countDocuments(query);
        final List<TeamRewardsNote> teamRewardsNoteList = collection.find(query).sort(Sorts.descending(TeamRewardsNoteFields.time)).skip(skip).limit(limit).into(new ArrayList<>());
        return new Tuple2<>(count, teamRewardsNoteList);
    }

    public double aggregateRewards(long playerId, int currencyId, String validBets, long wagerStart, long wagerEnd) {
        final List<Bson> filterList = new ArrayList<>();
        filterList.add(Filters.eq(TeamRewardsNoteFields.playerId, playerId));
        if (currencyId > 0) {
            filterList.add(Filters.eq(TeamRewardsNoteFields.currencyId, currencyId));
        }
        filterList.add(Filters.gte(TeamRewardsNoteFields.time, wagerStart));
        filterList.add(Filters.lt(TeamRewardsNoteFields.time, wagerEnd));

        final Bson query = Filters.and(filterList);

        final TeamRewardsNote wagerAmount = collection.aggregate(Arrays.asList(
                Aggregates.match(query),
                Aggregates.group(null, Accumulators.sum(validBets, "$" + validBets))
        )).first();

        if (wagerAmount != null) {
            return wagerAmount.getAmount();
        }
        return 0;
    }

    /**
     * 60 day
     *
     * @param start
     * @param end
     * @return
     */
    public long delete(long start, long end) {
        final Criteria criteria = new Criteria();
        criteria.and(TeamRewardsNoteFields.time).gte(start).lte(end);

        int BATCH_SIZE = 1000;
        long totalDeleted = 0;
        while (true) {
            // 查询一批数据
            final Query query = new Query(criteria).limit(BATCH_SIZE);

            // 批量删除
            final long deletedCount = ops.remove(query, TeamRewardsNote.class).getDeletedCount();
            totalDeleted += deletedCount;

            if (deletedCount < BATCH_SIZE) {
                break;
            }
        }
        return totalDeleted;
    }

}
