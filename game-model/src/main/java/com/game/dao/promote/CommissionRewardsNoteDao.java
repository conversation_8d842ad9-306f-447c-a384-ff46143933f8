package com.game.dao.promote;

import com.game.engine.mongo.*;
import com.game.engine.util.misc.Tuple2;
import com.game.entity.activity.WinTicketsNote;
import com.game.entity.activity.WinTicketsNoteFields;
import com.game.entity.game.GameNote;
import com.game.entity.game.GameNoteFields;
import com.game.entity.game.GameWagerStat;
import com.game.entity.player.PlayerFields;
import com.game.entity.player.activity.ActivityInfoFields;
import com.game.entity.player.promote.CommissionRewardsNote;
import com.game.entity.player.promote.CommissionRewardsNoteFields;
import com.game.entity.player.promote.ReferralRewardsNoteFields;
import com.game.enums.GameType;
import com.game.utils.VirtualThreadUtils;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.model.Accumulators;
import com.mongodb.client.model.Aggregates;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.Sorts;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

public class CommissionRewardsNoteDao implements EntityDao<CommissionRewardsNote> {

    private final MongoCollection<CommissionRewardsNote> collection;
    private final MongoTemplate ops;

    public CommissionRewardsNoteDao(DBConnectionMrg dbConnectionMrg) {
        this.ops = dbConnectionMrg.getMongoTemplate();
        this.collection = dbConnectionMrg.getCollection("commissionRewards_note", entityClass());
        final IndexDescAll indexDescAll = IndexDescAll.createWithAllIndexs(
                IndexDescSingle.createIndex(new IndexTypeAndName(Long.class, CommissionRewardsNoteFields.playerId)),
                IndexDescSingle.createIndex(new IndexTypeAndName(Long.class, CommissionRewardsNoteFields.time))
        );
        final MongoCollection<Document> mongoCollection = dbConnectionMrg.getMongoTemplate().getCollection("commissionRewards_note");
        ToolMongoIndexHelper.indexBuildWork(indexDescAll.getApplicationIndex(), null, mongoCollection);
    }

    @Override
    public Class<CommissionRewardsNote> entityClass() {
        return CommissionRewardsNote.class;
    }

    public void insert(CommissionRewardsNote commissionRewardsNote) {
        if (commissionRewardsNote.getPlayerId() <= 0) {
            throw new IllegalArgumentException("commissionRewardsNote.getPlayerId() <= 0");
        }
        VirtualThreadUtils.execute(() -> collection.insertOne(commissionRewardsNote));
    }

    public Tuple2<Integer, List<CommissionRewardsNote>> findByTime(long playerId, long startTime, long endTime, int skip, int limit) {
        final Bson query = Filters.and(
                Filters.eq(CommissionRewardsNoteFields.playerId, playerId),
                Filters.gte(CommissionRewardsNoteFields.time, startTime),
                Filters.lt(CommissionRewardsNoteFields.time, endTime)
        );
        final int count = (int) collection.countDocuments(query);
        final List<CommissionRewardsNote> commissionRewardsNoteList = collection.find(query).sort(Sorts.descending(CommissionRewardsNoteFields.time)).skip(skip).limit(limit).into(new ArrayList<>());
        return new Tuple2<>(count, commissionRewardsNoteList);
    }

    public double aggregateRewards(long playerId, int currencyId, String validBets, long wagerStart, long wagerEnd) {
        final List<Bson> filterList = new ArrayList<>();
        filterList.add(Filters.eq(CommissionRewardsNoteFields.playerId, playerId));
        if (currencyId > 0) {
            filterList.add(Filters.eq(CommissionRewardsNoteFields.currencyId, currencyId));
        }
        if (wagerStart > 0 && wagerEnd > 0) {
            filterList.add(Filters.gte(CommissionRewardsNoteFields.time, wagerStart));
            filterList.add(Filters.lt(CommissionRewardsNoteFields.time, wagerEnd));

        }

        final Bson query = Filters.and(filterList);

        final CommissionRewardsNote wagerAmount = collection.aggregate(Arrays.asList(
                Aggregates.match(query),
                Aggregates.group(null, Accumulators.sum(validBets, "$" + validBets))
        )).first();

        if (wagerAmount != null) {
            return wagerAmount.getAmount();
        }
        return 0;
    }

    /**
     * 60 day
     *
     * @param start
     * @param end
     * @return
     */
    public long delete(long start, long end) {
        final Criteria criteria = new Criteria();
        criteria.and(CommissionRewardsNoteFields.time).gte(start).lte(end);

        int BATCH_SIZE = 1000;
        long totalDeleted = 0;
        while (true) {
            // 查询一批数据
            final Query query = new Query(criteria).limit(BATCH_SIZE);

            // 批量删除
            final long deletedCount = ops.remove(query, CommissionRewardsNote.class).getDeletedCount();
            totalDeleted += deletedCount;

            if (deletedCount < BATCH_SIZE) {
                break;
            }
        }
        return totalDeleted;
    }

}
