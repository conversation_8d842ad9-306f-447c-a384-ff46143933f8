package com.game.handler.http.login;

import com.game.engine.io.handler.HttpHandler;
import com.game.engine.log.GameLog;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.GuidGeneratorUtils;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.enums.ErrorCode;
import com.game.loginsr.main.LoginServer;
import com.game.loginsr.mrg.DataLoginMrg;
import com.proto.LoginMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@IHandlerEntity(path = "/api/login/channelInstall", desc = "渠道安装")
public class ReqChannelInstallDataHandler extends HttpHandler {
    private final Logger LOGGER = LoggerFactory.getLogger(ReqChannelInstallDataHandler.class);

    @Override
    public void run() {
        final LoginMessage.ResChannelInstallMessage.Builder res = LoginMessage.ResChannelInstallMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResChannelInstall_VALUE);
        try {
            final String data = (String) paramsMap.get("data");
            final byte[] bytes = MsgUtil.decode(data);

            final LoginMessage.ReqChannelInstallMessage req = LoginMessage.ReqChannelInstallMessage.parseFrom(bytes);
            final String host = req.getHost();
            final int channel = req.getChannel();
            final String uniqueID = req.getUniqueID();
            final String referralCode = req.getReferralCode();
            final String cl = req.getCl();
            final String ea = req.getEa();
            final String de = req.getDe();

            final String business_no = DataLoginMrg.getInstance().findBusiness_no(this.getClass().getSimpleName(), host);
            if (StringUtil.isNullOrEmpty(business_no)) {
                res.setError(ErrorCode.DomainName_Not_Exist.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);

            //TODO 统计channel安装
            int agentId = 0;
            if (!StringUtil.isNullOrEmpty(referralCode)) {
                if (Integer.parseInt(referralCode) / 10000000 == 3) {
                    agentId = Integer.parseInt(referralCode);
                }
            }
            final GameLog channelInstallLog = new GameLog("platform_channelInstallLog");
            channelInstallLog.append("number", GuidGeneratorUtils.generateOrderId())
                    .append("business_no", business_no)
                    .append("site", host)
                    .append("mediaId", ea)
                    .append("adId", de)
                    .append("channel", channel)
                    .append("uniqueID", uniqueID)
                    .append("agentId", agentId)
                    .append("channelId", cl)
                    .append("ip", MsgUtil.getClientIp(session))
                    .append("logTime", TimeUtil.currentTimeMillis());
            LoginServer.getInstance().getLogProducerMrg().send(channelInstallLog);
        } catch (Exception e) {
            LOGGER.error("ReqChannelInstallDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

}
