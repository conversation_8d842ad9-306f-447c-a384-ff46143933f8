package com.game.handler.http.login;

import com.game.c_entity.merchant.C_Advertise;
import com.game.c_entity.merchant.C_MaintainNotice;
import com.game.c_entity.middleplatform.C_BaseMaintainNotice;
import com.game.c_entity.middleplatform.C_BaseMerchant;
import com.game.dao.RegisterLimitDao;
import com.game.dao.account.AccountDao;
import com.game.engine.enums.state.ServerState;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.Config;
import com.game.engine.utils.MsgUtil;
import com.game.entity.RegisterLimit;
import com.game.entity.account.Account;
import com.game.entity.account.ThreePartyInfo;
import com.game.enums.ErrorCode;
import com.game.enums.ThreeParty;
import com.game.enums.redis.RedisLogin;
import com.game.loginsr.mrg.AccountMrg;
import com.game.loginsr.mrg.DataLoginMrg;
import com.game.loginsr.mrg.MerchantData;
import com.game.loginsr.script.IFeedbackScript;
import com.game.loginsr.server.script.IAccountScript;
import com.game.loginsr.server.script.ILoginScript;
import com.game.manager.EntityDaoMrg;
import com.proto.CommonMessage;
import com.proto.LoginMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;
import java.util.Map;

@IHandlerEntity(path = "/api/login/game", desc = "登录")
public class ReqLoginHandler extends HttpHandler {
    private final Logger LOGGER = LoggerFactory.getLogger(ReqLoginHandler.class);

    @Override
    public void run() {
        final LoginMessage.ResLoginMessage.Builder res = LoginMessage.ResLoginMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResLogin_VALUE);
        try {
            if (Config.serverState == ServerState.MAINTAIN || Config.serverState == ServerState.CLOSING) {
                res.setError(ErrorCode.Server_Maintenance.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final String data = (String) paramsMap.get("data");
            final byte[] bytes = MsgUtil.decode(data);
            final String country = (String) paramsMap.get("country");

            final LoginMessage.ReqLoginMessage req = LoginMessage.ReqLoginMessage.parseFrom(bytes);

            final C_BaseMerchant c_baseMerchant = DataLoginMrg.getInstance().findC_BaseMerchant(this.getClass().getSimpleName(), req.getHost());
            if (c_baseMerchant == null) {
                res.setError(ErrorCode.DomainName_Not_Exist.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }
            final String business_no = c_baseMerchant.getBusiness_no();

            final C_BaseMaintainNotice c_baseMaintainNotice = DataLoginMrg.getInstance().findC_BaseMaintainNotice();
            if (c_baseMaintainNotice != null && c_baseMaintainNotice.isStatus()) {
                res.setError(ErrorCode.Server_Maintenance.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                LOGGER.warn("playerId；{}，c_baseMaintainNotice", pid);
                return;
            }

            final MerchantData merchantData = DataLoginMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), business_no);
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            String account = req.getAccount();
            if (req.getThreeParty() == ThreeParty.Phone.getThreeParty()) {
                account = req.getAreaCode() + "-" + req.getAccount();
            }

            final C_MaintainNotice c_maintainNotice = merchantData.findC_MaintainNotice();
            if (c_maintainNotice != null && c_maintainNotice.isStatus()) {
                final String finalAccount = account;
                final String accountIds = RedisPoolManager.getInstance().function(jedis ->
                        jedis.sync().hget(RedisLogin.Platform_LG_Map_AccountID.getKey(business_no, req.getThreeParty()), finalAccount));
                final long accountId = StringUtil.isNullOrEmpty(accountIds) ? 0 : Long.parseLong(accountIds);
                if (!c_maintainNotice.getWhitelistIds().contains(accountId)) {
                    res.setError(ErrorCode.Server_Maintenance.getCode());
                    MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                    LOGGER.warn("playerId；{}，c_maintainNotice", accountId);
                    return;
                }
            }

            final String ip = MsgUtil.getClientIp(session);
            if (DataLoginMrg.getInstance().isBlackList(business_no, ip)) {
                LOGGER.warn("account：{}，ip：{}，blackList", req.getAccount(), ip);
                res.setError(ErrorCode.Blacklist_Not_Login.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final Map<String, Object> receiveMap = new LinkedHashMap<>();
            receiveMap.put("account", req.getAccount());
            receiveMap.put("threeParty", req.getThreeParty());
            receiveMap.put("device", req.getDevice());
            receiveMap.put("country", country);
            receiveMap.put("channel", req.getChannel());
            final CommonMessage.FbInfo fbInfo = req.getFbInfo();
            receiveMap.put("pixelId", fbInfo.getPixelId());
            receiveMap.put("fbToken", fbInfo.getFbToken());
            receiveMap.put("business_no", c_baseMerchant.getBusiness_no());
            final int threeParty = req.getThreeParty();

            switch (ThreeParty.valuesOf(threeParty)) {
                case Email:
                    emailLogin(business_no, req, receiveMap);
                    break;
                case Phone:
                    phoneLogin(business_no, req, receiveMap);
                    break;
                case Account:
                    accountLogin(business_no, req, receiveMap);
                    break;
                default://三方
                    threePartyLogin(req, res, business_no, ip, receiveMap);
                    break;
            }

        } catch (Exception e) {
            LOGGER.error("ReqLoginHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    private Account findAccount(String business_no, int threeParty, String account) {
        final String accountIds = RedisPoolManager.getInstance().function(jedis ->
                jedis.sync().hget(RedisLogin.Platform_LG_Map_AccountID.getKey(business_no, threeParty), account));
        final long accountId = StringUtil.isNullOrEmpty(accountIds) ? 0 : Long.parseLong(accountIds);
        if (accountId == 0) {
            return null;
        }
        return AccountMrg.getInstance().findAccountId(accountId);
    }

    private void emailLogin(String business_no, LoginMessage.ReqLoginMessage req, final Map<String, Object> paramsMap) {
        paramsMap.put("password", req.getPassword().trim());

        final Account account = findAccount(business_no, req.getThreeParty(), req.getAccount());
        ScriptLoader.getInstance().consumerScript("MailLoginScript",
                (ILoginScript script) -> script.login(account, getSession(), paramsMap));
    }

    private void accountLogin(String business_no, LoginMessage.ReqLoginMessage req, final Map<String, Object> paramsMap) {
        paramsMap.put("account", req.getAccount().trim());
        paramsMap.put("password", req.getPassword().trim());

        final Account account = findAccount(business_no, req.getThreeParty(), req.getAccount());
        ScriptLoader.getInstance().consumerScript("AccountLoginScript",
                (ILoginScript script) -> script.login(account, getSession(), paramsMap));
    }

    private void phoneLogin(String business_no, LoginMessage.ReqLoginMessage req, final Map<String, Object> paramsMap) {
        final String ac = req.getAreaCode() + "-" + req.getAccount();
        paramsMap.put("account", ac.trim());
        paramsMap.put("password", req.getPassword().trim());

        final Account account = findAccount(business_no, req.getThreeParty(), ac);
        ScriptLoader.getInstance().consumerScript("PhoneLoginScript",
                (ILoginScript script) -> script.login(account, getSession(), paramsMap));
    }

    private void threePartyLogin(LoginMessage.ReqLoginMessage req, LoginMessage.ResLoginMessage.Builder res,
                                 String business_no, String ip, final Map<String, Object> paramsMap) {

        Account account = findAccount(business_no, req.getThreeParty(), req.getThreePartyId());
        if (account == null) {
            synchronized (req.getThreePartyId().intern()) {
                RegisterLimit registerLimit = EntityDaoMrg.getInstance().getDao(RegisterLimitDao.class).findByIp(business_no, ip);
                if (registerLimit == null) {
                    registerLimit = new RegisterLimit();
                    registerLimit.setBusiness_no(business_no);
                    registerLimit.setIpAddress(ip);
                    EntityDaoMrg.getInstance().getDao(RegisterLimitDao.class).insert(registerLimit);
                }

                final MerchantData merchantData = DataLoginMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), business_no);
                if (merchantData == null) {
                    res.setError(ErrorCode.Data_Error.getCode());
                    MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                    return;
                }

                final String sameIpRegisterLimit = merchantData.findC_GlobalValue("sameIpRegisterLimit");
                if (!StringUtil.isNullOrEmpty(sameIpRegisterLimit) && registerLimit.getCount() >= Integer.parseInt(sameIpRegisterLimit)) {
                    LOGGER.warn("account：{}，registered limit", req.getAccount());
                    res.setError(ErrorCode.Registered_limit.getCode());
                    MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                    return;
                }

                LOGGER.warn("threePartyId：{}，http://{}/pwa?al={}&ea={}&de={}&fb_dynamic_pixel={}&fb_token={}",
                        req.getThreePartyId(), req.getHost(), req.getReferralCode(), req.getEa(), req.getDe(), req.getFbInfo().getPixelId(), req.getFbInfo().getFbToken());

                LOGGER.warn("threePartyId：{}，http://{}/kwai?al={}&ea={}&de={}&kwai_dynamic_pixel={}&kwai_token={}",
                        req.getThreePartyId(), req.getHost(), req.getReferralCode(), req.getEa(), req.getDe(), req.getKWaiInfo().getPixelId(), req.getKWaiInfo().getKWaiToken());

                final String accounts = req.getAccount();
                final int threeParty = req.getThreeParty();
                final String threePartyId = req.getThreePartyId();
                final String country = (String) paramsMap.get("country");

                final String host = req.getHost();
                final String referralCode = req.getReferralCode();
                final String cl = req.getCl();
                final String ea = req.getEa();
                final String de = req.getDe();
                final String activity = req.getActivity();

                final boolean check = ScriptLoader.getInstance().functionScript("AccountScript",
                        (IAccountScript script) -> script.checkReferralCode(referralCode));
                if (!check) {
                    res.setError(ErrorCode.ReferralCode_Error.getCode());
                    MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                    return;
                }

                account = AccountMrg.getInstance().createAccount(business_no, threeParty, threePartyId);

                final Account finalAccount1 = account;
                finalAccount1.setBusiness_no(business_no);
                ScriptLoader.getInstance().consumerScript("AccountScript",
                        (IAccountScript script) -> script.bindReferralCode(finalAccount1, referralCode, host, cl, ea, de));

                //TODO 处理掉参数
                int type = 0;
                String pixelId = "";
                String token = "";
                if (!StringUtil.isNullOrEmpty(req.getEa()) && !StringUtil.isNullOrEmpty(req.getDe())) {
                    if (StringUtil.isNullOrEmpty(req.getFbInfo().getPixelId()) && StringUtil.isNullOrEmpty(req.getKWaiInfo().getPixelId())) {
                        final String advertiseId = req.getReferralCode() + req.getEa() + req.getDe();
                        final C_Advertise c_advertise = merchantData.findC_Advertise(advertiseId);
                        if (c_advertise != null) {
                            type = c_advertise.getType();
                            pixelId = c_advertise.getPixelId();
                            token = c_advertise.getToken();
                        }
                    }
                }
                if (type == 0) {
                    account.setPixelId(req.getFbInfo().getPixelId());
                    account.setFbToken(req.getFbInfo().getFbToken());
                    account.setClickId(req.getKWaiInfo().getClickId());
                    account.setKWaiPixelId(req.getKWaiInfo().getPixelId());
                    account.setKWaiToken(req.getKWaiInfo().getKWaiToken());
                } else if (type == 1) {//fb
                    account.setPixelId(pixelId);
                    account.setFbToken(token);
                } else if (type == 2) {//kWai
                    account.setClickId(req.getKWaiInfo().getClickId());
                    account.setKWaiPixelId(pixelId);
                    account.setKWaiToken(token);
                }

                account.setRegion(country);
                account.setBusiness_no(business_no);
                account.setMediaId(Integer.parseInt(StringUtil.isNullOrEmpty(ea) ? "0" : ea));
                account.setAdId(Integer.parseInt(StringUtil.isNullOrEmpty(de) ? "0" : de));
                account.setActivity(StringUtil.isNullOrEmpty(activity) ? "" : activity);

                final ThreePartyInfo threePartyInfo = new ThreePartyInfo();
                threePartyInfo.setThreeParty(threeParty);
                threePartyInfo.setThreePartyId(threePartyId);
                threePartyInfo.setAccount(accounts);
                account.getThreePartyInfoMap().put(threeParty, threePartyInfo);
                EntityDaoMrg.getInstance().getDao(AccountDao.class).insert(account);

                EntityDaoMrg.getInstance().getDao(RegisterLimitDao.class).updateRegisterLimit(business_no, ip);
                paramsMap.put("register", true);

                //fb回传
                ScriptLoader.getInstance().consumerScript("FeedbackScript",
                        (IFeedbackScript script) -> script.sendFbFeedback(finalAccount1, "", "", country, ip));

                //KWai回传
                ScriptLoader.getInstance().consumerScript("FeedbackScript",
                        (IFeedbackScript script) -> script.sendKWaiFeedback(finalAccount1));
            }
        }

        final Account finalAccount = account;
        paramsMap.put("threePartyId", req.getThreePartyId());
        ScriptLoader.getInstance().consumerScript("ThreePartyLoginScript",
                (ILoginScript script) -> script.login(finalAccount, getSession(), paramsMap));
    }

}
