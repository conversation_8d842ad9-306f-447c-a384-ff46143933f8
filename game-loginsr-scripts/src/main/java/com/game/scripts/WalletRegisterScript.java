package com.game.scripts;

import com.game.c_entity.merchant.C_Advertise;
import com.game.c_entity.middleplatform.C_BaseMerchant;
import com.game.dao.RegisterLimitDao;
import com.game.dao.account.AccountDao;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.MD5;
import com.game.engine.utils.MsgUtil;
import com.game.entity.account.Account;
import com.game.entity.account.wallet.Web3Wallet;
import com.game.enums.ErrorCode;
import com.game.enums.ThreeParty;
import com.game.enums.redis.RedisLogin;
import com.game.loginsr.mrg.AccountMrg;
import com.game.loginsr.mrg.DataLoginMrg;
import com.game.loginsr.mrg.MerchantData;
import com.game.loginsr.script.IFeedbackScript;
import com.game.loginsr.script.IRegisterScript;
import com.game.loginsr.server.script.IAccountScript;
import com.game.loginsr.server.script.ILoginScript;
import com.game.manager.EntityDaoMrg;
import com.proto.LoginMessage;
import com.proto.MIDMessage;
import io.netty.channel.Channel;
import io.netty.util.internal.StringUtil;
import org.apache.pulsar.client.api.AuthenticationFactory;
import org.apache.pulsar.client.api.Producer;
import org.apache.pulsar.client.api.PulsarClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;
import java.util.Map;

public class WalletRegisterScript implements IRegisterScript {
    private static final Logger LOGGER = LoggerFactory.getLogger(WalletRegisterScript.class);

    @Override
    public void walletRegister(LoginMessage.ReqRegisterMessage req, C_BaseMerchant c_baseMerchant, String ip,
                               String country, Channel session) {
        final LoginMessage.ResLoginMessage.Builder res = LoginMessage.ResLoginMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResLogin_VALUE);
        try {
            // 新的字段
            final String walletAddress = req.getWalletAddress();  // 钱包地址
            final String chainNamespace = req.getChainNamespace();  // 链命名空间
            final int chainId = req.getChainId();  // 链ID
            final String referralCode = req.getReferralCode();  // 推荐码
            final String host = req.getHost();  // 域名
            final String cl = req.getCl();  // 渠道
            final String ea = req.getEa();  // 媒体id
            final String de = req.getDe();  // 广告
            final String activity = req.getActivity();  // 活动

            if (StringUtil.isNullOrEmpty(walletAddress) || StringUtil.isNullOrEmpty(chainNamespace) || chainId <= 0) {
                LOGGER.warn("walletAddress：{}，chainNamespace：{}，chainId：{}，is invalid", walletAddress, chainNamespace, chainId);
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final String business_no = c_baseMerchant.getBusiness_no();

            final MerchantData merchantData = DataLoginMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), business_no);
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            // 检查钱包地址是否已注册
            final String accountId = RedisPoolManager.getInstance().function(jedis ->
                    jedis.sync().hget(RedisLogin.Platform_LG_Map_AccountID.getKey(business_no, ThreeParty.Wallet.getThreeParty()), walletAddress));
            if (!StringUtil.isNullOrEmpty(accountId)) {
                LOGGER.warn("walletAddress：{}，already registered", walletAddress);
                res.setError(ErrorCode.Account_AlreadyExists.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            // 检查推荐码
            final boolean check = ScriptLoader.getInstance().functionScript("AccountScript",
                    (IAccountScript script) -> script.checkReferralCode(referralCode));
            if (!check) {
                res.setError(ErrorCode.ReferralCode_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            // 创建账户
            final Account account = AccountMrg.getInstance().createAccount(business_no, ThreeParty.Wallet.getThreeParty(), walletAddress);

            account.setBusiness_no(business_no);

            // 绑定推荐码
            ScriptLoader.getInstance().consumerScript("AccountScript",
                    (IAccountScript script) -> script.bindReferralCode(account, referralCode, host, cl, ea, de));

            // 设置广告信息
            int type = 0;
            String pixelId = "";
            String token = "";
            if (!StringUtil.isNullOrEmpty(req.getEa()) && !StringUtil.isNullOrEmpty(req.getDe())) {
                if (StringUtil.isNullOrEmpty(req.getFbInfo().getPixelId()) && StringUtil.isNullOrEmpty(req.getKWaiInfo().getPixelId())) {
                    final String advertiseId = req.getReferralCode() + req.getEa() + req.getDe();
                    final C_Advertise c_advertise = merchantData.findC_Advertise(advertiseId);
                    if (c_advertise != null) {
                        type = c_advertise.getType();
                        pixelId = c_advertise.getPixelId();
                        token = c_advertise.getToken();
                    }
                }
            }
            if (type == 0) {
                account.setPixelId(req.getFbInfo().getPixelId());
                account.setFbToken(req.getFbInfo().getFbToken());
                account.setClickId(req.getKWaiInfo().getClickId());
                account.setKWaiPixelId(req.getKWaiInfo().getPixelId());
                account.setKWaiToken(req.getKWaiInfo().getKWaiToken());
            } else if (type == 1) {
                account.setPixelId(pixelId);
                account.setFbToken(token);
            } else if (type == 2) {
                account.setClickId(req.getKWaiInfo().getClickId());
                account.setKWaiPixelId(pixelId);
                account.setKWaiToken(token);
            }

            // 设置额外的字段
            account.setRegion(country);
            account.setBusiness_no(business_no);
            account.setMediaId(Integer.parseInt(StringUtil.isNullOrEmpty(ea) ? "0" : ea));
            account.setAdId(Integer.parseInt(StringUtil.isNullOrEmpty(de) ? "0" : de));
            account.setActivity(StringUtil.isNullOrEmpty(activity) ? "" : activity);
            account.setEmailSubscribe(req.getEmailSubscribe());

            // 创建钱包信息
            Web3Wallet walletInfo = new Web3Wallet();
            walletInfo.setWalletAddress(walletAddress);
            walletInfo.setChainNamespace(chainNamespace);
            walletInfo.setChainId(chainId);
            account.setWallet(walletInfo);

            // 加密密码并设置
            account.setPassword(MD5.MD5Encode(req.getPassword()));

            // 保存账户信息
            EntityDaoMrg.getInstance().getDao(AccountDao.class).insert(account);
            EntityDaoMrg.getInstance().getDao(RegisterLimitDao.class).updateRegisterLimit(business_no, ip);

            // 发送消息到 Pulsar
            sendToPulsar(account);

            // 执行钱包登录逻辑
            walletLogin(req, account, session);

            // 回传FB信息
            ScriptLoader.getInstance().consumerScript("FeedbackScript",
                    (IFeedbackScript script) -> script.sendFbFeedback(account, "", walletAddress, country, ip));

            // 回传KWai信息
            ScriptLoader.getInstance().consumerScript("FeedbackScript",
                    (IFeedbackScript script) -> script.sendKWaiFeedback(account));
        } catch (Exception e) {
            LOGGER.error("walletRegister error", e);
        }
    }

    // Web3钱包登录逻辑
    private void walletLogin(LoginMessage.ReqRegisterMessage req, Account account, Channel session) {
        final String walletAddress = account.getWallet().getWalletAddress();
        final Map<String, Object> paramsMap = new LinkedHashMap<>();
        paramsMap.put("account", walletAddress);
        paramsMap.put("password", req.getPassword());
        paramsMap.put("threeParty", ThreeParty.Wallet.getThreeParty());
        paramsMap.put("device", req.getDevice());
        paramsMap.put("business_no", account.getBusiness_no());
        ScriptLoader.getInstance().consumerScript("Web3WalletLoginScript",
                (ILoginScript script) -> script.login(account, session, paramsMap));
    }

    private void sendToPulsar(Account account) {
        // todo 配置和连接方式等他们确定的

        Web3Wallet wallet = account.getWallet();

        // Pulsar 配置参数（假设）
        String pulsarServiceUrl = "pulsar://your-pulsar-url:6650";  // 替换为 Pulsar 服务 URL
        String pulsarTopic = "web3-wallet-register-topic";  // Pulsar 主题

        // 认证信息（如果需要的话）
        String pulsarAuthToken = "your-authentication-token";  // 如果需要 Token 认证
        // 或者是用户名和密码认证的情况
        String pulsarUsername = "your-username";  // 用户名
        String pulsarPassword = "your-password";  // 密码

        // 消息内容
        String pulsarMessage = String.format(
                "Account: %s, Wallet Address: %s, Chain Namespace: %s, Chain ID: %d",
                account.getAccount(), wallet.getWalletAddress(), wallet.getChainNamespace(), wallet.getChainId()
        );

        try {
            // 创建 Pulsar 客户端
            PulsarClient client = PulsarClient.builder()
                    .serviceUrl(pulsarServiceUrl)
                    .authentication(AuthenticationFactory.token(pulsarAuthToken)) // 使用 token 认证
                    //.authentication(AuthenticationFactory.basic(pulsarUsername, pulsarPassword))  // 如果需要用户名密码认证
                    .build();

            // 创建生产者
            Producer<byte[]> producer = client.newProducer()
                    .topic(pulsarTopic)
                    .create();

            // 发送消息到 Pulsar
            producer.send(pulsarMessage.getBytes());

            // 关闭 producer 和 client
            producer.close();
            client.close();

            LOGGER.info("Message sent to Pulsar: {}", pulsarMessage);
        } catch (Exception e) {
            LOGGER.error("Failed to send message to Pulsar", e);
        }
    }

}
