package com.game.scripts;

import com.game.c_entity.merchant.C_Agent;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.math.MathUtils;
import com.game.entity.account.Account;
import com.game.enums.redis.RedisLogin;
import com.game.loginsr.mrg.DataLoginMrg;
import com.game.loginsr.mrg.MerchantData;
import com.game.loginsr.server.script.IAccountScript;
import io.netty.util.internal.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class AccountScript implements IAccountScript {
    private static final Logger LOGGER = LoggerFactory.getLogger(AccountScript.class);

    @Override
    public boolean checkReferralCode(String referralCode) {
        if (StringUtil.isNullOrEmpty(referralCode)) {
            return true;
        }

        //联盟推广码
        if (referralCode.length() == 10) {
            return true;
        }

        if (!StringUtils.isNumeric(referralCode)) {
            final String superiorId = RedisPoolManager.getInstance().function(jedis ->
                    jedis.sync().hget(RedisLogin.Platform_LG_Map_InvitationID.getKey(), referralCode));
            if (StringUtil.isNullOrEmpty(superiorId)) {
                LOGGER.warn("referrerCode：{}， bind failed", referralCode);
                return false;
            }
            return true;
        } else {
            if (Integer.parseInt(referralCode) / ******** == 3) {
                return true;
            } else if (Integer.parseInt(referralCode) / ******** == 5) {
                return true;
            }
        }
        return false;
    }

    @Override
    public void bindReferralCode(Account account, String referralCode, String host, String cl, String ea, String de) {
        try {
            account.setHost("https://" + host + "?" + "al=" + "&cl=" + cl + "&ea=" + ea + "&de=" + de);
            if (!StringUtil.isNullOrEmpty(cl)) {
                account.setChannelId(Integer.parseInt(cl));
            }
            if (StringUtil.isNullOrEmpty(referralCode)) {
                //自动归属代理
                final MerchantData merchantData = DataLoginMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), account.getBusiness_no());
                if (merchantData == null) {
                    return;
                }

                final String agentAttributionOpen = merchantData.findC_GlobalValue("agentAttributionOpen");
                if (StringUtil.isNullOrEmpty(agentAttributionOpen) || !Boolean.parseBoolean(agentAttributionOpen)) {
                    return;
                }

                final String agentAttributionMethod = merchantData.findC_GlobalValue("agentAttributionMethod");
                if (StringUtil.isNullOrEmpty(agentAttributionMethod)) {
                    return;
                }

                if (Integer.parseInt(agentAttributionMethod) == 1) {//轮询
                    long id;
                    try {
                        id = RedisPoolManager.getInstance().function(cmd ->
                                cmd.sync().incr(RedisLogin.Platform_LG_Agent.getKey(account.getBusiness_no())) // 等价 incrby 1
                        );
                    } catch (Exception e) {
                        // 计数器损坏（非整数等）时，初始化为 0 再重试一次
                        RedisPoolManager.getInstance().syncConsumer(cmd ->
                                cmd.del(RedisLogin.Platform_LG_Agent.getKey(account.getBusiness_no())));
                        id = RedisPoolManager.getInstance().function(cmd ->
                                cmd.sync().incr(RedisLogin.Platform_LG_Agent.getKey(account.getBusiness_no())));
                    }

                    final int size = merchantData.getC_proxyMap().size();
                    if (id > size) {
                        RedisPoolManager.getInstance().executeAsync(commands ->
                                commands.del(RedisLogin.Platform_LG_Agent.getKey(account.getBusiness_no())));

                        id = RedisPoolManager.getInstance().function(commands ->
                                commands.sync().incr(RedisLogin.Platform_LG_Agent.getKey(account.getBusiness_no())));
                    }

                    final List<C_Agent> c_agentList = new ArrayList<>(merchantData.getC_proxyMap().values());
                    final C_Agent c_proxy = c_agentList.get((int) (id - 1));
                    if (c_proxy == null) {
                        return;
                    }
                    account.setAgentId(c_proxy.getProxyId());
                    account.setHost("https://" + host + "?" + "al=" + c_proxy.getProxyId() + "&cl=" + cl + "&ea=" + ea + "&de=" + de);
                } else {
                    final Map<Integer, C_Agent> c_proxyMap = merchantData.getC_proxyMap();
                    if (c_proxyMap.isEmpty()) {
                        return;
                    }
                    final int proxyId = getProxyWeightProxyId(c_proxyMap);
                    account.setAgentId(proxyId);
                    account.setHost("https://" + host + "?" + "al=" + proxyId + "&cl=" + cl + "&ea=" + ea + "&de=" + de);
                }
                return;
            }

            if (!StringUtils.isNumeric(referralCode)) {
                account.setReferralCode(referralCode);
                account.setHost("https://" + host + "?" + "referralCode=" + referralCode);
            } else {
                if (Integer.parseInt(referralCode) / ******** == 3) {
                    account.setAgentId(Integer.parseInt(referralCode));
                    account.setHost("https://" + host + "?" + "al=" + referralCode + "&cl=" + cl + "&ea=" + ea + "&de=" + de);
                } else if (Integer.parseInt(referralCode) / ******** == 5) {
                    account.setCurrencyTraderId(Integer.parseInt(referralCode));
                    account.setHost("https://" + host + "?" + "al=" + referralCode + "&cl=" + cl + "&ea=" + ea + "&de=" + de);
                }
            }
        } catch (Exception e) {
            LOGGER.error("bindReferralCode", e);
        }
    }

    public static int getProxyWeightProxyId(Map<Integer, C_Agent> weightMap) {
        int weight_total = 0;
        for (Map.Entry<Integer, C_Agent> entry : weightMap.entrySet()) {
            weight_total += entry.getValue().getWeight();
        }
        final int random_num = MathUtils.random(1, weight_total);
        int m = 0;
        for (Map.Entry<Integer, C_Agent> entry : weightMap.entrySet()) {
            final int weight = entry.getValue().getWeight();
            if (m <= random_num && random_num <= m + weight) {
                return entry.getValue().getProxyId();
            }
            m += weight;
        }
        return -1;
    }
}
