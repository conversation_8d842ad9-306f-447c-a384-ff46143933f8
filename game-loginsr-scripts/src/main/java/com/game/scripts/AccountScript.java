package com.game.scripts;

import com.game.engine.io.redis.RedisPoolManager;
import com.game.entity.account.Account;
import com.game.enums.redis.RedisLogin;
import com.game.loginsr.server.script.IAccountScript;
import io.netty.util.internal.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class AccountScript implements IAccountScript {
    private static final Logger LOGGER = LoggerFactory.getLogger(AccountScript.class);

    @Override
    public boolean checkReferralCode(String referralCode) {
        if (StringUtil.isNullOrEmpty(referralCode)) {
            return true;
        }

        if (!StringUtils.isNumeric(referralCode)) {
            final String superiorId = RedisPoolManager.getInstance().function(jedis ->
                    jedis.sync().hget(RedisLogin.Platform_LG_Map_InvitationID.getKey(), referralCode));
            if (StringUtil.isNullOrEmpty(superiorId)) {
                LOGGER.warn("referrerCode：{}， bind failed", referralCode);
                return false;
            }
            return true;
        } else {
            if (Integer.parseInt(referralCode) / ******** == 3) {
                return true;
            } else if (Integer.parseInt(referralCode) / ******** == 4) {
                return true;
            } else if (Integer.parseInt(referralCode) / ******** == 5) {
                return true;
            }
        }
        return false;
    }

    @Override
    public void bindReferralCode(Account account, String referralCode, String host, String ea, String de) {
        account.setHost("https://" + host);

        if (StringUtil.isNullOrEmpty(referralCode)) {
            return;
        }

        if (!StringUtils.isNumeric(referralCode)) {
            account.setReferralCode(referralCode);
            account.setHost("https://" + host + "?" + "referralCode=" + referralCode);
        } else {
            if (Integer.parseInt(referralCode) / ******** == 3) {
                account.setAgentId(Integer.parseInt(referralCode));
                account.setHost("https://" + host + "?" + "al=" + referralCode + "&ea=" + ea + "&de=" + de);
            } else if (Integer.parseInt(referralCode) / ******** == 4) {
                account.setChannelId(Integer.parseInt(referralCode));
                account.setHost("https://" + host + "?" + "al=" + referralCode + "&ea=" + ea + "&de=" + de);
            } else if (Integer.parseInt(referralCode) / ******** == 5) {
                account.setCurrencyTraderId(Integer.parseInt(referralCode));
                account.setHost("https://" + host + "?" + "al=" + referralCode + "&ea=" + ea + "&de=" + de);
            }
        }

    }
}
