package com.game.scripts.player;

import com.game.dao.player.PlayerDao;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.TimeUtil;
import com.game.engine.utils.TimeUtils;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.entity.player.activity.rechargeRecover.RechargeRecoverInfo;
import com.game.entity.player.inbox.InboxInfo;
import com.game.entity.player.stats.Stats;
import com.game.entity.player.stats.StatsInfo;
import com.game.enums.QuestType;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.mrg.quest.QuestMrg;
import com.game.hall.mrg.quest.typehandler.DailyQuestHandler;
import com.game.hall.mrg.quest.typehandler.WeeklyQuestHandler;
import com.game.hall.script.IBonusScript;
import com.game.hall.script.IInboxScript;
import com.game.hall.script.IPlayerScript;
import com.game.hall.script.IVipClubScript;
import com.game.hall.script.activity.IActivityScript;
import com.game.hall.script.activity.IDailyContestScript;
import com.game.hall.script.activity.ILuckSpinScript;
import com.game.hall.script.activity.IWeeklyRaffleScript;
import com.game.manager.EntityDaoMrg;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


public class PlayerResetScript implements IPlayerScript {
    private static final Logger LOGGER = LoggerFactory.getLogger(PlayerResetScript.class);

    @Override
    public void everyMinuteCheck() {
        PlayerMrg.getInstance().forEachPlayer(player -> {
            final long curTimeMillis = TimeUtil.currentTimeMillis();
            final long lastRefreshTime = player.getLastRefreshTime();
            //每天
            final long nextZeroTime = TimeUtil.getTimeBeginOfToday(lastRefreshTime, player.getTimeZone()) + TimeUtils.DAY;
            if (nextZeroTime < curTimeMillis) {
                // 0点事件
                if (curTimeMillis > lastRefreshTime) {
                    // 先更新时间戳，避免各种异常情况
                    player.setLastRefreshTime(curTimeMillis);
                    EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                            .updatePlayerField(player.getPlayerId(), PlayerFields.lastRefreshTime, curTimeMillis);
                }
                everyDayReset(player);
            }

            //每周
            final long nextWeekZero = TimeUtil.getTimeBeginOfWeek(lastRefreshTime, player.getTimeZone()) + TimeUtils.WEEK;
            if (nextWeekZero < curTimeMillis) {
                // 周一事件
                if (curTimeMillis > lastRefreshTime) {
                    // 先更新时间戳，避免各种异常情况
                    player.setLastRefreshTime(curTimeMillis);
                    EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                            .updatePlayerField(player.getPlayerId(), PlayerFields.lastRefreshTime, curTimeMillis);
                }
                everyWeeklyReset(player);
            }

            //每月1号
            final long nextMonth = TimeUtil.getTimeBeginOfMonth(curTimeMillis, player.getTimeZone());
            if (lastRefreshTime < nextMonth && nextMonth < curTimeMillis) {
                // 先更新时间戳，避免各种异常情况
                player.setLastRefreshTime(curTimeMillis);
                EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                        .updatePlayerField(player.getPlayerId(), PlayerFields.lastRefreshTime, curTimeMillis);
                everyMonthReset(player);
            }

            ActivityMrg.activityRest(player, 0);
            ActivityMrg.luckSpinRest(player, ActivityMrg.LUCK_SPIN_VIP);
            ActivityMrg.luckSpinRest(player, ActivityMrg.LUCK_SPIN_REFERRAL);

            ScriptLoader.getInstance().consumerScript("ReferralSpinScript",
                    (ILuckSpinScript script) -> script.referralIntervalAddTimes(player));
            ScriptLoader.getInstance().consumerScript("BonusScript",
                    (IBonusScript script) -> script.settlementBonus(player));

            ScriptLoader.getInstance().consumerScript("RewardBoxScript",
                    (IActivityScript script) -> script.resetData(player, null));
            ScriptLoader.getInstance().consumerScript("MysteryBonusScript",
                    (IActivityScript script) -> script.resetData(player, null));

            ScriptLoader.getInstance().consumerScript("InboxScript", (IInboxScript script)
                    -> script.sendDayPubMail(player));
            ScriptLoader.getInstance().consumerScript("InboxScript", (IInboxScript script)
                    -> script.sendWeeklyPubMail(player));
            ScriptLoader.getInstance().consumerScript("InboxScript", (IInboxScript script)
                    -> script.sendTimelyPubMail(player));
        });
    }

    /**
     * 不通过服务器每天定时器
     *
     * @param player
     */
    @Override
    public void everyDayReset(Player player) {
        LOGGER.info("playerId：{}，everyDayReset", player.getPlayerId());

        player.resetDaily();
        final InboxInfo inboxInfo = player.getInboxInfo();
        inboxInfo.resetDay();
        EntityDaoMrg.getInstance().getDao(PlayerDao.class).inboxDao.updateReceivedDay(player.getPlayerId(), inboxInfo);

        final DailyQuestHandler dailyQuestHandler = (DailyQuestHandler)
                QuestMrg.getInstance().getQuestTypeHandler(QuestType.DAILY.getNumber());
        dailyQuestHandler.dailyReset(player);

        final StatsInfo statsInfo = player.getStatsInfo();
        for (Stats stats : statsInfo.getStatsMap().values()) {
            stats.resetDaily();
        }
        ActivityMrg.activityRest(player, 1);
        ActivityMrg.luckSpinRest(player, ActivityMrg.LUCK_SPIN_DAILY);
        ScriptLoader.getInstance().consumerScript("DailyContestScript",
                (IDailyContestScript script) -> script.resetData(player));

        ScriptLoader.getInstance().consumerScript("VipClubScript", (IVipClubScript script) -> {
            script.vipCheckRelegation(player);
        });
        ScriptLoader.getInstance().consumerScript("VipClubScript", (IVipClubScript script) -> {
            script.resetVipSignIn(player);
        });
        ScriptLoader.getInstance().consumerScript("VipClubScript",
                (IVipClubScript script) -> script.resetDaily(player));

        ScriptLoader.getInstance().consumerScript("RedEnvelopeRainScript",
                (IActivityScript script) -> script.resetData(player, null));
        ScriptLoader.getInstance().consumerScript("PiggyBankScript",
                (IActivityScript script) -> script.resetData(player, null));
        ScriptLoader.getInstance().consumerScript("FirstChargeSignInScript",
                (IActivityScript script) -> script.resetData(player, null));

        ScriptLoader.getInstance().consumerScript("WageredRebatesScript",
                (IActivityScript script) -> script.resetData(player, null));

        ActivityMrg.continuousDepositRest(player, 1);

        final RechargeRecoverInfo rechargeRecoverInfo = player.getRechargeRecoverInfo();
        rechargeRecoverInfo.reset();
    }

    @Override
    public void everyWeeklyReset(Player player) {
        LOGGER.info("playerId：{}，everyWeeklyReset", player.getPlayerId());

        final InboxInfo inboxInfo = player.getInboxInfo();
        inboxInfo.resetWeekly();
        EntityDaoMrg.getInstance().getDao(PlayerDao.class).inboxDao.updateReceivedWeekly(player.getPlayerId(), inboxInfo);

        final StatsInfo statsInfo = player.getStatsInfo();
        for (Stats stats : statsInfo.getStatsMap().values()) {
            stats.resetWeekly();
        }

        final WeeklyQuestHandler questTypeHandler = (WeeklyQuestHandler)
                QuestMrg.getInstance().getQuestTypeHandler(QuestType.WEEKLY.getNumber());
        questTypeHandler.weeklyReset(player);

        ActivityMrg.activityRest(player, 2);
        ActivityMrg.luckSpinRest(player, ActivityMrg.LUCK_SPIN_WEEKLY);

        ScriptLoader.getInstance().consumerScript("VipClubScript",
                (IVipClubScript script) -> script.resetWeekly(player));

        ScriptLoader.getInstance().consumerScript("WeeklyRaffleScript",
                (IWeeklyRaffleScript script) -> script.resetData(player));

        ActivityMrg.continuousDepositRest(player, 2);
        ScriptLoader.getInstance().consumerScript("FirstDepositInviteBonusScript",
                (IActivityScript script) -> script.resetData(player, 2));
    }

    @Override
    public void everyMonthReset(Player player) {
        LOGGER.info("playerId：{}，everyMonthReset", player.getPlayerId());

        ScriptLoader.getInstance().consumerScript("VipClubScript",
                (IVipClubScript script) -> script.resetMonthly(player));

        ActivityMrg.continuousDepositRest(player, 3);
        ScriptLoader.getInstance().consumerScript("FirstDepositInviteBonusScript",
                (IActivityScript script) -> script.resetData(player, 3));
    }

    public static void main(String[] args) {
        final long nextMonth = TimeUtil.getTimeBeginOfMonth(TimeUtil.currentTimeMillis(), "UTC-3");
        System.out.println(nextMonth);

        final long nextWeekZero = TimeUtil.getTimeBeginOfWeek(TimeUtil.currentTimeMillis(), "UTC-3") + TimeUtils.WEEK;
        System.out.println(nextWeekZero);

        final long nextZeroTime = TimeUtil.getTimeBeginOfToday(TimeUtil.currentTimeMillis(), "UTC-3") + TimeUtils.DAY;
        System.out.println(nextZeroTime);
    }
}
