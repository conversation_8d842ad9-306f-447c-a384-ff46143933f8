package com.game.scripts.player;

import com.game.c_entity.merchant.C_FirstDepositInviteBonus;
import com.game.c_entity.middleplatform.C_BaseMerchant;
import com.game.dao.session.SessionDao;
import com.game.dao.player.PlayerDao;
import com.game.dao.player.PlayerPromoteDao;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.log.GameLog;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.*;
import com.game.entity.player.TurnoverRecord;
import com.game.entity.player.WithdrawStandard;
import com.game.entity.player.activity.firstDepositInviteBonus.FirstDepositInviteBonusInfo;
import com.game.entity.player.stats.Stats;
import com.game.entity.session.SessionsNote;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.entity.player.promote.PlayerPromote;
import com.game.entity.player.promote.PlayerPromoteFields;
import com.game.entity.player.promote.ReferralCode;
import com.game.enums.GameType;
import com.game.enums.QuestGoalType;
import com.game.enums.QuestType;
import com.game.enums.TurnoverReason;
import com.game.enums.redis.RedisLogin;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.*;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.mrg.quest.Blackboard;
import com.game.hall.mrg.quest.QuestMrg;
import com.game.hall.script.*;
import com.game.hall.script.activity.IActivityScript;
import com.game.hall.script.activity.IDailyContestScript;
import com.game.hall.script.activity.IWeeklyRaffleScript;
import com.game.manager.EntityDaoMrg;
import com.game.utils.VirtualThreadUtils;
import com.proto.HallMessage;
import io.netty.util.internal.StringUtil;
import it.unimi.dsi.fastutil.ints.IntArrayList;
import it.unimi.dsi.fastutil.ints.IntList;
import it.unimi.dsi.fastutil.longs.LongArrayList;
import it.unimi.dsi.fastutil.longs.LongList;
import it.unimi.dsi.fastutil.longs.LongLists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Update;

import java.util.Iterator;
import java.util.Map;
import java.util.Objects;

public class PlayerLoginScript implements IPlayerScript {

    private static final Logger LOGGER = LoggerFactory.getLogger(PlayerLoginScript.class);

    private void loadData(Player player) {
        loadPlayerPromote(player);
    }

    private void resetData(Player player) {
        ActivityMrg.activityClearData(player);
        HallServer.getInstance().getGameResetMrg().checkDailyReset(player);

        ScriptLoader.getInstance().consumerScript("InboxScript",
                (IInboxScript script) -> script.deleteExpireInbox(player));

        ActivityMrg.activityRest(player, 0);
        ActivityMrg.luckSpinRest(player, ActivityMrg.LUCK_SPIN_VIP);
        ActivityMrg.luckSpinRest(player, ActivityMrg.LUCK_SPIN_REFERRAL);

        ScriptLoader.getInstance().consumerScript("BonusScript",
                (IBonusScript script) -> script.settlementBonus(player));

        ScriptLoader.getInstance().consumerScript("RewardBoxScript",
                (IActivityScript script) -> script.resetData(player, null));
        ScriptLoader.getInstance().consumerScript("MysteryBonusScript",
                (IActivityScript script) -> script.resetData(player, null));
    }

    private void initData(Player player) {
        //注册任务目标
        QuestMrg.getInstance().registerGoalListeners(player);
        QuestMrg.getInstance().getQuestTypeHandler(QuestType.DAILY.getNumber())
                .checkAndAcceptQuest(player);
        QuestMrg.getInstance().getQuestTypeHandler(QuestType.WEEKLY.getNumber())
                .checkAndAcceptQuest(player);

        ScriptLoader.getInstance().consumerScript("InboxScript",
                (IInboxScript script) -> script.sendPubMail(player));

        ActivityMrg.activityInit(player);
        ActivityMrg.luckSpinInit(player);

        ScriptLoader.getInstance().consumerScript("BonusScript",
                (IBonusScript script) -> script.initData(player));
        ScriptLoader.getInstance().consumerScript("DailyContestScript",
                (IDailyContestScript script) -> script.initData(player));
        ScriptLoader.getInstance().consumerScript("WeeklyRaffleScript",
                (IWeeklyRaffleScript script) -> script.initData(player));

        ScriptLoader.getInstance().consumerScript("RedEnvelopeRainScript",
                (IActivityScript script) -> script.initData(player, null));
        ScriptLoader.getInstance().consumerScript("RewardBoxScript",
                (IActivityScript script) -> script.initData(player, null));
        ScriptLoader.getInstance().consumerScript("MysteryBonusScript",
                (IActivityScript script) -> script.initData(player, null));

        ScriptLoader.getInstance().consumerScript("ContinuousDepositScript",
                (IActivityScript script) -> script.initData(player, null));

        ScriptLoader.getInstance().consumerScript("WageredRebatesScript",
                (IActivityScript script) -> script.initData(player, null));
        ScriptLoader.getInstance().consumerScript("FirstDepositInviteBonusScript",
                (IActivityScript script) -> script.initData(player, null));
    }

    private void loadPlayerPromote(Player player) {
        PlayerPromote playerPromote = player.getPlayerPromote();
        if (playerPromote == null) {
            final ReferralCode referralCode = PlayerMrg.getInstance().createReferralCode(player, "--");
            playerPromote = PlayerPromote.createPlayerPromote(player, referralCode);
            player.setPlayerPromote(playerPromote);

            EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class)
                    .insert(playerPromote);

            player.setInvitationCode(referralCode.getCode());
            final Update update = new Update();
            update.set(PlayerFields.invitationCode, player.getInvitationCode());
            EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                    .updatePlayer(player.getPlayerId(), update);
            return;
        }

        final Update update = new Update();
        if (!Objects.equals(player.getPlayerName(), playerPromote.getPlayerName())) {
            playerPromote.setPlayerName(player.getPlayerName());
            update.set(PlayerPromoteFields.playerName, playerPromote.getPlayerName());
        }
        EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class)
                .updatePromotion(playerPromote.getPlayerId(), update);
    }

    /**
     * 先加载数据，再重置数据（一定要保证时序性）
     *
     * @param player
     * @param ip
     * @param region
     */
    @Override
    public void loginHall(Player player, String ip, String region, HallMessage.ReqPlayerEntryHallMessage req) {
        //TODO 更新玩家数据
        updatePlayerData(player, req, ip, region);
        player.setHeartbeat(TimeUtil.currentTimeMillis() + 15 * TimeUtil.MIN);

        //加载
        loadData(player);
        //重置
        resetData(player);
        //todo 正式上线 初始化玩家数据(并非玩家创建数据)
        initData(player);

        PlayerMrg.getInstance().addOnlinePlayer(player);

        //日志
        final GameLog playerLoginLog = new GameLog("platform_playerLoginLog");
        playerLoginLog.append("number", GuidGeneratorUtils.generateOrderId())
                .append("business_no", player.getBusiness_no())
                .append("site", player.getWebSite())
                .append("playerId", player.getPlayerId())
                .append("playerName", player.getPlayerName())
                .append("loginTime", player.getLoginTime())
                .append("ip", player.getIp())
                .append("channelId", player.getChannelId())
                .append("agentId", player.getAgentId())
                .append("mediaId", player.getMediaId())
                .append("adId", player.getAdId())
                .append("region", player.getRegion())
                .append("device", req.getDevice1())
                .append("model", req.getModel1())
                .append("browser", req.getBrowser())
                .append("channel", req.getChannel())//0.h5 1.pwa 2.app
                .append("logTime", TimeUtil.currentTimeMillis());
        final int registerDay = TimeUtil.getNaturalDays(TimeUtil.currentTimeMillis(), player.getCreateTime(), player.getTimeZone());
        playerLoginLog.append("registerDay", registerDay);
        HallServer.getInstance().getLogProducerMrg().send(playerLoginLog);

        //TODO 记录session
        final SessionsNote sessionsNote = new SessionsNote();
        sessionsNote.setPlayerId(player.getPlayerId());
        sessionsNote.setBusiness_no(player.getBusiness_no());
        sessionsNote.setDevice(req.getDevice1());
        sessionsNote.setLocation(StringUtil.isNullOrEmpty(player.getRegion()) ? "" : player.getRegion());
        sessionsNote.setIpAddress(player.getIp());
        EntityDaoMrg.getInstance().getDao(SessionDao.class).insert(sessionsNote);

        //TODO 重新计算上级
        if (player.getPlayerPromote().getSuperiorId() > 0 && player.getPlayerPromote().getSuperiors().isEmpty()) {
            ScriptLoader.getInstance().consumerScript("PromoteScript",
                    (IPromoteScript script) -> script.calculateSuperiors(player));
        }

        //TODO 切换打码模式
        switchTurnover(player);

        //更新任务
        QuestMrg.getInstance().updateGoal(player, QuestGoalType.DailyLogin, Blackboard.emptyBlackboard());
        //活动
        if (player.isRegister()) {
            ScriptLoader.getInstance().consumerScript("FreeGiveScript",
                    (IActivityScript script) -> script.freeGiveExecute(player, 1));
            ScriptLoader.getInstance().consumerScript("InboxScript",
                    (IInboxScript script) -> script.sendEventPubMail(player, InboxMrg.FIRST_LOGIN, false));
            player.setRegister(false);
        }
        ScriptLoader.getInstance().consumerScript("FreeGiveScript",
                (IActivityScript script) -> script.freeGiveExecute(player, 2));
        ScriptLoader.getInstance().consumerScript("FreeGiveScript",
                (IActivityScript script) -> script.freeGiveExecute(player, 3));
        //pwa
        if (req.getChannel() > 0) {
            ScriptLoader.getInstance().consumerScript("ChannelRewardScript",
                    (IChannelRewardScript script) -> script.receivePwaReward(player, player.getCurrencyId()));
        }
    }

    private void switchTurnover(Player player) {
        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }
        final String value = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "turnoverSwitch");
        if (StringUtil.isNullOrEmpty(value)) {
            return;
        }
        if (Integer.parseInt(value) == 3) {
            for (Map.Entry<Integer, WithdrawStandard> entry : player.getWithdrawStandardMap().entrySet()) {
                final int currencyId = entry.getKey();
                if (currencyId == 0) {
                    continue;
                }
                final WithdrawStandard withdrawStandard = entry.getValue();
                final double balance = CurrencyMrg.getInstance().getCurrencyValue(player, currencyId);
                final double bonus = CurrencyMrg.getInstance().getCurrencyValue(player, currencyId * 10);
                final double totalAmount = BigDecimalUtils.add(balance, bonus, 4);
                if (totalAmount == 0) {
                    withdrawStandard.reset();
                    continue;
                }
                if (withdrawStandard.getDrawStandard() > withdrawStandard.getBettingVolume()) {
                    //TODO 要求洗码量
                    ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                            (IPlayerScript script) -> script.drawStandard(player, TurnoverReason.TurnoverSwitch, currencyId, totalAmount, withdrawStandard.getDrawStandard()));

                    //TODO 实际洗码量
                    ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                            (IPlayerScript script) -> script.bettingTurnover(player, TurnoverReason.TurnoverSwitch, currencyId, withdrawStandard.getBettingVolume()));
                }
                withdrawStandard.reset();
            }
        } else {
            if (player.getTurnoverRecordMap().isEmpty()) {
                return;
            }
            final IntList updateCurrency = new IntArrayList();
            final LongList deleteOrderId = new LongArrayList();
            final Iterator<Map.Entry<Long, TurnoverRecord>> iterator = player.getTurnoverRecordMap().entrySet().iterator();
            while (iterator.hasNext()) {
                final TurnoverRecord turnoverRecord = iterator.next().getValue();
                if (turnoverRecord.getDrawStandard() == turnoverRecord.getBettingVolume()) {
                    iterator.remove();
                    deleteOrderId.add(turnoverRecord.getOrderId());
                    continue;
                }

                final WithdrawStandard withdrawStandard = player.getWithdrawStandard(turnoverRecord.getCurrencyId());
                withdrawStandard.setDrawStandard(BigDecimalUtils.add(withdrawStandard.getDrawStandard(), turnoverRecord.getDrawStandard(), 4));
                withdrawStandard.setBettingVolume(BigDecimalUtils.add(withdrawStandard.getBettingVolume(), turnoverRecord.getBettingVolume(), 4));
                updateCurrency.add(turnoverRecord.getCurrencyId());

                iterator.remove();
                deleteOrderId.add(turnoverRecord.getOrderId());
            }

            for (int currencyId : updateCurrency) {

                final double balance = CurrencyMrg.getInstance().getCurrencyValue(player, currencyId);
                final double bonus = CurrencyMrg.getInstance().getCurrencyValue(player, currencyId * 10);
                final double totalAmount = BigDecimalUtils.add(balance, bonus, 4);

                //TODO 要求洗码量
                ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                        (IPlayerScript script) -> script.drawStandard(player, TurnoverReason.TurnoverSwitch, currencyId, totalAmount, 0));

                //TODO 实际洗码量
                ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                        (IPlayerScript script) -> script.bettingTurnover(player, TurnoverReason.TurnoverSwitch, currencyId, 0));
            }

            EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                    .updateTurnoverRecord(player, LongLists.EMPTY_LIST, deleteOrderId);
        }
    }

    private void updatePlayerData(Player player, HallMessage.ReqPlayerEntryHallMessage req, String ip, String region) {
        final C_BaseMerchant c_baseMerchant = DataHallMrg.getInstance()
                .findC_BaseHostMerchant(this.getClass().getSimpleName(), req.getHost());
        if (c_baseMerchant != null) {
            player.setTimeZone(c_baseMerchant.getTimeZone());
        }
        player.setOnline(true);
        player.setLoginTime(TimeUtil.currentTimeMillis());
        player.setLastIp(StringUtil.isNullOrEmpty(player.getIp()) ? ip : player.getIp());
        player.setIp(ip);
        player.setRegion(region);
        player.setHallId(Config.SERVER_ID);
        player.setGateId(req.getGateId());
        player.setWebSite(req.getHost());
        player.setDevice(req.getDevice1());
        player.setModel(req.getModel1());
        player.setBrowser(req.getBrowser());
        player.setChannel(req.getChannel());
        player.resetGameInfo();

        ScriptLoader.getInstance().consumerScript("AgentGameScript",
                (IAgentGameScript script) -> script.statisticalGameNoteData(player));

        //重置短信发送次数
        if (!StringUtil.isNullOrEmpty(player.getAreaCode())) {
            RedisPoolManager.getInstance().executeAsync(commands ->
                    commands.del(RedisLogin.Platform_LG_Account_PhoneMsg.getKey(player.getBusiness_no(), player.getAreaCode() + "-" + player.getPhone()))
            );
        }

        final Update update = new Update();
        update.set(PlayerFields.timeZone, player.getTimeZone())
                .set(PlayerFields.online, player.isOnline())
                .set(PlayerFields.loginTime, player.getLoginTime())
                .set(PlayerFields.lastIp, player.getLastIp())
                .set(PlayerFields.ip, player.getIp())
                .set(PlayerFields.region, player.getRegion())
                .set(PlayerFields.hallId, player.getHallId())
                .set(PlayerFields.gateId, player.getGateId())
                .set(PlayerFields.webSite, player.getWebSite())
                .set(PlayerFields.device, player.getDevice())
                .set(PlayerFields.model, player.getModel())
                .set(PlayerFields.browser, player.getBrowser())
                .set(PlayerFields.channel, player.getChannel())
                .set(PlayerFields.gameId, player.getGameId())
                .set(PlayerFields.bonus, player.isBonus())
                .set(PlayerFields.platformId, player.getPlatformId())
                .set(PlayerFields.gameType, player.getGameType())
                .set(PlayerFields.gameCurrencyId, player.getGameCurrencyId())
                .set(PlayerFields.playerCurrencyId, player.getPlayerCurrencyId());

        //TODO 兼容币种
        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData != null) {
            if (!merchantData.getC_currencyMap().containsKey(player.getCurrencyId())) {
                final String currencyId = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "initCurrencyId");
                if (!StringUtil.isNullOrEmpty(currencyId)) {
                    player.setCurrencyId(Integer.parseInt(currencyId));
                    update.set(PlayerFields.currencyId, player.getCurrencyId());
                    if (player.getCurrencyId() / 1000 == 1) {
                        player.setViewFiat(Integer.parseInt(currencyId));
                        update.set(PlayerFields.viewFiat, player.getViewFiat());
                    }
                }
            }
        }

        EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                .updatePlayer(player.getPlayerId(), update);
    }
}

