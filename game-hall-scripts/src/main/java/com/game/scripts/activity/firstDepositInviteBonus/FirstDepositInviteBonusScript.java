package com.game.scripts.activity.firstDepositInviteBonus;

import com.game.c_entity.merchant.C_Activity;
import com.game.c_entity.merchant.C_FirstDepositInviteBonus;
import com.game.c_entity.middleplatform.C_BaseCurrency;
import com.game.dao.activity.FirstDepositInviteNoteDao;
import com.game.dao.player.PlayerDao;
import com.game.engine.log.GameLog;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.GuidGeneratorUtils;
import com.game.engine.utils.TimeUtil;
import com.game.entity.activity.FirstDepositInviteNote;
import com.game.entity.player.Player;
import com.game.entity.player.activity.firstDepositInviteBonus.FirstDepositInviteBonusInfo;
import com.game.entity.player.activity.firstDepositInviteBonus.InviteRecharge;
import com.game.entity.player.promote.PlayerPromote;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.InboxMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.IInboxScript;
import com.game.hall.script.activity.IActivityScript;
import com.game.manager.EntityDaoMrg;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

public class FirstDepositInviteBonusScript implements IActivityScript {
    private static final Logger LOGGER = LoggerFactory.getLogger(FirstDepositInviteBonusScript.class);

    @Override
    public void initData(Player player, C_Activity c_activity) {
        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }

        final C_FirstDepositInviteBonus c_firstDepositInviteBonus = merchantData.findC_FirstDepositInviteBonus(this.getClass().getSimpleName(), ActivityMrg.FIRSTDEPOSIT_INIVTEBONUS);
        if (c_firstDepositInviteBonus == null) {
            return;
        }

        final FirstDepositInviteBonusInfo firstDepositInviteBonusInfo = player.getFirstDepositInviteBonusInfo();
        if (firstDepositInviteBonusInfo.isStart() && firstDepositInviteBonusInfo.getC_id() == c_firstDepositInviteBonus.getC_id()) {
            return;
        }

        firstDepositInviteBonusInfo.reset();
        firstDepositInviteBonusInfo.setStart(true);
        firstDepositInviteBonusInfo.setC_id(c_firstDepositInviteBonus.getC_id());

        EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                .updateFirstDepositInviteBonusInfo(player.getPlayerId(), firstDepositInviteBonusInfo);
    }

    @Override
    public void resetData(Player player, int cycleType) {
        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }

        final C_FirstDepositInviteBonus c_firstDepositInviteBonus = merchantData.findC_FirstDepositInviteBonus(this.getClass().getSimpleName(), ActivityMrg.FIRSTDEPOSIT_INIVTEBONUS);
        if (c_firstDepositInviteBonus == null) {
            return;
        }

        if (c_firstDepositInviteBonus.getCycleType() != cycleType) {
            return;
        }

        if (c_firstDepositInviteBonus.getCycleType() == 1) {
            if (TimeUtil.currentTimeMillis() < c_firstDepositInviteBonus.getLimitTimeEnd()) {
                return;
            }
        }

        final FirstDepositInviteBonusInfo firstDepositInviteBonusInfo = player.getFirstDepositInviteBonusInfo();

        firstDepositInviteBonusInfo.reset();
        initData(player, null);
    }

    @Override
    public void execute(Player player, int currencyId, double progress) {
        initData(player, null);

        final FirstDepositInviteBonusInfo firstDepositInviteBonusInfo = player.getFirstDepositInviteBonusInfo();
        if (!firstDepositInviteBonusInfo.isStart()) {
            return;
        }

        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }

        final C_FirstDepositInviteBonus c_firstDepositInviteBonus = merchantData.findC_FirstDepositInviteBonus(this.getClass().getSimpleName(), ActivityMrg.FIRSTDEPOSIT_INIVTEBONUS);
        if (c_firstDepositInviteBonus == null) {
            return;
        }

        if (c_firstDepositInviteBonus.getCycleType() == 1) {
            if ((TimeUtil.currentTimeMillis() < c_firstDepositInviteBonus.getLimitTimeStart())
                    || (TimeUtil.currentTimeMillis() >= c_firstDepositInviteBonus.getLimitTimeEnd())) {
                return;
            }
        }

        final Optional<InviteRecharge> optional = firstDepositInviteBonusInfo.getInviteRechargeMap().values()
                .stream().filter(inviteRecharge -> !inviteRecharge.isFinish()).findFirst();
        if (optional.isEmpty()) {
            return;
        }

        final InviteRecharge inviteRecharge = optional.get();
        inviteRecharge.incWagered(currencyId, progress);
    }

    @Override
    public void execute(Player player, int currencyId, double wageredAmount, double rechargeAmount) {
        initData(player, null);

        final PlayerPromote playerPromote = player.getPlayerPromote();
        if (playerPromote.getSuperiorId() == 0) {
            return;
        }

        final Player superiorPlayer = PlayerMrg.getInstance().findDbPlayer(playerPromote.getSuperiorId());
        if (superiorPlayer == null) {
            return;
        }

        final FirstDepositInviteBonusInfo firstDepositInviteBonusInfo = superiorPlayer.getFirstDepositInviteBonusInfo();
        if (!firstDepositInviteBonusInfo.isStart()) {
            return;
        }

        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }

        final C_FirstDepositInviteBonus c_firstDepositInviteBonus = merchantData.findC_FirstDepositInviteBonus(this.getClass().getSimpleName(), ActivityMrg.FIRSTDEPOSIT_INIVTEBONUS);
        if (c_firstDepositInviteBonus == null) {
            return;
        }

        if (c_firstDepositInviteBonus.getCycleType() == 1) {
            if ((TimeUtil.currentTimeMillis() < c_firstDepositInviteBonus.getLimitTimeStart())
                    || (TimeUtil.currentTimeMillis() >= c_firstDepositInviteBonus.getLimitTimeEnd())) {
                return;
            }
        }

        if (c_firstDepositInviteBonus.getCycleType() == 1) {
            if (player.getTotalRechargeTimes() > 1) {
                return;
            }
        } else if (c_firstDepositInviteBonus.getCycleType() == 2) {
            if (player.getWeeklyRechargeTimes() > 1) {
                return;
            }
        } else {
            if (player.getMonthlyRechargeTimes() > 1) {
                return;
            }
        }

        if (rechargeAmount < c_firstDepositInviteBonus.getNeedChargeAmount()) {
            return;
        }

        if (rechargeAmount > 0) {
            firstDepositInviteBonusInfo.incCharge(currencyId, rechargeAmount);

            final InviteRecharge inviteRecharge = new InviteRecharge();
            inviteRecharge.setPlayerId(player.getPlayerId());
            inviteRecharge.setCurrencyId(currencyId);
            inviteRecharge.setVipLevel(superiorPlayer.getVipClub().getVipLevel());
            inviteRecharge.setFirstDepositTime(TimeUtil.currentTimeMillis());
            inviteRecharge.incCharge(currencyId, rechargeAmount);
            firstDepositInviteBonusInfo.getInviteRechargeMap().put(player.getPlayerId(), inviteRecharge);

            EntityDaoMrg.getInstance().getDao(PlayerDao.class).firstDepositInviteBonusDao
                    .updateFirstDepositInviteBonus(superiorPlayer.getPlayerId(), firstDepositInviteBonusInfo);

            final FirstDepositInviteNote firstDepositInviteNote = new FirstDepositInviteNote();
            firstDepositInviteNote.setBusiness_no(player.getBusiness_no());
            firstDepositInviteNote.setPlayerId(player.getPlayerId());
            firstDepositInviteNote.setCurrencyId(currencyId);
            firstDepositInviteNote.setChargeAmount(rechargeAmount);
            firstDepositInviteNote.setCreateTime(TimeUtil.currentTimeMillis());
            EntityDaoMrg.getInstance().getDao(FirstDepositInviteNoteDao.class).insert(firstDepositInviteNote);

            double bonus = BigDecimalUtils.mul(rechargeAmount, c_firstDepositInviteBonus.getRewardRate(), 4);
            if (bonus >= c_firstDepositInviteBonus.getMaxRewardAmount()) {
                bonus = c_firstDepositInviteBonus.getMaxRewardAmount();
            }

            final GameLog playerActivityLog = new GameLog("platform_playerFirstDepositSubordinateBonusLog");
            playerActivityLog.append("number", GuidGeneratorUtils.generateOrderId())
                    .append("business_no", player.getBusiness_no())
                    .append("site", player.getWebSite())
                    .append("channel", player.getChannel())
                    .append("playerId", player.getPlayerId())
                    .append("playerName", player.getPlayerName())
                    .append("language", player.getLanguage())
                    .append("agentId", player.getAgentId())
                    .append("channelId", player.getChannelId())
                    .append("region", player.getRegisterRegion())
                    .append("superiorId", superiorPlayer.getPlayerId())
                    .append("activityId", ActivityMrg.FIRSTDEPOSIT_INIVTEBONUS)
                    .append("activityUniqueId", c_firstDepositInviteBonus.getC_id())
                    .append("currencyId", currencyId)
                    .append("bonus", bonus)
                    .append("turnoverMul", c_firstDepositInviteBonus.getTurnoverMul())
                    .append("logTime", TimeUtil.currentTimeMillis());
            if (c_firstDepositInviteBonus.getCycleType() == 1) {//时间
                playerActivityLog.append("startTime", c_firstDepositInviteBonus.getLimitTimeStart());
                playerActivityLog.append("endTime", c_firstDepositInviteBonus.getLimitTimeEnd());
            } else if (c_firstDepositInviteBonus.getCycleType() == 2) {//每周
                final long startTime = TimeUtil.getTimeBeginOfWeek(TimeUtil.currentTimeMillis(), player.getTimeZone());
                final long endTime = TimeUtil.getTimeEndOfWeek(TimeUtil.currentTimeMillis(), player.getTimeZone());
                playerActivityLog.append("startTime", startTime);
                playerActivityLog.append("endTime", endTime);
            } else if (c_firstDepositInviteBonus.getCycleType() == 3) {//每月
                final long startTime = TimeUtil.getTimeBeginOfMonth(TimeUtil.currentTimeMillis(), player.getTimeZone());
                final long endTime = TimeUtil.getTimeEndOfMonth(TimeUtil.currentTimeMillis(), player.getTimeZone());
                playerActivityLog.append("startTime", startTime);
                playerActivityLog.append("endTime", endTime);
            }
            HallServer.getInstance().getLogProducerMrg().send(playerActivityLog);

            final C_BaseCurrency c_baseCurrency = DataHallMrg.getInstance().findC_BaseCurrency(this.getClass().getSimpleName(), currencyId);
            final List<String> params = new ArrayList<>();
            if (c_baseCurrency != null) {
                params.add(c_baseCurrency.getSymbol());
            }
            params.add("" + bonus);
            ScriptLoader.getInstance().consumerScript("InboxScript",
                    (IInboxScript script) -> script.sendInboxMail(superiorPlayer, InboxMrg.FIRST_DEPOSIT_INVITE_BONUS_1, params));
        }
    }
}
