package com.game.scripts.activity;

import com.game.c_entity.merchant.C_Activity;
import com.game.c_entity.middleplatform.C_BaseExchangeRate;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.ScriptLoader;
import com.game.engine.util.misc.Tuple2;
import com.game.engine.utils.FastCollectionsUtils;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.entity.player.activity.ActivityData;
import com.game.entity.player.activity.ActivityInfo;
import com.game.enums.*;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.mrg.currency.RewardRequest;
import com.game.hall.script.IBonusScript;
import com.game.hall.script.IPlayerScript;
import it.unimi.dsi.fastutil.ints.Int2DoubleMap;
import it.unimi.dsi.fastutil.objects.ObjectIterator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 结算周期 无 每日每周
 * 每日只有每档
 * 每周只有最高
 * 指定结算时间点
 */
public class CompensateScript extends ActivityScript {
    private static final Logger LOGGER = LoggerFactory.getLogger(CompensateScript.class);

    @Override
    public void initData(Player player, C_Activity c_activity) {
        if (TimeUtil.currentTimeMillis() < c_activity.getStartTime() || TimeUtil.currentTimeMillis() >= c_activity.getEndTime()) {
            return;
        }

        if (c_activity.channelLimit(player.getChannelId())) {
            return;
        }

        if (c_activity.agentLimit(player.getAgentId())) {
            return;
        }

        final ActivityInfo activityInfo = player.getActivityInfo();
        final int activityId = Integer.parseInt(c_activity.getActivityId() + "" + c_activity.getActivitySubType());
        ActivityData activityData = activityInfo.getActivityData(activityId);
        if (activityData == null) {
            activityData = new ActivityData();
            activityInfo.getActivityDataMap().put(activityId, activityData);
        }

        if (activityData.isStart() && c_activity.getC_id() == activityData.getC_id()) {
            return;
        }

        activityData.reset();
        activityData.setStart(true);
        activityData.setC_id(c_activity.getC_id());
        if (c_activity.getActivitySubType() == 1) {//每日 必须整点
            final long endTime = TimeUtil.getTimeBeginOfToday(TimeUtil.currentTimeMillis(), c_activity.getTimeZone()) + c_activity.getCompensateTime();
            if (TimeUtil.currentTimeMillis() < endTime) {
                activityData.getEndTimeMap().put(c_activity.getActivitySubType(), endTime);
            } else {
                activityData.getEndTimeMap().put(c_activity.getActivitySubType(),
                        TimeUtil.getTimeBeginOfNextDay(TimeUtil.currentTimeMillis(), c_activity.getTimeZone()) + c_activity.getCompensateTime());
            }
        } else {
            final long endTime = TimeUtil.getTimeBeginOfWeek(TimeUtil.currentTimeMillis(), c_activity.getTimeZone()) + c_activity.getCompensateTime();
            if (TimeUtil.currentTimeMillis() < endTime) {
                activityData.getEndTimeMap().put(c_activity.getActivitySubType(), endTime);
            } else {
                activityData.getEndTimeMap().put(c_activity.getActivitySubType(),
                        TimeUtil.getTimeBeginOfWeek(TimeUtil.currentTimeMillis(), c_activity.getTimeZone()) + 7 * TimeUtil.DAY + c_activity.getCompensateTime());
            }
        }
    }

    @Override
    public void clearData(Player player, C_Activity c_activity) {
        final ActivityInfo activityInfo = player.getActivityInfo();
        final int activityId = Integer.parseInt(c_activity.getActivityId() + "" + c_activity.getActivitySubType());
        final ActivityData activityData = activityInfo.getActivityData(activityId);
        if (activityData == null) {
            return;
        }

        final long endTime = activityData.getLastEndTimeMap().getOrDefault(c_activity.getActivitySubType(), 0L);
        if (endTime == 0L) {
            return;
        }

        if (TimeUtil.currentTimeMillis() < endTime + c_activity.getAwardClearTime()) {
            return;
        }

        activityData.getBonusMap().clear();
        activityData.getTurnoverMap().clear();
        activityData.getLastEndTimeMap().clear();
    }

    @Override
    public void resetData(Player player, C_Activity c_activity) {
        final ActivityInfo activityInfo = player.getActivityInfo();
        final int activityId = Integer.parseInt(c_activity.getActivityId() + "" + c_activity.getActivitySubType());
        final ActivityData activityData = activityInfo.getActivityData(activityId);
        if (activityData == null) {
            return;
        }

        if (!activityData.isStart()) {
            return;
        }

        final long endTime = activityData.getEndTime(c_activity.getActivitySubType());
        if (TimeUtil.currentTimeMillis() < endTime) {
            return;
        }

        if (c_activity.getReceive() == 2) {//自动
            final List<C_Activity.RewardInfo> rewardInfos = allReward(c_activity, activityData, activityData.getProgressMap());

            final RewardRequest rewardRequest = reward(player, activityData, activityData.getProgressMap(), c_activity, rewardInfos);

            if (!rewardRequest.getCurrencyMap().isEmpty()) {
                final RewardReason rewardReason = RewardReason.Activity_Compensate;
                rewardReason.setSource(c_activity.getActivitySubType() + "");
                CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getBonusCurrencyMap(), rewardReason);

                //TODO 活动日志
                sendActivityLog(player, 2, c_activity, rewardRequest);

                TransactionFrom transaction;
                if (c_activity.getActivitySubType() == 1) {//每日
                    transaction = TransactionFrom.Promotion_DailyRelief;
                } else {
                    transaction = TransactionFrom.Promotion_WeeklyRelief;
                }
                final Tuple2<Integer, Double> tuple2 = rewardRequest.getReward();
                ScriptLoader.getInstance().consumerScript("BonusScript", (IBonusScript script) ->
                        script.addBonusNote(transaction, player, tuple2.getFirst(), tuple2.getSecond()));
            }
        } else { //手动
            activityData.getLastEndTimeMap().put(c_activity.getActivitySubType(), endTime);

            int currencyId = player.getCurrencyId();
            if (c_activity.isEvUsd()) {
                currencyId = Currency.USD.getCurrencyId();
            }
            final List<C_Activity.RewardInfo> rewardInfos = reward(currencyId, c_activity, activityData, activityData.getProgressMap());
            final RewardRequest rewardRequest = manualReward(player, activityData, c_activity, rewardInfos);
            for (ObjectIterator<Int2DoubleMap.Entry> itr = FastCollectionsUtils.fastIterator(rewardRequest.getBonusCurrencyMap()); itr.hasNext(); ) {
                final Int2DoubleMap.Entry entry = itr.next();

                final double progress = activityData.getBonusMap().getOrDefault(entry.getIntKey(), 0d);
                activityData.getBonusMap().put(entry.getIntKey(), BigDecimalUtils.add(progress, entry.getDoubleValue(), 4));
            }
        }
        activityData.resetProcess();

        initData(player, c_activity);
    }

    public RewardRequest manualReward(Player player, ActivityData activityData, C_Activity c_activity, List<C_Activity.RewardInfo> rewardInfos) {
        final RewardRequest rewardRequest = new RewardRequest();

        double progress = 0;
        if (c_activity.isEvUsd()) {
            for (Map.Entry<Integer, Double> entry : activityData.getProgressMap().entrySet()) {
                final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), entry.getKey());
                if (c_baseExchangeRate == null) {
                    continue;
                }
                progress = BigDecimalUtils.add(progress, BigDecimalUtils.mul(entry.getValue(), c_baseExchangeRate.getExchangeRate(), 9), 9);
            }
        } else {
            progress = activityData.getProgressMap().getOrDefault(player.getCurrencyId(), 0d);
        }

        if (progress > 0) {
            progress = 0;
        } else {
            progress = Math.abs(progress);
        }

        for (C_Activity.RewardInfo rewardInfo : rewardInfos) {
            if (rewardInfo.getRewardType() == 1) {
                rewardRequest.addCurrency(rewardInfo.rewardCurrency, rewardInfo.reward);

                activityData.incTurnover(rewardInfo.rewardCurrency,
                        BigDecimalUtils.mul(rewardInfo.reward, rewardInfo.turnoverMul, 4));
            } else {
                if (!c_activity.isEvUsd()) {
                    double value = BigDecimalUtils.mul(progress, rewardInfo.reward, 9);
                    if (value > 0) {
                        if (value >= rewardInfo.maxReward) {
                            value = rewardInfo.maxReward;
                        }
                        rewardRequest.addCurrency(rewardInfo.rewardCurrency, value);

                        activityData.incTurnover(rewardInfo.rewardCurrency,
                                BigDecimalUtils.mul(value, rewardInfo.turnoverMul, 4));
                    }
                } else {
                    final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), rewardInfo.rewardCurrency);
                    if (c_baseExchangeRate != null) {
                        double value = BigDecimalUtils.mul(progress, rewardInfo.reward, 9);
                        if (value > 0) {
                            if (value >= rewardInfo.maxReward) {
                                value = rewardInfo.maxReward;
                            }
                            final double exchange = BigDecimalUtils.div(1, c_baseExchangeRate.getExchangeRate(), 9);
                            rewardRequest.addCurrency(rewardInfo.rewardCurrency, BigDecimalUtils.mul(value, exchange, 9));

                            activityData.incTurnover(rewardInfo.rewardCurrency,
                                    BigDecimalUtils.mul(BigDecimalUtils.mul(value, exchange, 9), rewardInfo.turnoverMul, 4));
                        }
                    }
                }
            }
        }
        return rewardRequest;
    }

    @Override
    public RewardRequest receiveReward(Player player, C_Activity c_activity) {
        final ActivityInfo activityInfo = player.getActivityInfo();
        final int activityId = Integer.parseInt(c_activity.getActivityId() + "" + c_activity.getActivitySubType());
        final ActivityData activityData = activityInfo.getActivityData(activityId);
        if (activityData == null) {
            return new RewardRequest();
        }

        int currencyId = player.getCurrencyId();
        if (c_activity.isEvUsd()) {
            currencyId = Currency.USD.getCurrencyId();
        }
        final List<C_Activity.RewardInfo> rewardInfos = reward(currencyId, c_activity, activityData, activityData.getProgressMap());

        final RewardRequest rewardRequest = reward(player, activityData, activityData.getProgressMap(), c_activity, rewardInfos);

        final RewardReason rewardReason = RewardReason.Activity_Compensate;
        rewardReason.setSource(c_activity.getActivitySubType() + "");
        CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getBonusCurrencyMap(), rewardReason);

        return rewardRequest;
    }

    @Override
    public RewardRequest receiveSettlementReward(Player player, C_Activity c_activity) {
        final ActivityInfo activityInfo = player.getActivityInfo();
        final int activityId = Integer.parseInt(c_activity.getActivityId() + "" + c_activity.getActivitySubType());
        final ActivityData activityData = activityInfo.getActivityData(activityId);
        if (activityData == null) {
            return new RewardRequest();
        }

        if (activityData.getBonusMap().isEmpty()) {
            return new RewardRequest();
        }

        final RewardRequest rewardRequest = new RewardRequest();
        for (Map.Entry<Integer, Double> entry : activityData.getBonusMap().entrySet()) {
            rewardRequest.addCurrency(entry.getKey() / 10, entry.getValue());
        }

        final RewardReason rewardReason = RewardReason.Activity_Compensate;
        rewardReason.setSource(c_activity.getActivitySubType() + "");
        CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getBonusCurrencyMap(), rewardReason);

        for (Map.Entry<Integer, Double> entry : activityData.getTurnoverMap().entrySet()) {
            final double amount = activityData.getBonusMap().getOrDefault(entry.getKey() * 10, 0d);
            if (amount == 0) {
                continue;
            }
            final TurnoverReason turnoverReason = TurnoverReason.Activity_Compensate;
            turnoverReason.setSource(c_activity.getActivitySubType() + "");
            ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                    (IPlayerScript script) -> script.drawStandard(player, turnoverReason, entry.getKey(), amount, entry.getValue()));
        }

        activityData.getBonusMap().clear();
        activityData.getTurnoverMap().clear();
        return rewardRequest;
    }

    @Override
    public void execute(Player player, int currencyId, double progress) {
        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }

        final List<C_Activity> c_activityList = merchantData.getC_activityListMap().get(ActivityMrg.COMPENSATE);
        if (c_activityList == null || c_activityList.isEmpty()) {
            return;
        }

        for (C_Activity c_activity : c_activityList) {
            if (TimeUtil.currentTimeMillis() < c_activity.getStartTime() || TimeUtil.currentTimeMillis() >= c_activity.getEndTime()) {
                continue;
            }

            if (!checkActivityJoin(player, currencyId, c_activity)) {
                continue;
            }

            initData(player, c_activity);

            final ActivityInfo activityInfo = player.getActivityInfo();
            final int activityId = Integer.parseInt(c_activity.getActivityId() + "" + c_activity.getActivitySubType());
            final ActivityData activityData = activityInfo.getActivityData(activityId);

            final double currProgress = BigDecimalUtils.add(activityData.getProgressMap().getOrDefault(currencyId, 0d), progress, 4);
            activityData.getProgressMap().put(currencyId, currProgress);

            if (c_activity.isNowReceive() && c_activity.getReceive() == 2) {
                final List<C_Activity.RewardInfo> rewardInfos = reward(currencyId, c_activity, activityData, activityData.getProgressMap());

                final RewardRequest rewardRequest = reward(player, activityData, activityData.getProgressMap(), c_activity, rewardInfos);

                final RewardReason rewardReason = RewardReason.Activity_Compensate;
                rewardReason.setSource(c_activity.getActivitySubType() + "");
                CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getBonusCurrencyMap(), rewardReason);

                //TODO 活动日志
                sendActivityLog(player, 2, c_activity, rewardRequest);

                if (!rewardRequest.getCurrencyMap().isEmpty()) {
                    TransactionFrom transaction;
                    if (c_activity.getActivitySubType() == 1) {//每日
                        transaction = TransactionFrom.Promotion_DailyRelief;
                    } else {
                        transaction = TransactionFrom.Promotion_WeeklyRelief;
                    }
                    final Tuple2<Integer, Double> tuple2 = rewardRequest.getReward();
                    ScriptLoader.getInstance().consumerScript("BonusScript", (IBonusScript script) ->
                            script.addBonusNote(transaction, player, tuple2.getFirst(), tuple2.getSecond()));
                }
            }
        }
    }

    @Override
    List<C_Activity.RewardInfo> reward(int currencyId, C_Activity c_activity, ActivityData activityData, Map<Integer, Double> progressMap) {
        final List<C_Activity.RewardInfo> rewardInfos = new ArrayList<>();
        double progress = 0;
        if (c_activity.isEvUsd()) {
            currencyId = Currency.USD.getCurrencyId();
            for (Map.Entry<Integer, Double> entry : progressMap.entrySet()) {
                final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), entry.getKey());
                if (c_baseExchangeRate == null) {
                    continue;
                }
                progress = BigDecimalUtils.add(progress, BigDecimalUtils.mul(entry.getValue(), c_baseExchangeRate.getExchangeRate(), 9), 9);
            }
        } else {
            progress = progressMap.getOrDefault(currencyId, 0d);
        }

        if (progress > 0) {
            progress = 0;
        } else {
            progress = Math.abs(progress);
        }

        final Set<Integer> rewards = activityData.getReceiveRewards(currencyId);
        if (c_activity.getRewardType() == 1) {//每档
            final List<C_Activity.RewardInfo> rechargeList = c_activity.findRewardsList(rewards, currencyId, progress);
            if (rechargeList.isEmpty()) {
                return rewardInfos;
            }
            rewardInfos.addAll(rechargeList);
            return rewardInfos;
        } else {//最高档
            if (!rewards.isEmpty()) {
                return rewardInfos;
            }
            final C_Activity.RewardInfo recharge = c_activity.findRewardInfo(currencyId, progress);
            if (recharge == null) {
                return rewardInfos;
            }
            rewardInfos.add(recharge);
        }
        return rewardInfos;
    }

    @Override
    public RewardRequest reward(Player player, ActivityData activityData, Map<Integer, Double> progressMap, C_Activity c_activity, List<C_Activity.RewardInfo> rewardInfos) {
        final RewardRequest rewardRequest = new RewardRequest();

        double progress = 0;
        if (c_activity.isEvUsd()) {
            for (Map.Entry<Integer, Double> entry : progressMap.entrySet()) {
                final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), entry.getKey());
                if (c_baseExchangeRate == null) {
                    continue;
                }
                progress = BigDecimalUtils.add(progress, BigDecimalUtils.mul(entry.getValue(), c_baseExchangeRate.getExchangeRate(), 9), 9);
            }
        } else {
            progress = progressMap.getOrDefault(player.getCurrencyId(), 0d);
        }

        if (progress > 0) {
            progress = 0;
        } else {
            progress = Math.abs(progress);
        }

        for (C_Activity.RewardInfo rewardInfo : rewardInfos) {
            final Set<Integer> receiveRewards = activityData.getReceiveRewards(rewardInfo.currencyId);
            receiveRewards.add(rewardInfo.getId());
            if (rewardInfo.getRewardType() == 1) {
                rewardRequest.addCurrency(rewardInfo.rewardCurrency, rewardInfo.reward);

                //打码量
//                final WithdrawStandard withdrawStandard = player.getWithdrawStandard(rewardInfo.rewardCurrency);
//                withdrawStandard.incDrawStandard(BigDecimalUtils.mul(rewardInfo.reward, rewardInfo.turnoverMul, 4));
                final TurnoverReason turnoverReason = TurnoverReason.Activity_Compensate;
                turnoverReason.setSource(c_activity.getActivitySubType() + "");
                ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                        (IPlayerScript script) -> script.drawStandard(player, turnoverReason, rewardInfo.rewardCurrency, rewardInfo.reward,
                                BigDecimalUtils.mul(rewardInfo.reward, rewardInfo.turnoverMul, 4)));

            } else {
                final double receiveAmount = activityData.getReceiveAmountMap().getOrDefault(rewardInfo.rewardCurrency, 0d);
                if (!c_activity.isEvUsd()) {
                    double value = BigDecimalUtils.sub(BigDecimalUtils.mul(progress, rewardInfo.reward, 9), receiveAmount, 9);
                    if (value > 0) {
                        if (value >= rewardInfo.maxReward) {
                            value = rewardInfo.maxReward;
                        }
                        rewardRequest.addCurrency(rewardInfo.rewardCurrency, value);
                        activityData.incReceiveAmount(rewardInfo.rewardCurrency, value);

                        //打码量
//                        final WithdrawStandard withdrawStandard = player.getWithdrawStandard(rewardInfo.rewardCurrency);
//                        withdrawStandard.incDrawStandard(BigDecimalUtils.mul(value, rewardInfo.turnoverMul, 4));
                        final TurnoverReason turnoverReason = TurnoverReason.Activity_Compensate;
                        turnoverReason.setSource(c_activity.getActivitySubType() + "");
                        final double finalValue = value;
                        ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                                (IPlayerScript script) -> script.drawStandard(player, turnoverReason, rewardInfo.rewardCurrency, finalValue,
                                        BigDecimalUtils.mul(finalValue, rewardInfo.turnoverMul, 4)));
                    }
                } else {
                    final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), rewardInfo.rewardCurrency);
                    if (c_baseExchangeRate != null) {
                        double value = BigDecimalUtils.sub(BigDecimalUtils.mul(progress, rewardInfo.reward, 9), BigDecimalUtils.mul(receiveAmount, c_baseExchangeRate.getExchangeRate(), 9), 9);
                        if (value > 0) {
                            if (value >= rewardInfo.maxReward) {
                                value = rewardInfo.maxReward;
                            }
                            final double exchange = BigDecimalUtils.div(1, c_baseExchangeRate.getExchangeRate(), 9);
                            rewardRequest.addCurrency(rewardInfo.rewardCurrency, BigDecimalUtils.mul(value, exchange, 9));
                            activityData.incReceiveAmount(rewardInfo.rewardCurrency, BigDecimalUtils.mul(value, exchange, 9));

                            //打码量
//                            final WithdrawStandard withdrawStandard = player.getWithdrawStandard(rewardInfo.rewardCurrency);
//                            withdrawStandard.incDrawStandard(BigDecimalUtils.mul(BigDecimalUtils.mul(value, exchange, 9), rewardInfo.turnoverMul, 4));
                            final TurnoverReason turnoverReason = TurnoverReason.Activity_Compensate;
                            turnoverReason.setSource(c_activity.getActivitySubType() + "");
                            final double finalValue = value;
                            ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                                    (IPlayerScript script) -> script.drawStandard(player, turnoverReason, rewardInfo.rewardCurrency, BigDecimalUtils.mul(finalValue, exchange, 9),
                                            BigDecimalUtils.mul(BigDecimalUtils.mul(finalValue, exchange, 9), rewardInfo.turnoverMul, 4)));
                        }
                    }
                }
            }
        }
        return rewardRequest;
    }

    @Override
    List<C_Activity.RewardInfo> allReward(C_Activity c_activity, ActivityData activityData, Map<Integer, Double> progressMap) {
        final List<C_Activity.RewardInfo> rewardInfoList = new ArrayList<>();
        if (c_activity.getRewardType() == 1) {//每档
            if (!c_activity.isEvUsd()) {
                for (Map.Entry<Integer, Double> entry : progressMap.entrySet()) {
                    final int currencyId = entry.getKey();
                    double progress = entry.getValue();

                    if (progress > 0) {
                        progress = 0;
                    } else {
                        progress = Math.abs(progress);
                    }

                    final Set<Integer> rewards = activityData.getReceiveRewards(currencyId);
                    final List<C_Activity.RewardInfo> rechargeList = c_activity.findRewardsList(rewards, currencyId, progress);
                    if (rechargeList.isEmpty()) {
                        continue;
                    }

                    rewardInfoList.addAll(rechargeList);
                }
            } else {
                double progress = 0;
                for (Map.Entry<Integer, Double> entry : progressMap.entrySet()) {
                    final int currencyId = entry.getKey();
                    final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), currencyId);
                    if (c_baseExchangeRate == null) {
                        continue;
                    }
                    progress = BigDecimalUtils.add(progress, BigDecimalUtils.mul(progress, c_baseExchangeRate.getExchangeRate(), 9), 9);
                }

                if (progress > 0) {
                    progress = 0;
                } else {
                    progress = Math.abs(progress);
                }

                final Set<Integer> rewards = activityData.getReceiveRewards(Currency.USD.getCurrencyId());
                final List<C_Activity.RewardInfo> rechargeList = c_activity.findRewardsList(rewards, Currency.USD.getCurrencyId(), progress);

                rewardInfoList.addAll(rechargeList);
            }
        } else {//最高档
            if (!c_activity.isEvUsd()) {
                for (Map.Entry<Integer, Double> entry : progressMap.entrySet()) {
                    final int currencyId = entry.getKey();
                    double progress = entry.getValue();

                    if (progress > 0) {
                        progress = 0;
                    } else {
                        progress = Math.abs(progress);
                    }

                    final C_Activity.RewardInfo recharge = c_activity.findRewardInfo(currencyId, progress);
                    if (recharge == null) {
                        continue;
                    }
                    rewardInfoList.add(recharge);
                }
            } else {
                double progress = 0;
                for (Map.Entry<Integer, Double> entry : progressMap.entrySet()) {
                    final int currencyId = entry.getKey();
                    final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), currencyId);
                    if (c_baseExchangeRate == null) {
                        continue;
                    }
                    progress = BigDecimalUtils.add(progress, BigDecimalUtils.mul(progress, c_baseExchangeRate.getExchangeRate(), 9), 9);
                }

                if (progress > 0) {
                    progress = 0;
                } else {
                    progress = Math.abs(progress);
                }

                final C_Activity.RewardInfo recharge = c_activity.findRewardInfo(Currency.USD.getCurrencyId(), progress);
                if (recharge != null) {
                    rewardInfoList.add(recharge);
                }
            }
        }
        return rewardInfoList;
    }

}
