package com.game.scripts.activity;

import com.game.c_entity.merchant.C_Activity;
import com.game.dao.player.PlayerDao;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.ScriptLoader;
import com.game.engine.util.misc.Tuple2;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.FreeGameInfo;
import com.game.entity.player.GameInfo;
import com.game.entity.player.Player;
import com.game.entity.player.activity.ActivityData;
import com.game.entity.player.activity.ActivityInfo;
import com.game.enums.RewardReason;
import com.game.enums.TransactionFrom;
import com.game.enums.TurnoverReason;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.mrg.currency.RewardRequest;
import com.game.hall.script.IBonusScript;
import com.game.hall.script.IPlayerScript;
import com.game.manager.EntityDaoMrg;
import it.unimi.dsi.fastutil.ints.IntLists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Optional;

/**
 * 首充放弃送
 */
public class FirstDepositGiveUpScript extends ActivityScript {
    private static final Logger LOGGER = LoggerFactory.getLogger(FirstDepositGiveUpScript.class);

    @Override
    public void initData(Player player, C_Activity c_activity) {
        if (TimeUtil.currentTimeMillis() < c_activity.getStartTime() || TimeUtil.currentTimeMillis() >= c_activity.getEndTime()) {
            return;
        }

        if (c_activity.channelLimit(player.getChannelId())) {
            return;
        }

        if (c_activity.agentLimit(player.getAgentId())) {
            return;
        }

        final ActivityInfo activityInfo = player.getActivityInfo();
        final int activityId = Integer.parseInt(c_activity.getActivityId() + "" + c_activity.getActivitySubType());
        ActivityData activityData = activityInfo.getActivityData(activityId);
        if (activityData == null) {
            activityData = new ActivityData();
            activityInfo.getActivityDataMap().put(activityId, activityData);
        }

        if (activityData.isStart() && c_activity.getC_id() == activityData.getC_id()) {
            return;
        }

        activityData.reset();
        activityData.setStart(true);
        activityData.setC_id(c_activity.getC_id());
    }

    @Override
    public void resetData(Player player, C_Activity c_activity) {
        final ActivityInfo activityInfo = player.getActivityInfo();
        final int activityId = Integer.parseInt(c_activity.getActivityId() + "" + c_activity.getActivitySubType());
        final ActivityData activityData = activityInfo.getActivityData(activityId);
        if (activityData == null) {
            return;
        }

        if (!activityData.isStart()) {
            return;
        }

        if (c_activity.getSettlementCycle() == 0 && TimeUtil.currentTimeMillis() < c_activity.getEndTime()) {
            return;
        }

        activityData.getReceived().clear();
        activityData.resetProcess();

        initData(player, c_activity);
    }

    @Override
    public void clearData(Player player, C_Activity c_activity) {
    }

    @Override
    public RewardRequest receiveReward(Player player, C_Activity c_activity) {
        final RewardRequest rewardRequest = freeGiveReward(player, c_activity);
        final RewardReason rewardReason = RewardReason.Activity_FirstDepositGiveUp;
        CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getBonusCurrencyMap(), rewardReason);
        return rewardRequest;
    }

    @Override
    public RewardRequest receiveSettlementReward(Player player, C_Activity c_activity) {
        return new RewardRequest();
    }

    @Override
    public void freeGiveExecute(Player player, int freeGiveType) {
//        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
//        if (merchantData == null) {
//            return;
//        }
//
//        final C_Activity c_activity = merchantData.findC_Activity(this.getClass().getSimpleName(), ActivityMrg.FIRSTDEPOSIT_GIVEUP);
//        if (c_activity == null) {
//            return;
//        }
//
//        if (!checkActivityJoin(player, 0, c_activity)) {
//            return;
//        }
//
//        initData(player, c_activity);
//
//        if (c_activity.getReceive() == 2) {//自动
//            final RewardRequest rewardRequest = freeGiveReward(player, c_activity);
//
//            final RewardReason rewardReason = RewardReason.Activity_FirstDepositGiveUp;
//            CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getBonusCurrencyMap(), rewardReason);
//
//            //TODO 活动日志
//            sendActivityLog(player, 2, c_activity, rewardRequest);
//
//            if (!rewardRequest.getCurrencyMap().isEmpty()) {
//                final TransactionFrom transaction = TransactionFrom.FirstDepositGiveUp;
//                final Tuple2<Integer, Double> tuple2 = rewardRequest.getReward();
//                ScriptLoader.getInstance().consumerScript("BonusScript", (IBonusScript script) ->
//                        script.addBonusNote(transaction, player, tuple2.getFirst(), tuple2.getSecond()));
//            }
//        }
    }

    @Override
    public int receiveStatus(Player player, C_Activity c_activity) {
        final ActivityInfo activityInfo = player.getActivityInfo();
        final int activityId = Integer.parseInt(c_activity.getActivityId() + "" + c_activity.getActivitySubType());
        final ActivityData activityData = activityInfo.getActivityData(activityId);
        if (activityData == null) {
            return 0;
        }

        if (activityData.isStart() && c_activity.isNowReceive()) {
            if (!activityData.getReceived().isEmpty()) {
                return 2;
            }
            return 0;
        }
        return 0;
    }

    private RewardRequest freeGiveReward(Player player, C_Activity c_activity) {
        final RewardRequest rewardRequest = new RewardRequest();

        final ActivityInfo activityInfo = player.getActivityInfo();
        final int activityId = Integer.parseInt(c_activity.getActivityId() + "" + c_activity.getActivitySubType());
        final ActivityData activityData = activityInfo.getActivityData(activityId);
        if (activityData == null) {
            return rewardRequest;
        }

        if (activityData.getReceived().contains(c_activity.getActivitySubType())) {
            return rewardRequest;
        }

        activityData.getReceived().add(c_activity.getActivitySubType());

        final C_Activity.FreeGiveInfo freeGiveInfo = c_activity.getFreeGiveInfo();
        rewardRequest.addCurrency(freeGiveInfo.getCurrencyId(), freeGiveInfo.getReward());

        //添加免费游戏次数
        if (freeGiveInfo.currencyId > 0) {
            final FreeGameInfo freeGameInfo = player.getFreeGameInfo(freeGiveInfo.currencyId);
            final GameInfo gameInfo = freeGameInfo.getFreeGame(freeGiveInfo.gameId);
            gameInfo.incFreeTimes(freeGiveInfo.freeTimes);
            gameInfo.setBet(freeGiveInfo.bet);
            gameInfo.setMinWithdraw(freeGiveInfo.getMinWithdraw());
            gameInfo.setMaxWithdraw(freeGiveInfo.getMaxWithdraw());
            EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                    .updateFreeGameInfo(player, IntLists.singleton(freeGiveInfo.currencyId));
        }

        //TODO 打码
        final TurnoverReason turnoverReason = TurnoverReason.Activity_FirstDepositGiveUp;
        ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                (IPlayerScript script) -> script.drawStandard(player, turnoverReason, freeGiveInfo.getCurrencyId(), freeGiveInfo.getReward(),
                        BigDecimalUtils.mul(freeGiveInfo.getReward(), freeGiveInfo.getTurnoverMul(), 4)));
        return rewardRequest;
    }

}
