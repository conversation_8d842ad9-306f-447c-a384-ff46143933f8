package com.game.scripts.activity;

import com.game.c_entity.merchant.C_Activity;
import com.game.c_entity.middleplatform.C_BaseExchangeRate;
import com.game.dao.player.PlayerDao;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.log.GameLog;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.GuidGeneratorUtils;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.FreeGameInfo;
import com.game.entity.player.GameInfo;
import com.game.entity.player.Player;
import com.game.entity.player.activity.ActivityData;
import com.game.entity.player.activity.ActivityInfo;
import com.game.entity.player.stats.Stats;
import com.game.entity.player.vip.VipClub;
import com.game.enums.Currency;
import com.game.enums.TurnoverReason;
import com.game.enums.redis.RedisHall;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.currency.RewardRequest;
import com.game.hall.script.IPlayerScript;
import com.game.hall.script.activity.IActivityScript;
import com.game.manager.EntityDaoMrg;
import io.netty.util.internal.StringUtil;
import it.unimi.dsi.fastutil.ints.IntLists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.InetAddress;
import java.util.*;

public class ActivityScript implements IActivityScript {
    private static final Logger LOGGER = LoggerFactory.getLogger(ActivityScript.class);

    boolean checkActivityJoin(Player player, int currencyId, C_Activity c_activity) {
        if (c_activity.channelLimit(player.getChannelId())) {
//            LOGGER.warn("activityId：{}，activitySubtype：{}，playerId：{}，channelId：{}，channel limit", c_activity.getActivityId(), c_activity.getActivitySubType(), player.getPlayerId(), player.getChannelId());
            return false;
        }

        if (c_activity.agentLimit(player.getAgentId())) {
//            LOGGER.warn("activityId：{}，activitySubtype：{}，playerId：{}，channelId：{}，channel limit", c_activity.getActivityId(), c_activity.getActivitySubType(), player.getPlayerId(), player.getChannelId());
            return false;
        }

        if (c_activity.isEvUsd()) {
            currencyId = Currency.USD.getCurrencyId();
        }

        final C_Activity.ConditionInfo conditionInfo = c_activity.findCondition(currencyId);
        if (conditionInfo == null) {
//            LOGGER.warn("activityId：{}，activitySubtype：{}，playerId：{}，conditionInfo not exist", c_activity.getActivityId(), c_activity.getActivitySubType(), player.getPlayerId());
            return false;
        }

        if (TimeUtil.currentTimeMillis() < c_activity.getStartTime() || TimeUtil.currentTimeMillis() >= c_activity.getEndTime()) {
//            LOGGER.warn("activityId：{}，activitySubtype：{}，playerId：{}，end", c_activity.getActivityId(), c_activity.getActivitySubType(), player.getPlayerId());
            return false;
        }

        final VipClub vipClub = player.getVipClub();
        if (vipClub.getVipLevel() < conditionInfo.getVipLimit()) {
//            LOGGER.warn("activityId：{}，activitySubtype：{}，playerId：{}，vipLimit：{}-{}",
//                    c_activity.getActivityId(), c_activity.getActivitySubType(), player.getPlayerId(), vipClub.getVipLevel(), conditionInfo.getVipLimit());
            return false;
        }

        double balance = 0;
        if (!c_activity.isEvUsd()) {
            balance = player.getCurrencyMap().getOrDefault(currencyId, 0d);
        } else {
            for (Map.Entry<Integer, Double> entry : player.getCurrencyMap().entrySet()) {
                final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), entry.getKey());
                if (c_baseExchangeRate == null) {
                    continue;
                }
                balance = BigDecimalUtils.add(balance, BigDecimalUtils.mul(entry.getValue(), c_baseExchangeRate.getExchangeRate(), 9), 9);
            }
        }

        if (balance < conditionInfo.getBalance()) {
//            LOGGER.warn("activityId：{}，activitySubtype：{}，playerId：{}，balanceLimit：{}-{}",
//                    c_activity.getActivityId(), c_activity.getActivitySubType(), player.getPlayerId(), balance, conditionInfo.getBalance());
            return false;
        }

        final int registerDay = TimeUtil.getNaturalDays(TimeUtil.currentTimeMillis(), player.getCreateTime(), c_activity.getTimeZone());
        if (registerDay < conditionInfo.getRegisterDay()) {
//            LOGGER.warn("activityId：{}，activitySubtype：{}，playerId：{}，registerDayLimit：{}-{}",
//                    c_activity.getActivityId(), c_activity.getActivitySubType(), player.getPlayerId(), registerDay, conditionInfo.getRegisterDay());
            return false;
        }

        final int firstRechargeDay = TimeUtil.getNaturalDays(TimeUtil.currentTimeMillis(), player.getFirstRechargeTime(), c_activity.getTimeZone());
        if (firstRechargeDay < conditionInfo.getFirstRechargeDay()) {
//            LOGGER.warn("activityId：{}，activitySubtype：{}，playerId：{}，firstRechargeDayLimit；{}-{}",
//                    c_activity.getActivityId(), c_activity.getActivitySubType(), player.getPlayerId(), firstRechargeDay, conditionInfo.getFirstRechargeDay());
            return false;
        }

        double firstRechargeAmount = 0;
        if (!c_activity.isEvUsd()) {
            final Stats stats = player.getStats(currencyId);
            firstRechargeAmount = stats.getFirstRechargeAmount();
        } else {
            for (Map.Entry<Integer, Stats> entry : player.getStatsInfo().getStatsMap().entrySet()) {
                final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), entry.getKey());
                if (c_baseExchangeRate == null) {
                    continue;
                }
                firstRechargeAmount = BigDecimalUtils.add(firstRechargeAmount, BigDecimalUtils.mul(entry.getValue().getFirstRechargeAmount(), c_baseExchangeRate.getExchangeRate(), 9), 9);
            }
        }

        if (firstRechargeAmount < conditionInfo.getFirstRechargeAmount()) {
//            LOGGER.warn("activityId：{}，activitySubtype：{}，playerId：{}，firstRechargeAmountLimit：{}-{}",
//                    c_activity.getActivityId(), c_activity.getActivitySubType(), player.getPlayerId(), firstRechargeAmount, conditionInfo.getFirstRechargeAmount());
            return false;
        }

        if (conditionInfo.getRechargeTime() > 0 && (player.getRechargeTime() == 0 || ((TimeUtil.currentTimeMillis() - player.getRechargeTime()) / TimeUtil.HOUR > conditionInfo.getRechargeTime()))) {
//            LOGGER.warn("activityId：{}，activitySubtype：{}，playerId：{}，rechargeTimeLimit：{}-{}",
//                    c_activity.getActivityId(), c_activity.getActivitySubType(), player.getPlayerId(), (TimeUtil.currentTimeMillis() - player.getRechargeTime()) / TimeUtil.HOUR, conditionInfo.getRechargeTime());
            return false;
        }

        return true;
    }

    List<C_Activity.RewardInfo> allReward(C_Activity c_activity, ActivityData activityData, Map<Integer, Double> progressMap) {
        final List<C_Activity.RewardInfo> rewardInfoList = new ArrayList<>();
        if (c_activity.getRewardType() == 1) {//每档
            if (!c_activity.isEvUsd()) {
                for (Map.Entry<Integer, Double> entry : progressMap.entrySet()) {
                    final int currencyId = entry.getKey();
                    final double progress = entry.getValue();

                    final Set<Integer> rewards = activityData.getReceiveRewards(currencyId);
                    final List<C_Activity.RewardInfo> rechargeList = c_activity.findRewardsList(rewards, currencyId, progress);
                    if (rechargeList.isEmpty()) {
                        continue;
                    }

                    rewardInfoList.addAll(rechargeList);
                }
            } else {
                double progress = 0;
                for (Map.Entry<Integer, Double> entry : progressMap.entrySet()) {
                    final int currencyId = entry.getKey();
                    final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), currencyId);
                    if (c_baseExchangeRate == null) {
                        continue;
                    }
                    progress = BigDecimalUtils.add(progress, BigDecimalUtils.mul(progress, c_baseExchangeRate.getExchangeRate(), 9), 9);
                }

                final Set<Integer> rewards = activityData.getReceiveRewards(Currency.USD.getCurrencyId());
                final List<C_Activity.RewardInfo> rechargeList = c_activity.findRewardsList(rewards, Currency.USD.getCurrencyId(), progress);

                rewardInfoList.addAll(rechargeList);
            }
        } else {//最高档
            if (!c_activity.isEvUsd()) {
                for (Map.Entry<Integer, Double> entry : progressMap.entrySet()) {
                    final int currencyId = entry.getKey();
                    final double progress = entry.getValue();

                    final C_Activity.RewardInfo recharge = c_activity.findRewardInfo(currencyId, progress);
                    if (recharge == null) {
                        continue;
                    }
                    rewardInfoList.add(recharge);
                }
            } else {
                double progress = 0;
                for (Map.Entry<Integer, Double> entry : progressMap.entrySet()) {
                    final int currencyId = entry.getKey();
                    final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), currencyId);
                    if (c_baseExchangeRate == null) {
                        continue;
                    }
                    progress = BigDecimalUtils.add(progress, BigDecimalUtils.mul(progress, c_baseExchangeRate.getExchangeRate(), 9), 9);
                }

                final C_Activity.RewardInfo recharge = c_activity.findRewardInfo(Currency.USD.getCurrencyId(), progress);
                if (recharge != null) {
                    rewardInfoList.add(recharge);
                }
            }
        }
        return rewardInfoList;
    }

    List<C_Activity.RewardInfo> reward(int currencyId, C_Activity c_activity, ActivityData activityData, Map<Integer, Double> progressMap) {
        final List<C_Activity.RewardInfo> rewardInfos = new ArrayList<>();
        double progress = 0;
        if (c_activity.isEvUsd()) {
            currencyId = Currency.USD.getCurrencyId();
            for (Map.Entry<Integer, Double> entry : progressMap.entrySet()) {
                final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), entry.getKey());
                if (c_baseExchangeRate == null) {
                    continue;
                }
                progress = BigDecimalUtils.add(progress, BigDecimalUtils.mul(entry.getValue(), c_baseExchangeRate.getExchangeRate(), 9), 9);
            }
        } else {
            progress = progressMap.getOrDefault(currencyId, 0d);
        }

        final Set<Integer> rewards = activityData.getReceiveRewards(currencyId);
        if (c_activity.getRewardType() == 1) {//每档
            final List<C_Activity.RewardInfo> rechargeList = c_activity.findRewardsList(rewards, currencyId, progress);
            if (rechargeList.isEmpty()) {
                return rewardInfos;
            }
            rewardInfos.addAll(rechargeList);
            return rewardInfos;
        } else {//最高档
            if (!rewards.isEmpty()) {
                return rewardInfos;
            }
            final C_Activity.RewardInfo recharge = c_activity.findRewardInfo(currencyId, progress);
            if (recharge == null) {
                return rewardInfos;
            }
            rewardInfos.add(recharge);
        }
        return rewardInfos;
    }

    RewardRequest reward(Player player, ActivityData activityData, Map<Integer, Double> progressMap, C_Activity c_activity, List<C_Activity.RewardInfo> rewardInfos) {
        final RewardRequest rewardRequest = new RewardRequest();
        for (C_Activity.RewardInfo rewardInfo : rewardInfos) {
            final Set<Integer> receiveRewards = activityData.getReceiveRewards(rewardInfo.currencyId);
            receiveRewards.add(rewardInfo.getId());
            //添加免费游戏次数
            if (rewardInfo.rewardCurrency > 0) {
                final FreeGameInfo freeGameInfo = player.getFreeGameInfo(rewardInfo.rewardCurrency);
                final GameInfo gameInfo = freeGameInfo.getFreeGame(rewardInfo.gameId);
                gameInfo.incFreeTimes(rewardInfo.freeTimes);
                gameInfo.setBet(rewardInfo.bet);
                gameInfo.setMinWithdraw(rewardInfo.getMinWithdraw());
                gameInfo.setMaxWithdraw(rewardInfo.getMaxWithdraw());
                EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                        .updateFreeGameInfo(player, IntLists.singleton(rewardInfo.rewardCurrency));
            }
            if (rewardInfo.getRewardType() == 1) {
                rewardRequest.addCurrency(rewardInfo.rewardCurrency, rewardInfo.reward);

                //打码
//                final WithdrawStandard withdrawStandard = player.getWithdrawStandard(rewardInfo.rewardCurrency);
//                withdrawStandard.incDrawStandard(BigDecimalUtils.mul(rewardInfo.reward, rewardInfo.turnoverMul, 9));
                TurnoverReason turnoverReason;
                if (c_activity.getActivityId() == ActivityMrg.DEPOSIT) {
                    turnoverReason = TurnoverReason.Activity_RechargeAccumulation;
                } else if (c_activity.getActivityId() == ActivityMrg.WAGERED) {
                    turnoverReason = TurnoverReason.Activity_Wagered;
                } else {
                    turnoverReason = TurnoverReason.Default;
                }
                turnoverReason.setSource(c_activity.getActivitySubType() + "");
                ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                        (IPlayerScript script) -> script.drawStandard(player, turnoverReason, rewardInfo.rewardCurrency, rewardInfo.reward,
                                BigDecimalUtils.mul(rewardInfo.reward, rewardInfo.turnoverMul, 4)));
            } else {
                double progress = 0;
                if (!c_activity.isEvUsd()) {
                    progress = progressMap.getOrDefault(rewardInfo.rewardCurrency, 0d);
                    double reward = BigDecimalUtils.mul(progress, rewardInfo.reward, 9);
                    if (reward >= rewardInfo.maxReward) {
                        reward = rewardInfo.maxReward;
                    }
                    rewardRequest.addCurrency(rewardInfo.rewardCurrency, reward);

                    //打码
//                    final WithdrawStandard withdrawStandard = player.getWithdrawStandard(rewardInfo.rewardCurrency);
//                    withdrawStandard.incDrawStandard(BigDecimalUtils.mul(reward, rewardInfo.turnoverMul, 9));
                    TurnoverReason turnoverReason;
                    if (c_activity.getActivityId() == ActivityMrg.DEPOSIT) {
                        turnoverReason = TurnoverReason.Activity_RechargeAccumulation;
                    } else if (c_activity.getActivityId() == ActivityMrg.WAGERED) {
                        turnoverReason = TurnoverReason.Activity_Wagered;
                    } else {
                        turnoverReason = TurnoverReason.Default;
                    }
                    turnoverReason.setSource(c_activity.getActivitySubType() + "");
                    final double finalReward = reward;
                    ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                            (IPlayerScript script) -> script.drawStandard(player, turnoverReason, rewardInfo.rewardCurrency, finalReward,
                                    BigDecimalUtils.mul(finalReward, rewardInfo.turnoverMul, 4)));
                } else {
                    for (Map.Entry<Integer, Double> entry : progressMap.entrySet()) {
                        final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), entry.getKey());
                        if (c_baseExchangeRate == null) {
                            continue;
                        }
                        progress = BigDecimalUtils.add(progress, BigDecimalUtils.mul(entry.getValue(), c_baseExchangeRate.getExchangeRate(), 9), 9);
                    }

                    final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), rewardInfo.rewardCurrency);
                    if (c_baseExchangeRate != null) {
                        final double exchange = BigDecimalUtils.div(1, c_baseExchangeRate.getExchangeRate(), 9);
                        double reward = BigDecimalUtils.mul(BigDecimalUtils.mul(exchange, progress, 9), rewardInfo.reward, 9);
                        if (reward >= rewardInfo.maxReward) {
                            reward = rewardInfo.maxReward;
                        }
                        rewardRequest.addCurrency(rewardInfo.rewardCurrency, reward);

                        //打码
//                        final WithdrawStandard withdrawStandard = player.getWithdrawStandard(rewardInfo.rewardCurrency);
//                        withdrawStandard.incDrawStandard(BigDecimalUtils.mul(reward, rewardInfo.turnoverMul, 9));
                        TurnoverReason turnoverReason;
                        if (c_activity.getActivityId() == ActivityMrg.DEPOSIT) {
                            turnoverReason = TurnoverReason.Activity_RechargeAccumulation;
                        } else if (c_activity.getActivityId() == ActivityMrg.WAGERED) {
                            turnoverReason = TurnoverReason.Activity_Wagered;
                        } else {
                            turnoverReason = TurnoverReason.Default;
                        }
                        turnoverReason.setSource(c_activity.getActivitySubType() + "");
                        final double finalReward = reward;
                        ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                                (IPlayerScript script) -> script.drawStandard(player, turnoverReason, rewardInfo.rewardCurrency, finalReward,
                                        BigDecimalUtils.mul(finalReward, rewardInfo.turnoverMul, 4)));
                    }
                }
            }
        }
        return rewardRequest;
    }

    RewardRequest manualReward(Player player, ActivityData activityData, C_Activity c_activity, List<C_Activity.RewardInfo> rewardInfos) {
        final RewardRequest rewardRequest = new RewardRequest();
        for (C_Activity.RewardInfo rewardInfo : rewardInfos) {
            //添加免费游戏次数
//            if (rewardInfo.rewardCurrency > 0) {
//                final FreeGameInfo freeGameInfo = player.getFreeGameInfo(rewardInfo.rewardCurrency);
//                final GameInfo gameInfo = freeGameInfo.getFreeGame(rewardInfo.gameId);
//                gameInfo.incFreeTimes(rewardInfo.freeTimes);
//                gameInfo.setBet(rewardInfo.bet);
//                gameInfo.setMinWithdraw(rewardInfo.getMinWithdraw());
//                gameInfo.setMaxWithdraw(rewardInfo.getMaxWithdraw());
//                EntityDaoMrg.getInstance().getDao(PlayerDao.class)
//                        .updateFreeGameInfo(player, IntLists.singleton(rewardInfo.rewardCurrency));
//            }
            if (rewardInfo.getRewardType() == 1) {
                rewardRequest.addCurrency(rewardInfo.rewardCurrency, rewardInfo.reward);

                activityData.incTurnover(rewardInfo.rewardCurrency,
                        BigDecimalUtils.mul(rewardInfo.reward, rewardInfo.turnoverMul, 4));
            } else {
                double progress = 0;
                if (!c_activity.isEvUsd()) {
                    progress = activityData.getProgressMap().getOrDefault(rewardInfo.rewardCurrency, 0d);
                    double reward = BigDecimalUtils.mul(progress, rewardInfo.reward, 9);
                    if (reward >= rewardInfo.maxReward) {
                        reward = rewardInfo.maxReward;
                    }
                    rewardRequest.addCurrency(rewardInfo.rewardCurrency, reward);

                    activityData.incTurnover(rewardInfo.rewardCurrency,
                            BigDecimalUtils.mul(reward, rewardInfo.turnoverMul, 4));
                } else {
                    for (Map.Entry<Integer, Double> entry : activityData.getProgressMap().entrySet()) {
                        final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), entry.getKey());
                        if (c_baseExchangeRate == null) {
                            continue;
                        }
                        progress = BigDecimalUtils.add(progress, BigDecimalUtils.mul(entry.getValue(), c_baseExchangeRate.getExchangeRate(), 9), 9);
                    }

                    final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), rewardInfo.rewardCurrency);
                    if (c_baseExchangeRate != null) {
                        final double exchange = BigDecimalUtils.div(1, c_baseExchangeRate.getExchangeRate(), 9);
                        double reward = BigDecimalUtils.mul(BigDecimalUtils.mul(exchange, progress, 9), rewardInfo.reward, 9);
                        if (reward >= rewardInfo.maxReward) {
                            reward = rewardInfo.maxReward;
                        }
                        rewardRequest.addCurrency(rewardInfo.rewardCurrency, reward);

                        activityData.incTurnover(rewardInfo.rewardCurrency,
                                BigDecimalUtils.mul(reward, rewardInfo.turnoverMul, 4));
                    }
                }
            }
        }
        return rewardRequest;
    }

    /**
     * 状态 0.不可领取 1.可领取
     *
     * @param player
     * @param c_activity
     * @return
     */
    @Override
    public int receiveStatus(Player player, C_Activity c_activity) {
        final ActivityInfo activityInfo = player.getActivityInfo();
        int activityId = Integer.parseInt(c_activity.getActivityId() + "" + c_activity.getActivitySubType());
        if (c_activity.getActivityId() == ActivityMrg.WAGERED) {
            final C_Activity.WageredCondition condition = c_activity.getWageredCondition();
            final String gameType = condition.getGameType() == 0 ? "" : condition.getGameType() + "";
//            final String platformId = condition.getPlatformId() == 0 ? "" : condition.getPlatformId() + "";
            activityId = Integer.parseInt(c_activity.getActivityId() + gameType);
        }

        final ActivityData activityData = activityInfo.getActivityData(activityId);
        if (activityData == null) {
            return 0;
        }

        int currencyId = player.getCurrencyId();
        if (c_activity.isEvUsd()) {
            currencyId = Currency.USD.getCurrencyId();
        }
        final Set<Integer> rewards = activityData.getReceiveRewards(currencyId);
        if (c_activity.getActivityId() == ActivityMrg.DEPOSIT && c_activity.getActivitySubType() == 2 && !rewards.isEmpty()) {
            return 2;
        }

        if (activityData.isStart() && c_activity.isNowReceive()) {
            final List<C_Activity.RewardInfo> rewardInfos = reward(currencyId, c_activity, activityData, activityData.getProgressMap());
            if (!rewardInfos.isEmpty()) {
                return 1;
            }
            if (c_activity.getRewardType() == 1) {//每档
                final List<C_Activity.RewardInfo> rechargeList = c_activity.getRewardsMap().get(currencyId);
                if (rechargeList == null) {
                    return 0;
                }
                if (rechargeList.size() != rewards.size()) {
                    return 0;
                } else {
                    return 2;
                }
            } else {
                if (!rewards.isEmpty()) {
                    return 2;
                }
            }
        } else if (activityData.isStart()) {//活动结束领取
            if (!activityData.getBonusMap().isEmpty()) {
                return 1;
            }
        }
        return 0;
    }

    @Override
    public void sendActivityLog(Player player, int type, C_Activity c_activity, RewardRequest rewardRequest) {
        final GameLog playerActivityLog = new GameLog("platform_playerActivityLog");
        playerActivityLog.append("number", GuidGeneratorUtils.generateOrderId())
                .append("business_no", player.getBusiness_no())
                .append("channel", player.getChannel())
                .append("site", player.getWebSite())
                .append("id", c_activity.getC_id())
                .append("activityId", c_activity.getActivityId())
                .append("activityUniqueId", c_activity.getC_id())
                .append("activityType", c_activity.getActivityType())
                .append("language", c_activity.getLanguage())
                .append("type", type)//type =1，参加活动，type =2 ,领取
                .append("playerId", player.getPlayerId())
                .append("playerName", player.getPlayerName())
                .append("agentId", player.getAgentId())
                .append("channelId", player.getChannelId())
                .append("region", player.getRegisterRegion())
                .append("logTime", TimeUtil.currentTimeMillis());
        if (type == 2) {
            playerActivityLog.append("currencyReward", JsonUtils.writeAsJson(rewardRequest.getCurrencyMap()));
        } else {
            playerActivityLog.append("currencyReward", "");
        }
        HallServer.getInstance().getLogProducerMrg().send(playerActivityLog);
    }

    @Override
    public boolean isSameNetworkOrDevice(Player player, Player superiorPlayer, int activityId) {
        try {
            final Set<String> ips = new HashSet<>();
            final Set<String> devices = new HashSet<>();
            if (ActivityMrg.REWARD_BOX == activityId) {
                final Set<String> rewardBoxIp = RedisPoolManager.getInstance().function(jedis ->
                        jedis.sync().smembers(RedisHall.Platform_Role_Map_RewardBox_Ip.getKey(superiorPlayer.getPlayerId())));
                if (rewardBoxIp != null) {
                    ips.addAll(rewardBoxIp);
                }

                final Set<String> rewardBoxDevice = RedisPoolManager.getInstance().function(jedis ->
                        jedis.sync().smembers(RedisHall.Platform_Role_Map_RewardBox_Device.getKey(superiorPlayer.getPlayerId())));
                if (rewardBoxDevice != null) {
                    devices.addAll(rewardBoxDevice);
                }

            } else if (ActivityMrg.LUCK_SPIN_REFERRAL == activityId) {
                final Set<String> referralLuckSpinIp = RedisPoolManager.getInstance().function(jedis ->
                        jedis.sync().smembers(RedisHall.Platform_Role_Map_ReferralLuckSpin_Ip.getKey(superiorPlayer.getPlayerId())));
                if (referralLuckSpinIp != null) {
                    ips.addAll(referralLuckSpinIp);
                }

                final Set<String> referralLuckSpinDevice = RedisPoolManager.getInstance().function(jedis ->
                        jedis.sync().smembers(RedisHall.Platform_Role_Map_ReferralLuckSpin_device.getKey(superiorPlayer.getPlayerId())));
                if (referralLuckSpinDevice != null) {
                    devices.addAll(referralLuckSpinDevice);
                }
            }

            ips.add(superiorPlayer.getRegisterIp());
            if (!StringUtil.isNullOrEmpty(superiorPlayer.getDevice())) {
                devices.add(superiorPlayer.getDevice() + "-" + superiorPlayer.getModel());
            }

//            LOGGER.warn("ips：{}", JsonUtils.writeAsJson(ips));
//            LOGGER.warn("ip：{}", player.getIp());
            for (String ip : ips) {
                // 将IP地址转换为字节数组
                final byte[] ipABytes = InetAddress.getByName(ip).getAddress();
                final byte[] ipBBytes = InetAddress.getByName(player.getRegisterIp()).getAddress();

                // IPv4 长度是 4，IPv6 长度是 16，如果两者 IP 版本不匹配，则不属于同一网络
                if (ipABytes.length != ipBBytes.length) {
                    continue;
                }

                // 判断前2个字节是否相同
                if (ipABytes[0] == ipBBytes[0] && ipABytes[1] == ipBBytes[1]) {
                    return true;
                }
            }

            if (StringUtil.isNullOrEmpty(player.getDevice())) {
                return false;
            }

//            LOGGER.warn("devices：{}", JsonUtils.writeAsJson(devices));
//            LOGGER.warn("device：{}", player.getDevice() + "-" + player.getModel());
            for (String device : devices) {
                if (Objects.equals(device, player.getDevice() + "-" + player.getModel())) {
                    return true;
                }
            }

            return false;
        } catch (Exception e) {
            LOGGER.error("isSameNetwork", e);
            return false;
        }
    }
}
