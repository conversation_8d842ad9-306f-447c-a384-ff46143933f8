package com.game.scripts.activity.weeklyRaffle;

import com.game.c_entity.merchant.C_WinTicketNumbers;
import com.game.c_entity.middleplatform.C_BaseExchangeRate;
import com.game.c_entity.merchant.C_WeeklyRaffle;
import com.game.c_entity.middleplatform.C_BaseMerchant;
import com.game.dao.activity.TicketsNoteDao;
import com.game.dao.activity.WinTicketsNoteDao;
import com.game.dao.player.PlayerDao;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.log.GameLog;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.math.MathUtils;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.Config;
import com.game.engine.utils.GuidGeneratorUtils;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.TimeUtil;
import com.game.entity.activity.TicketsNote;
import com.game.entity.activity.WinTicketsNote;
import com.game.entity.player.Player;
import com.game.entity.player.activity.weeklyRaffle.WeeklyRaffleInfo;
import com.game.enums.FunctionEnabled;
import com.game.enums.RewardReason;
import com.game.enums.TransactionFrom;
import com.game.enums.TurnoverReason;
import com.game.enums.redis.RedisAllGame;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.mrg.currency.RewardRequest;
import com.game.hall.script.IBonusScript;
import com.game.hall.script.IFunctionEnabledScript;
import com.game.hall.script.IPlayerScript;
import com.game.hall.script.activity.IWeeklyRaffleScript;
import com.game.manager.EntityDaoMrg;
import com.game.utils.VirtualThreadUtils;
import io.netty.util.internal.StringUtil;
import it.unimi.dsi.fastutil.ints.Int2DoubleMap;
import it.unimi.dsi.fastutil.ints.Int2DoubleMaps;
import it.unimi.dsi.fastutil.objects.ObjectIterator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.ZoneId;
import java.util.*;

public class WeeklyRaffleScript implements IWeeklyRaffleScript {
    private static final Logger LOGGER = LoggerFactory.getLogger(WeeklyRaffleScript.class);

    @Override
    public void initData(Player player) {
        final boolean functionEnabled = ScriptLoader.getInstance().functionScript("FunctionEnabledScript", (IFunctionEnabledScript script)
                -> script.functionEnabled(player.getBusiness_no(), FunctionEnabled.WeeklyRaffle.getType()));
        if (!functionEnabled) {
            return;
        }

        final WeeklyRaffleInfo weeklyRaffleInfo = player.getWeeklyRaffleInfo();
        final String gameId = weeklyRaffleInfo.getGameId();
        if (!StringUtil.isNullOrEmpty(gameId)) {
            return;
        }
        weeklyRaffleInfo.setGameId(ActivityMrg.getGameId());
        EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                .updateWeeklyRaffleInfo(player.getPlayerId(), weeklyRaffleInfo);
    }

    @Override
    public void resetData(Player player) {
        final boolean functionEnabled = ScriptLoader.getInstance().functionScript("FunctionEnabledScript", (IFunctionEnabledScript script)
                -> script.functionEnabled(player.getBusiness_no(), FunctionEnabled.WeeklyRaffle.getType()));
        if (!functionEnabled) {
            return;
        }

        final WeeklyRaffleInfo weeklyRaffleInfo = player.getWeeklyRaffleInfo();
        if (Objects.equals(weeklyRaffleInfo.getGameId(), ActivityMrg.getGameId())) {
            return;
        }

        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }

        final C_WeeklyRaffle c_weeklyRaffle = merchantData.findC_WeeklyRaffle(this.getClass().getSimpleName(), ActivityMrg.WEEKLY_RAFFLE);
        if (c_weeklyRaffle == null) {
            return;
        }

        final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        final String gameId = dateFormat.format(TimeUtil.getDayOfWeekEndTimestamp(TimeUtil.currentTimeMillis() - TimeUtil.DAY, ZoneId.systemDefault().toString(), DayOfWeek.SUNDAY));
        final Map<Integer, WinTicketsNote> winTicketsNoteMap = loadWinTicketsNote(player.getBusiness_no(), gameId);
        if (winTicketsNoteMap.isEmpty()) {
            weeklyRaffleInfo.reset();
            return;
        }

        final RewardRequest rewardRequest = new RewardRequest();
        final List<Integer> ticketNumbers = new ArrayList<>();
        for (TicketsNote ticketsNote : weeklyRaffleInfo.getActiveTicketsMap().values()) {
            final WinTicketsNote winTicketsNote = winTicketsNoteMap.get(ticketsNote.getTicketNumbers());
            if (winTicketsNote == null) {
                continue;
            }
            ticketNumbers.add(ticketsNote.getTicketNumbers());
            rewardRequest.addCurrency(winTicketsNote.getCurrencyId(), winTicketsNote.getPrize());

            weeklyRaffleInfo.incTotalPrize(winTicketsNote.getCurrencyId(), winTicketsNote.getPrize());
            weeklyRaffleInfo.incTotalWinTickets();

            //TODO 打码
//            final WithdrawStandard withdrawStandard = player.getWithdrawStandard(winTicketsNote.getCurrencyId());
//            withdrawStandard.incDrawStandard(BigDecimalUtils.mul(winTicketsNote.getPrize(), c_weeklyRaffle.getRewardTurnoverMul(), 4));
            final TurnoverReason turnoverReason = TurnoverReason.WeeklyRaffle;
            turnoverReason.setSource(ticketsNote.getTicketNumbers() + "");
            ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                    (IPlayerScript script) -> script.drawStandard(player, turnoverReason, winTicketsNote.getCurrencyId(), winTicketsNote.getPrize(),
                            BigDecimalUtils.mul(winTicketsNote.getPrize(), c_weeklyRaffle.getRewardTurnoverMul(), 4)));
        }

        final RewardReason rewardReason = RewardReason.WeeklyRaffle;
        rewardReason.setSource(JsonUtils.writeAsJson(ticketNumbers));
        CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getBonusCurrencyMap(), rewardReason);

        weeklyRaffleInfo.reset();

        for (ObjectIterator<Int2DoubleMap.Entry> itr = Int2DoubleMaps.fastIterator(rewardRequest.getCurrencyMap()); itr.hasNext(); ) {
            final Int2DoubleMap.Entry entry = itr.next();
            final int currencyType = entry.getIntKey();
            ScriptLoader.getInstance().consumerScript("BonusScript", (IBonusScript script) ->
                    script.addBonusNote(TransactionFrom.WeeklyRaffle, player, currencyType, entry.getDoubleValue()));
        }
    }

    @Override
    public void weeklyRaffleExecute(Player player, int currencyId, double validBet) {
        final boolean functionEnabled = ScriptLoader.getInstance().functionScript("FunctionEnabledScript", (IFunctionEnabledScript script)
                -> script.functionEnabled(player.getBusiness_no(), FunctionEnabled.WeeklyRaffle.getType()));
        if (!functionEnabled) {
            return;
        }

        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }

        final boolean isManyCurrency = merchantData.getC_currencyMap().size() > 1;

        final C_WeeklyRaffle c_weeklyRaffle = merchantData.findC_WeeklyRaffle(this.getClass().getSimpleName(), ActivityMrg.WEEKLY_RAFFLE);
        if (c_weeklyRaffle == null) {
            return;
        }

        if (!c_weeklyRaffle.getJoinCurrency().contains(currencyId)) {
            return;
        }

        final WeeklyRaffleInfo weeklyRaffleInfo = player.getWeeklyRaffleInfo();
        weeklyRaffleInfo.incDailyWagered(currencyId, validBet);
        weeklyRaffleInfo.incWeeklyWagered(currencyId, validBet);

        if (weeklyRaffleInfo.getDailyTickets() < c_weeklyRaffle.getMaxDailyTickets()) {
            double totalUsdDailyWager = 0;
            for (Map.Entry<Integer, Double> entry : weeklyRaffleInfo.getDailyWageredMap().entrySet()) {
                if (isManyCurrency) {
                    final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), entry.getKey());
                    if (c_baseExchangeRate == null) {
                        continue;
                    }
                    totalUsdDailyWager = BigDecimalUtils.add(totalUsdDailyWager, BigDecimalUtils.mul(entry.getValue(), c_baseExchangeRate.getExchangeRate(), 9), 9);
                } else {
                    totalUsdDailyWager = BigDecimalUtils.add(totalUsdDailyWager, entry.getValue(), 9);
                }
            }

            if (totalUsdDailyWager >= c_weeklyRaffle.getDailyUsdWagered()) {
                weeklyRaffleInfo.incDailyTickets();
                weeklyRaffleInfo.incTotalTickets();
                weeklyRaffleInfo.incWeeklyTickets();
                weeklyRaffleInfo.getDailyWageredMap().clear();
                generateTicketsNote(player, weeklyRaffleInfo);

                RedisPoolManager.getInstance().executeAsync(commands ->
                        commands.incrby(RedisAllGame.Platform_All_WeeklyRaffle_Tickets.getKey(player.getBusiness_no(), ActivityMrg.getGameId()), 1)
                );
            }
        }

        if (weeklyRaffleInfo.getWeeklyTickets() < c_weeklyRaffle.getMaxTotalTickets()) {
            double totalUsdWeeklyWager = 0;
            for (Map.Entry<Integer, Double> entry : weeklyRaffleInfo.getWeeklyWageredMap().entrySet()) {
                if (isManyCurrency) {
                    final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), entry.getKey());
                    if (c_baseExchangeRate == null) {
                        continue;
                    }
                    totalUsdWeeklyWager = BigDecimalUtils.add(totalUsdWeeklyWager, BigDecimalUtils.mul(entry.getValue(), c_baseExchangeRate.getExchangeRate(), 9), 9);
                } else {
                    totalUsdWeeklyWager = BigDecimalUtils.add(totalUsdWeeklyWager, entry.getValue(), 9);
                }
            }

            if (totalUsdWeeklyWager >= c_weeklyRaffle.getEveryUsdWagered()) {
                weeklyRaffleInfo.incTotalTickets();
                weeklyRaffleInfo.incWeeklyTickets();
                weeklyRaffleInfo.getWeeklyWageredMap().clear();
                generateTicketsNote(player, weeklyRaffleInfo);

                RedisPoolManager.getInstance().executeAsync(commands ->
                        commands.incrby(RedisAllGame.Platform_All_WeeklyRaffle_Tickets.getKey(player.getBusiness_no(), ActivityMrg.getGameId()), 1)
                );
            }
        }
    }

    @Override
    public int generateTicketsNote(Player player, WeeklyRaffleInfo weeklyRaffleInfo) {
        final int ticketNumbers = EntityDaoMrg.getInstance().getDao(TicketsNoteDao.class)
                .generateTicketNumbers(player.getPlayerId());
        final TicketsNote ticketsNote = new TicketsNote();
        ticketsNote.setBusiness_no(player.getBusiness_no());
        ticketsNote.setGameId(ActivityMrg.getGameId());
        ticketsNote.setHeadId(player.getHeadId());
        ticketsNote.setPlayerId(player.getPlayerId());
        ticketsNote.setPlayerName(player.getPlayerName());
        ticketsNote.setTicketNumbers(ticketNumbers);
        EntityDaoMrg.getInstance().getDao(TicketsNoteDao.class).insert(ticketsNote);
        weeklyRaffleInfo.getActiveTicketsMap().put(ticketNumbers, ticketsNote);

        //TODO 票号日志
        final GameLog playerWeeklyRaffleLog = new GameLog("platform_playerTicketNumbersLog");
        playerWeeklyRaffleLog.append("number", GuidGeneratorUtils.generateOrderId())
                .append("business_no", player.getBusiness_no())
                .append("date", ticketsNote.getGameId())
                .append("playerId", player.getPlayerId())
                .append("ticketNumbers", ticketNumbers)
                .append("logTime", TimeUtil.currentTimeMillis());
        HallServer.getInstance().getLogProducerMrg().send(playerWeeklyRaffleLog);
        return ticketNumbers;
    }

    @Override
    public void settlement() {
        //指定一个服务器结算
        if (Config.SERVER_ID != 4000) {
            return;
        }

        final List<String> business_noList = DataHallMrg.getInstance().getC_baseHostMerchantMap().values().stream()
                .map(C_BaseMerchant::getBusiness_no)
                .distinct()
                .toList();

        for (String business_no : business_noList) {
            final boolean functionEnabled = ScriptLoader.getInstance().functionScript("FunctionEnabledScript", (IFunctionEnabledScript script)
                    -> script.functionEnabled(business_no, FunctionEnabled.WeeklyRaffle.getType()));
            if (!functionEnabled) {
                continue;
            }

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), business_no);
            if (merchantData == null) {
                continue;
            }

            final C_WeeklyRaffle c_weeklyRaffle = merchantData.findC_WeeklyRaffle(this.getClass().getSimpleName(), ActivityMrg.WEEKLY_RAFFLE);
            if (c_weeklyRaffle == null) {
                continue;
            }

            final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
            final String gameId = dateFormat.format(TimeUtil.getDayOfWeekEndTimestamp(TimeUtil.currentTimeMillis() - TimeUtil.HOUR, ZoneId.systemDefault().toString(), DayOfWeek.SUNDAY));

            final List<TicketsNote> ticketsNoteList = loadTicketsNote(business_no, gameId);
            if (ticketsNoteList.isEmpty()) {
                continue;
            }

            final List<C_WinTicketNumbers> c_winTicketNumbersList = merchantData.getC_winTicketNumbersListMap().get(gameId);

            int no = 0;
            final List<WinTicketsNote> winTicketsNoteList = new ArrayList<>();
            for (C_WeeklyRaffle.RewardAllocation rewardAllocation : c_weeklyRaffle.getRewardAllocationList()) {
                for (int i = 0; i < rewardAllocation.getNum(); i++) {
                    //指定中奖
                    if (c_winTicketNumbersList != null && !c_winTicketNumbersList.isEmpty()) {
                        final String id = gameId + "_" + rewardAllocation.getRewardLevels();
                        final C_WinTicketNumbers c_winTicketNumbers = merchantData.findC_WinTicketNumbers(this.getClass().getSimpleName(), id);
                        if (c_winTicketNumbers != null) {
                            if (winTicketsNoteList.stream().anyMatch(winTicketsNote -> winTicketsNote.getTicketNumbers() == c_winTicketNumbers.getTicketNumbers())) {
                                continue;
                            }
                            final WinTicketsNote winTicketsNote = createWinTicketsNote(++no, c_winTicketNumbers);
                            winTicketsNoteList.add(winTicketsNote);
                            ticketsNoteList.removeIf(note -> note.getTicketNumbers() == winTicketsNote.getTicketNumbers());

                            //中奖票号日志
                            sendWinTicketsNoteLog(winTicketsNote);
                            continue;
                        }
                    }

                    final TicketsNote ticketsNote = MathUtils.randomRemove(ticketsNoteList);
                    if (ticketsNote == null) {
                        break;
                    }
                    ticketsNote.setNo(++no);
                    ticketsNote.setCurrencyId(rewardAllocation.getCurrencyId());
                    ticketsNote.setPrize(rewardAllocation.getReward());

                    final WinTicketsNote winTicketsNote = createWinTicketsNote(ticketsNote);
                    winTicketsNoteList.add(winTicketsNote);

                    //中奖票号日志
                    sendWinTicketsNoteLog(winTicketsNote);
                }
            }
            EntityDaoMrg.getInstance().getDao(WinTicketsNoteDao.class).insertMany(winTicketsNoteList);
        }
    }

    private void sendWinTicketsNoteLog(WinTicketsNote winTicketsNote) {
        //TODO 中奖日志
        final GameLog playerWeeklyRaffleLog = new GameLog("platform_playerWinWeeklyRaffleLog");
        playerWeeklyRaffleLog.append("number", GuidGeneratorUtils.generateOrderId())
                .append("business_no", winTicketsNote.getBusiness_no())
                .append("date", winTicketsNote.getGameId())
                .append("no", winTicketsNote.getNo())
                .append("winnerId", winTicketsNote.getPlayerId())
                .append("ticketNumbers", winTicketsNote.getTicketNumbers())
                .append("currencyId", winTicketsNote.getCurrencyId())
                .append("prize", winTicketsNote.getPrize())
                .append("logTime", TimeUtil.currentTimeMillis());
        HallServer.getInstance().getLogProducerMrg().send(playerWeeklyRaffleLog);
    }

    private WinTicketsNote createWinTicketsNote(TicketsNote ticketsNote) {
        final WinTicketsNote winTicketsNote = new WinTicketsNote();
        winTicketsNote.setBusiness_no(ticketsNote.getBusiness_no());
        winTicketsNote.setGameId(ticketsNote.getGameId());
        winTicketsNote.setHeadId(ticketsNote.getHeadId());
        winTicketsNote.setPlayerId(ticketsNote.getPlayerId());
        winTicketsNote.setPlayerName(ticketsNote.getPlayerName());
        winTicketsNote.setTicketNumbers(ticketsNote.getTicketNumbers());
        winTicketsNote.setNo(ticketsNote.getNo());
        winTicketsNote.setCurrencyId(ticketsNote.getCurrencyId());
        winTicketsNote.setPrize(ticketsNote.getPrize());
        return winTicketsNote;
    }

    private WinTicketsNote createWinTicketsNote(int no, C_WinTicketNumbers c_winTicketNumbers) {
        final WinTicketsNote winTicketsNote = new WinTicketsNote();
        winTicketsNote.setBusiness_no(c_winTicketNumbers.getBusiness_no());
        winTicketsNote.setGameId(c_winTicketNumbers.getDate());
        winTicketsNote.setHeadId(c_winTicketNumbers.getHeadId());
        winTicketsNote.setPlayerId(c_winTicketNumbers.getPlayerId());
        winTicketsNote.setPlayerName(c_winTicketNumbers.getPlayerName());
        winTicketsNote.setTicketNumbers(c_winTicketNumbers.getTicketNumbers());
        winTicketsNote.setNo(no);
        winTicketsNote.setCurrencyId(c_winTicketNumbers.getCurrencyId());
        winTicketsNote.setPrize(c_winTicketNumbers.getReward());
        return winTicketsNote;
    }


    private List<TicketsNote> loadTicketsNote(String business_no, String currencyGameId) {
        int total = 0;
        final int limit = 10000;
        final List<TicketsNote> ticketsNoteList = new ArrayList<>();
        while (true) {
            final List<TicketsNote> ticketsNotes = EntityDaoMrg.getInstance().getDao(TicketsNoteDao.class).loadByGameIdTicketsNote(business_no, currencyGameId, total, limit);
            ticketsNoteList.addAll(ticketsNotes);

            total += ticketsNotes.size();
            if (ticketsNotes.size() < limit) {
                break;
            }
        }
        return ticketsNoteList;
    }

    private Map<Integer, WinTicketsNote> loadWinTicketsNote(String business_no, String currentGameId) {
        int total = 0;
        final int limit = 10000;
        final Map<Integer, WinTicketsNote> winTicketsNoteMap = new LinkedHashMap<>();
        while (true) {
            final List<WinTicketsNote> winTicketsNotes = EntityDaoMrg.getInstance().getDao(WinTicketsNoteDao.class).loadAllWinTicketsNote(business_no, currentGameId, total, limit);
            for (WinTicketsNote winTicketsNote : winTicketsNotes) {
                winTicketsNoteMap.put(winTicketsNote.getTicketNumbers(), winTicketsNote);
            }

            total += winTicketsNotes.size();
            if (winTicketsNotes.size() < limit) {
                break;
            }
        }
        return winTicketsNoteMap;
    }

}
