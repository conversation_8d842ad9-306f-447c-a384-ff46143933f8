package com.game.scripts.promote;

import com.game.c_entity.merchant.C_GameApi;
import com.game.c_entity.merchant.C_CashBack;
import com.game.dao.promote.CommissionRewardsNoteDao;
import com.game.dao.player.PlayerPromoteDao;
import com.game.dao.promote.TeamRewardsNoteDao;
import com.game.dao.promote.ThreeLevelRewardsNoteDao;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.log.GameLog;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.ScriptLoader;
import com.game.engine.util.misc.Tuple2;
import com.game.engine.utils.*;
import com.game.entity.player.Player;
import com.game.entity.player.promote.*;
import com.game.entity.player.stats.Stats;
import com.game.enums.ErrorCode;
import com.game.enums.RewardReason;
import com.game.enums.TransactionFrom;
import com.game.enums.TurnoverReason;
import com.game.enums.redis.RedisLogin;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.mrg.currency.RewardRequest;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.IBonusScript;
import com.game.hall.script.IPlayerScript;
import com.game.hall.script.IPromoteScript;
import com.game.manager.EntityDaoMrg;
import io.netty.util.internal.StringUtil;
import it.unimi.dsi.fastutil.ints.IntLists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Update;

import java.util.*;

public class PromoteScript implements IPromoteScript {
    private static Logger LOGGER = LoggerFactory.getLogger(PromoteScript.class);

    private PlayerPromote findSuperiorPromotion(String referrerCode) {
        final String superiorId = RedisPoolManager.getInstance().function(jedis ->
                jedis.sync().hget(RedisLogin.Platform_LG_Map_InvitationID.getKey(), referrerCode));
        if (StringUtil.isNullOrEmpty(superiorId)) {
            LOGGER.warn("referrerCode：{}， bind failed", referrerCode);
            return null;
        }
        return PlayerMrg.getInstance().findDbPlayerPromote(Long.parseLong(superiorId));
    }

    @Override
    public void calculateSuperiors(Player player) {
        try {
            final PlayerPromote playerPromote = player.getPlayerPromote();
            if (playerPromote == null) {
                return;
            }

            final List<Long> superiors = new ArrayList<>();
            PlayerPromote tempData = PlayerMrg.getInstance().findDbPlayerPromote(playerPromote.getSuperiorId());
            while (tempData != null) {
                superiors.add(tempData.getPlayerId());
                if (tempData.getSuperiorId() <= 0) break;
                tempData = PlayerMrg.getInstance().findDbPlayerPromote(tempData.getSuperiorId());
            }
            Collections.reverse(superiors);

            final Update update = new Update();
            update.set(PlayerPromoteFields.superiors, superiors);
            EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class)
                    .updatePromotion(playerPromote.getPlayerId(), update);
        } catch (Exception e) {
            LOGGER.error("calculateSuperiors error", e);
        }
    }

    @Override
    public ErrorCode bindSuperiorId(Player player, String source, String referrerCode, boolean save) {
        try {
            if (StringUtil.isNullOrEmpty(referrerCode)) {
                return ErrorCode.ReferralCode_Error;
            }
            final PlayerPromote superiorPromote = findSuperiorPromotion(referrerCode);
            if (superiorPromote == null) {
                return ErrorCode.ReferralCode_Error;
            }

            if (!Objects.equals(superiorPromote.getBusiness_no(), player.getBusiness_no())) {
                return ErrorCode.ReferralCode_Error;
            }

            if (superiorPromote.getPlayerId() == player.getPlayerId()) {
                return ErrorCode.ReferralCode_Error;
            }

            final PlayerPromote playerPromote = player.getPlayerPromote();
            if (playerPromote.getSuperiorId() != 0) {
                return ErrorCode.Already_Bound_To_The_Superior;
            }

            //上级
            final long superiorId = superiorPromote.getPlayerId();
            final List<Long> superiors = superiorPromote.getSuperiors();

            //玩家
            if (!StringUtil.isNullOrEmpty(source)) {
                final String[] activity = source.split("_");
                final int activityId = Integer.parseInt(activity[0]);
                playerPromote.setSource(activityId);
            }
            playerPromote.setSuperiorId(superiorId);
            playerPromote.setSuperiorCode(referrerCode);
            if (!superiors.isEmpty()) {
                playerPromote.getSuperiors().addAll(superiors);
            }
            playerPromote.getSuperiors().add(superiorId);
            final Update update = new Update();
            //团长
            if (playerPromote.getSuperiors().size() > 1) {
                final long teamId = playerPromote.getSuperiors().get(playerPromote.getSuperiors().size() - 2);
                final long id = playerPromote.getSuperiors().getLast();
                final PlayerPromote teamPlayer = PlayerMrg.getInstance().findDbPlayerPromote(id);
                if (teamPlayer != null) {
                    playerPromote.setTeamId(teamId);
                    playerPromote.setTeamCode(teamPlayer.getSuperiorCode());
                    if (save) {
                        update.set(PlayerPromoteFields.teamId, playerPromote.getTeamId())
                                .set(PlayerPromoteFields.teamCode, playerPromote.getTeamCode());
                    }
                }
            }
            //三级
            if (playerPromote.getSuperiors().size() > 2) {
                final long threeLevelId = playerPromote.getSuperiors().get(playerPromote.getSuperiors().size() - 3);
                final long id = playerPromote.getSuperiors().get(playerPromote.getSuperiors().size() - 2);
                final PlayerPromote threeLevePlayer = PlayerMrg.getInstance().findDbPlayerPromote(id);
                if (threeLevePlayer != null) {
                    playerPromote.setThreeLevelId(threeLevelId);
                    playerPromote.setThreeLevelCode(threeLevePlayer.getSuperiorCode());
                    if (save) {
                        update.set(PlayerPromoteFields.threeLevelId, playerPromote.getThreeLevelId())
                                .set(PlayerPromoteFields.threeLevelCode, playerPromote.getThreeLevelCode());
                    }
                }
            }

            if (save) {
                update.set(PlayerPromoteFields.superiorId, playerPromote.getSuperiorId())
                        .set(PlayerPromoteFields.superiorCode, playerPromote.getSuperiorCode())
                        .set(PlayerPromoteFields.superiors, playerPromote.getSuperiors());
                EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class)
                        .updatePromotion(player.getPlayerId(), update);
            }

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData != null) {
                final String referralRewardOpen = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "referralRewardOpen");
                if (!StringUtil.isNullOrEmpty(referralRewardOpen) && Boolean.parseBoolean(referralRewardOpen)) {
                    final String value = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "lockedRewards");
                    final String[] mh = value.split(Symbol.MAOHAO_REG);
                    if (mh.length > 0) {
                        final Update superiorUpdate = new Update();
                        if (superiorPromote.getLockedCurrencyId() == 0) {
                            superiorPromote.setLockedCurrencyId(Integer.parseInt(mh[0]));
                            superiorUpdate.set(PlayerPromoteFields.lockedCurrencyId, superiorPromote.getLockedCurrencyId());
                        }
                        superiorPromote.setLockedReward(superiorPromote.getLockedReward() + Double.parseDouble(mh[1]));
                        superiorUpdate.set(PlayerPromoteFields.lockedReward, superiorPromote.getLockedReward());
                        EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class)
                                .updatePromotion(superiorId, superiorUpdate);
                    }
                }
            }

            //重新计算所有下级的上级链
            refreshFriends(player);
            return ErrorCode.Success;
        } catch (Exception e) {
            LOGGER.error("bindSuperiorId error", e);
        }
        return ErrorCode.ReferralCode_Error;
    }

    @Override
    public ErrorCode unbindSuperiorId(Player player) {
        final PlayerPromote playerPromote = player.getPlayerPromote();
        if (playerPromote.getSuperiorId() == 0) return ErrorCode.Success;

        playerPromote.getSuperiors().clear();
        playerPromote.setSuperiorId(0);
        final Update update = new Update();
        update.set(PlayerPromoteFields.superiorId, playerPromote.getSuperiorId())
                .set(PlayerPromoteFields.superiors, playerPromote.getSuperiors());
        EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class)
                .updatePromotion(player.getPlayerId(), update);

        refreshFriends(player);
        return ErrorCode.Success;
    }


    private void refreshFriends(Player player) {
        final int limit = 1000;
        int totalCount = 0;
        final PlayerPromote playerPromote = player.getPlayerPromote();
        while (true) {
            final List<PlayerPromote> friendsList = EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class).loadAllBySuperiorId(player.getPlayerId(), totalCount, limit);
            if (friendsList == null || friendsList.isEmpty()) break;

            for (var friend : friendsList) {
                if (friend == null) continue;

                refreshSuperiors(friend, playerPromote);
            }

            totalCount += friendsList.size();
            if (friendsList.size() < limit) break;
        }
    }

    private void refreshSuperiors(PlayerPromote playerPromote, PlayerPromote superiorData) {
        if (playerPromote == null || superiorData == null) {
            return;
        }
        if (playerPromote.getSuperiorId() != superiorData.getPlayerId()) {
            return;
        }

        final List<Long> superiors = superiorData.getSuperiors();
        if (!superiors.isEmpty()) {
            playerPromote.getSuperiors().addAll(superiors);
        }

        final int limit = 1000;
        int totalCount = 0;
        while (true) {
            final List<PlayerPromote> playerPromotes = EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class).loadAllBySuperiorId(playerPromote.getPlayerId(), totalCount, limit);
            if (playerPromotes == null || playerPromotes.isEmpty()) break;

            for (var item : playerPromotes) {
                if (item == null) continue;
                refreshSuperiors(item, playerPromote);
            }

            totalCount += playerPromotes.size();
            if (playerPromotes.size() < limit) break;
        }
    }

    @Override
    public void calculateSuperiorCommission(Player player, C_GameApi c_gameApi, String noteId, int currencyId, double betAmount, double validBet) {
        try {
            final PlayerPromote playerPromote = player.getPlayerPromote();
            if (playerPromote == null) {
                return;
            }

            if (playerPromote.getSuperiorId() == 0) {
                return;
            }

            final PlayerPromote superiorPromote = PlayerMrg.getInstance().findDbPlayerPromote(playerPromote.getSuperiorId());

            if (superiorPromote == null) {
                LOGGER.warn("playerId：{}，superiorId：{}，not exist playerPromote", player.getPlayerId(), playerPromote.getSuperiorId());
                return;
            }

            final MerchantData merchantData = DataHallMrg.getInstance()
                    .findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                return;
            }

            final String rebateMethod = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "rebateMethod");
            final C_CashBack c_cashBack = merchantData.findC_CashBack(this.getClass().getSimpleName(), c_gameApi.getType());
            if (c_cashBack == null) {
                return;
            }

            double commission = 0;
            if (c_cashBack.getRebateMethod() == 4) {//佣金
                final double a = BigDecimalUtils.mul(betAmount, c_cashBack.getEffectiveRate(), 9);
                commission = BigDecimalUtils.mul(BigDecimalUtils.mul(a, c_cashBack.getGameCashBackRate(), 9), c_cashBack.getSuperiorCashBackRate(), 9);
            } else if (c_cashBack.getRebateMethod() == 1) {//有效
                commission = BigDecimalUtils.mul(BigDecimalUtils.mul(validBet, c_cashBack.getEffectiveRate(), 9), c_cashBack.getSuperiorCashBackRate(), 9);
            }

//                    LOGGER.warn("playerId：{}，calculateSuperiorCommission：{}", player.getPlayerId(), commission);

            if (commission == 0) {
                return;
            }

            final Map<Integer, CommissionRewards> commissionRewardsMap = superiorPromote.getCommissionRewardsMap();
            CommissionRewards commissionRewards = commissionRewardsMap.computeIfAbsent(currencyId, k -> new CommissionRewards());
            commissionRewards.incAvailable(commission);
            commissionRewards.incTotalReceived(commission);

            EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class)
                    .updateCommissionRewards(superiorPromote, IntLists.singleton(currencyId));

            final UniqueIDGenerator uniqueIDGenerator = HallServer.getInstance().getUniqueIDGenerator();
            final CommissionRewardsNote commissionRewardsNote = new CommissionRewardsNote(uniqueIDGenerator.nextId());
            commissionRewardsNote.setBusiness_no(superiorPromote.getBusiness_no());
            commissionRewardsNote.setPlayerId(superiorPromote.getPlayerId());
            commissionRewardsNote.setAmount(commission);
            commissionRewardsNote.setCurrencyId(currencyId);
            commissionRewardsNote.setStatus(1);
            EntityDaoMrg.getInstance().getDao(CommissionRewardsNoteDao.class).insert(commissionRewardsNote);

            final GameLog platform_promoteSuperiorLog = new GameLog("platform_promoteSuperiorLog")
                    .append("number", GuidGeneratorUtils.generateOrderId())
                    .append("gameNoteId", noteId)
                    .append("platformId", c_gameApi.getPlatformId())
                    .append("gameId", c_gameApi.getGameId())
                    .append("gameType", c_gameApi.getType())
                    .append("business_no", commissionRewardsNote.getBusiness_no())//商户号
                    .append("noteId", commissionRewardsNote.getNoteId())//生成的唯一id
                    .append("rebateMethod", rebateMethod)
                    .append("backCashRate", c_cashBack.getSuperiorCashBackRate())
                    .append("amount", commissionRewardsNote.getAmount())
                    .append("currencyId", commissionRewardsNote.getCurrencyId())
                    .append("superiorId", playerPromote.getSuperiorId())//上级id
                    .append("playerId", playerPromote.getPlayerId())//玩家id
                    .append("playerName", playerPromote.getPlayerName())
                    .append("logTime", TimeUtil.currentTimeMillis());
            HallServer.getInstance().getLogProducerMrg().send(platform_promoteSuperiorLog);
        } catch (Exception e) {
            LOGGER.error("calculateSuperiorCommission error", e);
        }
    }


    @Override
    public void calculateTeamCommission(Player player, C_GameApi c_gameApi, String noteId, int currencyId, double betAmount, double validBet) {
        try {
            final PlayerPromote playerPromote = player.getPlayerPromote();
            if (playerPromote == null) {
                return;
            }

            if (playerPromote.getSuperiorId() == 0) {
                return;
            }

            final int size = playerPromote.getSuperiors().size();
            if (size < 2) {
                return;
            }

            final long teamId = playerPromote.getSuperiors().get(size - 2);
            final PlayerPromote teamPromote = PlayerMrg.getInstance().findDbPlayerPromote(teamId);

            if (teamPromote == null) {
                LOGGER.warn("playerId：{}，teamId：{}，not exist", player.getPlayerId(), teamId);
                return;
            }

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                return;
            }

            final String rebateMethod = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "rebateMethod");
            final C_CashBack c_cashBack = merchantData.findC_CashBack(this.getClass().getSimpleName(), c_gameApi.getType());
            if (c_cashBack == null) {
                return;
            }

            double commission = 0;
            if (c_cashBack.getRebateMethod() == 4) {//佣金
                final double a = BigDecimalUtils.mul(betAmount, c_cashBack.getEffectiveRate(), 9);
                commission = BigDecimalUtils.mul(BigDecimalUtils.mul(a, c_cashBack.getGameCashBackRate(), 9), c_cashBack.getTeamCashBackRate(), 9);
            } else if (c_cashBack.getRebateMethod() == 1) {//有效
                commission = BigDecimalUtils.mul(BigDecimalUtils.mul(validBet, c_cashBack.getEffectiveRate(), 9), c_cashBack.getTeamCashBackRate(), 9);
            }

            //LOGGER.warn("playerId：{}，calculateTeamCommission：{}", player.getPlayerId(), commission);

            if (commission == 0) {
                return;
            }

            final Map<Integer, TeamRewards> teamRewardsMap = teamPromote.getTeamRewardsMap();
            TeamRewards teamRewards = teamRewardsMap.computeIfAbsent(currencyId, k -> new TeamRewards());
            teamRewards.incAvailable(commission);
            teamRewards.incTotalReceived(commission);

            EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class)
                    .updateTeamRewards(teamPromote, IntLists.singleton(currencyId));

            final UniqueIDGenerator uniqueIDGenerator = HallServer.getInstance().getUniqueIDGenerator();
            final TeamRewardsNote teamRewardsNote = new TeamRewardsNote(uniqueIDGenerator.nextId());
            teamRewardsNote.setBusiness_no(teamPromote.getBusiness_no());
            teamRewardsNote.setPlayerId(teamPromote.getPlayerId());
            teamRewardsNote.setAmount(commission);
            teamRewardsNote.setCurrencyId(currencyId);
            teamRewardsNote.setStatus(1);
            EntityDaoMrg.getInstance().getDao(TeamRewardsNoteDao.class).insert(teamRewardsNote);

            final GameLog platform_promoteTeamLog = new GameLog("platform_promoteTeamLog")
                    .append("number", GuidGeneratorUtils.generateOrderId())
                    .append("gameNoteId", noteId)
                    .append("business_no", teamRewardsNote.getBusiness_no())//商户号
                    .append("platformId", c_gameApi.getPlatformId())
                    .append("gameId", c_gameApi.getGameId())
                    .append("gameType", c_gameApi.getType())
                    .append("noteId", teamRewardsNote.getNoteId())//生成的唯一id
                    .append("rebateMethod", rebateMethod)
                    .append("backCashRate", c_cashBack.getTeamCashBackRate())
                    .append("amount", teamRewardsNote.getAmount())
                    .append("currencyId", teamRewardsNote.getCurrencyId())
                    .append("playerId", teamPromote.getPlayerId())//玩家id
                    .append("playerName", teamPromote.getPlayerName())
                    .append("teamId", teamId)//团长id
                    .append("logTime", TimeUtil.currentTimeMillis());
            HallServer.getInstance().getLogProducerMrg().send(platform_promoteTeamLog);

        } catch (Exception e) {
            LOGGER.error("calculateTeamCommission error", e);
        }
    }

    @Override
    public void calculateThreeLevelCommission(Player player, C_GameApi c_gameApi, String noteId, int currencyId, double betAmount, double validBet) {
        try {
            final PlayerPromote playerPromote = player.getPlayerPromote();
            if (playerPromote == null) {
                return;
            }

            if (playerPromote.getSuperiorId() == 0) {
                return;
            }

            final int size = playerPromote.getSuperiors().size();
            if (size < 3) {
                return;
            }

            final long threeLevelId = playerPromote.getSuperiors().get(size - 3);
            final PlayerPromote threeLevelPromote = PlayerMrg.getInstance().findDbPlayerPromote(threeLevelId);

            if (threeLevelPromote == null) {
                LOGGER.warn("playerId：{}，threeLevelId：{}，not exist", player.getPlayerId(), threeLevelId);
                return;
            }

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                return;
            }

            final String rebateMethod = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "rebateMethod");
            final C_CashBack c_cashBack = merchantData.findC_CashBack(this.getClass().getSimpleName(), c_gameApi.getType());
            if (c_cashBack == null) {
                return;
            }

            double commission = 0;
            if (c_cashBack.getRebateMethod() == 4) {//佣金
                final double a = BigDecimalUtils.mul(betAmount, c_cashBack.getEffectiveRate(), 9);
                commission = BigDecimalUtils.mul(BigDecimalUtils.mul(a, c_cashBack.getGameCashBackRate(), 9), c_cashBack.getThreeLevelCashBackRate(), 9);
            } else if (c_cashBack.getRebateMethod() == 1) {//有效
                commission = BigDecimalUtils.mul(BigDecimalUtils.mul(validBet, c_cashBack.getEffectiveRate(), 9), c_cashBack.getThreeLevelCashBackRate(), 9);
            }

            //LOGGER.warn("playerId：{}，calculateThreeLevelCommission：{}", player.getPlayerId(), commission);

            if (commission == 0) {
                return;
            }

            final Map<Integer, ThreeLevelRewards> threeLevelRewardsMap = threeLevelPromote.getThreeLevelRewardsMap();
            ThreeLevelRewards threeLevelRewards = threeLevelRewardsMap.computeIfAbsent(currencyId, k -> new ThreeLevelRewards());
            threeLevelRewards.incAvailable(commission);
            threeLevelRewards.incTotalReceived(commission);

            EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class)
                    .updateThreeLevelRewards(threeLevelPromote, IntLists.singleton(currencyId));

            final UniqueIDGenerator uniqueIDGenerator = HallServer.getInstance().getUniqueIDGenerator();
            final ThreeLevelRewardsNote threeLevelRewardsNote = new ThreeLevelRewardsNote(uniqueIDGenerator.nextId());
            threeLevelRewardsNote.setBusiness_no(threeLevelPromote.getBusiness_no());
            threeLevelRewardsNote.setPlayerId(threeLevelPromote.getPlayerId());
            threeLevelRewardsNote.setAmount(commission);
            threeLevelRewardsNote.setCurrencyId(currencyId);
            threeLevelRewardsNote.setStatus(1);
            EntityDaoMrg.getInstance().getDao(ThreeLevelRewardsNoteDao.class).insert(threeLevelRewardsNote);

            final GameLog platform_promoteThreeLevelLog = new GameLog("platform_promoteThreeLevelLog")
                    .append("number", GuidGeneratorUtils.generateOrderId())
                    .append("gameNoteId", noteId)
                    .append("business_no", threeLevelRewardsNote.getBusiness_no())//商户号
                    .append("platformId", c_gameApi.getPlatformId())
                    .append("gameId", c_gameApi.getGameId())
                    .append("gameType", c_gameApi.getType())
                    .append("noteId", threeLevelRewardsNote.getNoteId())//生成的唯一id
                    .append("rebateMethod", rebateMethod)
                    .append("backCashRate", c_cashBack.getThreeLevelCashBackRate())
                    .append("amount", threeLevelRewardsNote.getAmount())
                    .append("currencyId", threeLevelRewardsNote.getCurrencyId())
                    .append("playerId", threeLevelPromote.getPlayerId())//玩家id
                    .append("playerName", threeLevelPromote.getPlayerName())
                    .append("threeLevelId", threeLevelId)//三级id
                    .append("logTime", TimeUtil.currentTimeMillis());
            HallServer.getInstance().getLogProducerMrg().send(platform_promoteThreeLevelLog);

        } catch (Exception e) {
            LOGGER.error("calculateThreeLevelCommission error", e);
        }
    }

    @Override
    public void invitedRewards(Player player, int currencyId, double wagered, double recharge) {
        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }

        final String referralRewardOpen = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "referralRewardOpen");
        if (StringUtil.isNullOrEmpty(referralRewardOpen) || !Boolean.parseBoolean(referralRewardOpen)) {
            return;
        }

        final PlayerPromote playerPromote = player.getPlayerPromote();
        if (playerPromote.getSuperiorId() == 0) {
            return;
        }

        if (playerPromote.isInvitedReward()) {
            return;
        }

        final String invitedRewards = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "invitedRewards");
        final String invitedTurnoverMul = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "invitedTurnoverMul");
        final String invitedWagered = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "invitedWagered");
        final String invitedRecharge = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "invitedRecharge");

        if (!StringUtil.isNullOrEmpty(invitedWagered)) {
            final String[] wagereds = invitedWagered.split(Symbol.MAOHAO_REG);
            if (currencyId != Integer.parseInt(wagereds[0])) {
                return;
            }
            final Stats stats = player.getStats(currencyId);
            if (stats.getTotalValidBetAmount() < Double.parseDouble(wagereds[1])) {
                return;
            }
        }

        if (!StringUtil.isNullOrEmpty(invitedRecharge)) {
            final String[] recharges = invitedRecharge.split(Symbol.MAOHAO_REG);
            if (currencyId != Integer.parseInt(recharges[0])) {
                return;
            }
            final Stats stats = player.getStats(currencyId);
            if (stats.getTotalRechargeAmount() < Double.parseDouble(recharges[1])) {
                return;
            }
        }

        playerPromote.setInvitedReward(true);
        final Update update = new Update();
        update.set(PlayerPromoteFields.invitedReward, playerPromote.isInvitedReward());
        EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class).updatePromotion(player.getPlayerId(), update);

        final String[] rewards = invitedRewards.split(Symbol.MAOHAO_REG);
        final RewardRequest rewardRequest = new RewardRequest();
        final RewardReason rewardReason = RewardReason.InvitedRewards;
        rewardRequest.addCurrency(Integer.parseInt(rewards[0]), Double.parseDouble(rewards[1]));
        CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getBonusCurrencyMap(), rewardReason);

        //TODO 打码
        final TurnoverReason turnoverReason = TurnoverReason.InvitedRewards;
        ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                (IPlayerScript script) -> script.drawStandard(player, turnoverReason, Integer.parseInt(rewards[0]), Double.parseDouble(rewards[1]),
                        BigDecimalUtils.mul(Double.parseDouble(rewards[1]), Integer.parseInt(invitedTurnoverMul), 4)));

        if (!rewardRequest.getCurrencyMap().isEmpty()) {
            final Tuple2<Integer, Double> tuple2 = rewardRequest.getReward();
            ScriptLoader.getInstance().consumerScript("BonusScript",
                    (IBonusScript script) -> script.addBonusNote(TransactionFrom.Affiliate_InvitedRewards, player, tuple2.getFirst(), tuple2.getSecond()));
        }
    }
}
