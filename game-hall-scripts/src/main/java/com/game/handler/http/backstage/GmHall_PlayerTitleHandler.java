package com.game.handler.http.backstage;

import com.game.dao.player.PlayerDao;
import com.game.dao.player.PlayerPromoteDao;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.entity.player.promote.PlayerPromote;
import com.game.entity.player.promote.PlayerPromoteFields;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

//http://127.0.0.1:8380/gmHall/playerTitle?playerId=33403441&state=2
@IHandlerEntity(path = "/gmHall/playerTitle", desc = "封号,冻结")
public class GmHall_PlayerTitleHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(GmHall_PlayerTitleHandler.class);

    @Override
    public void run() {
        try {
            final String playerId = (String) paramsMap.get("playerId");
            final String state = (String) paramsMap.get("state");

            if (StringUtil.isNullOrEmpty(playerId) || StringUtil.isNullOrEmpty(state)) {
                PlayerMrg.responseHttp(ErrorCode.Parameter_Error.getCode(), session, pid);
                return;
            }

            final Player player = PlayerMrg.getInstance().findDbPlayer(Long.parseLong(playerId));
            if (player == null) {
                PlayerMrg.responseHttp(ErrorCode.Account_NotExist.getCode(), session, pid);
                return;
            }

            PlayerMrg.responseHttp(ErrorCode.Success.getCode(), session, pid);

            //1.正常 2.封号 3.冻结
            player.setState(Integer.parseInt(state));
            //更新数据库
            EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                    .updatePlayerField(player.getPlayerId(), PlayerFields.state, player.getState());

            if (player.isOnline() && player.isTitle()) {
                PlayerMrg.getInstance().sendKickOutPlayerMsg(player);
                PlayerMrg.getInstance().signOut(player);
            }
        } catch (Exception e) {
            LOGGER.error("", e);
            PlayerMrg.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session, pid);
        }
    }
}
