package com.game.handler.http.backstage;

import com.game.c_entity.merchant.C_Activity;
import com.game.engine.io.handler.HttpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.util.misc.Tuple2;
import com.game.entity.player.Player;
import com.game.enums.ErrorCode;
import com.game.enums.TransactionFrom;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.currency.RewardRequest;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.IBonusScript;
import com.game.hall.script.activity.IActivityScript;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

//http://127.0.0.1:8380/gmHall/firstDepositGiveUp?playerId=33403441
@IHandlerEntity(path = "/gmHall/firstDepositGiveUp", desc = "首充放弃")
public class GmHall_FirstDepositGiveUpHandler extends HttpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(GmHall_FirstDepositGiveUpHandler.class);

    @Override
    public void run() {
        try {
            final String playerId = (String) paramsMap.get("playerId");

            if (StringUtil.isNullOrEmpty(playerId)) {
                PlayerMrg.responseHttp(ErrorCode.Parameter_Error.getCode(), session, pid);
                return;
            }

            final Player player = PlayerMrg.getInstance().findDbPlayer(Long.parseLong(playerId));
            if (player == null) {
                PlayerMrg.responseHttp(ErrorCode.Account_NotExist.getCode(), session, pid);
                return;
            }

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                PlayerMrg.responseHttp(ErrorCode.Data_Error.getCode(), session, pid);
                return;
            }

            final C_Activity c_activity = merchantData.findC_Activity(this.getClass().getSimpleName(), ActivityMrg.FIRSTDEPOSIT_GIVEUP);
            if (c_activity == null) {
                PlayerMrg.responseHttp(ErrorCode.Data_Error.getCode(), session, pid);
                return;
            }

            final RewardRequest rewardRequest = ScriptLoader.getInstance().functionScript("FirstDepositGiveUpScript",
                    (IActivityScript script) -> script.receiveReward(player, c_activity));


            if (rewardRequest != null && !rewardRequest.getCurrencyMap().isEmpty()) {

                ScriptLoader.getInstance().consumerScript("ActivityScript",
                        (IActivityScript script) -> script.sendActivityLog(player, 2, c_activity, rewardRequest));

                final Tuple2<Integer, Double> tuple2 = rewardRequest.getReward();
                ScriptLoader.getInstance().consumerScript("BonusScript", (IBonusScript script) ->
                        script.addBonusNote(TransactionFrom.FirstDepositGiveUp, player, tuple2.getFirst(), tuple2.getSecond()));
            }

            PlayerMrg.responseHttp(ErrorCode.Success.getCode(), session, pid);
        } catch (Exception e) {
            LOGGER.error("", e);
            PlayerMrg.responseHttp(ErrorCode.Internal_Server_Error.getCode(), session, pid);
        }
    }
}
