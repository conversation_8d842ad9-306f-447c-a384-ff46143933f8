package com.game.handler.tcp.activity;

import com.game.c_entity.merchant.*;
import com.game.c_entity.middleplatform.C_BaseCurrency;
import com.game.c_entity.middleplatform.C_BaseExchangeRate;
import com.game.c_entity.middleplatform.C_BaseMerchant;
import com.game.dao.game.GameNoteDao;
import com.game.dao.order.RechargeOrderDao;
import com.game.dao.player.PlayerPromoteDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.math.MathUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.game.GameNote;
import com.game.entity.game.GameNoteFields;
import com.game.entity.game.GameWagerStat;
import com.game.entity.order.RechargeOrder;
import com.game.entity.order.RechargeOrderFields;
import com.game.entity.player.Player;
import com.game.entity.player.activity.firstChargeSignIn.FirstChargeSignInInfo;
import com.game.entity.player.activity.firstDepositInviteBonus.FirstDepositInviteBonusInfo;
import com.game.entity.player.activity.mysteryBonus.MysteryBonusInfo;
import com.game.entity.player.activity.mysteryBonus.MysteryRewardInfo;
import com.game.entity.player.activity.piggyBank.PiggyBankInfo;
import com.game.entity.player.activity.rechargeRecover.RechargeRecoverInfo;
import com.game.entity.player.activity.redEnvelopeRain.RedEnvelopeRainInfo;
import com.game.entity.player.activity.rewardBox.RewardBoxInfo;
import com.game.entity.player.activity.wageredRebates.WageredRebatesInfo;
import com.game.entity.player.promote.PlayerPromote;
import com.game.enums.Currency;
import com.game.enums.ErrorCode;
import com.game.enums.GameType;
import com.game.enums.redis.RedisHall;
import com.game.enums.redis.RedisRanking;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.*;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.IGameScript;
import com.game.hall.script.IInboxScript;
import com.game.manager.EntityDaoMrg;
import com.proto.ActivityMessage;
import com.proto.CommonMessage;
import com.proto.MIDMessage;
import io.lettuce.core.ScoredValue;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

@IHandlerEntity(mid = MIDMessage.MID.ReqActivityData_VALUE, msg = ActivityMessage.ReqActivityDataMessage.class)
public class ReqActivityDataHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqActivityDataHandler.class);

    @Override
    public void run() {
        final ActivityMessage.ResActivityDataMessage.Builder res = ActivityMessage.ResActivityDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResActivityData_VALUE);
        try {
            final ActivityMessage.ReqActivityDataMessage req = (ActivityMessage.ReqActivityDataMessage) getMessage();
            final String host = req.getHost();
            final int language = req.getLanguage();

            final C_BaseMerchant c_baseHostMerchant = DataHallMrg.getInstance().findC_BaseHostMerchant(this.getClass().getSimpleName(), host);
            if (c_baseHostMerchant == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final String business_no = c_baseHostMerchant.getBusiness_no();
            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), business_no);
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            Player player = null;
            if (pid > 0) {
                player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
                if (player == null) {
                    res.setError(ErrorCode.Player_Offline.getCode());
                    MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                    return;
                }
            }

            try {
                //红包雨
                final ActivityMessage.BonusRainInfo bonusRainInfo = buildBonusRainInfo(player, language, merchantData);
                if (bonusRainInfo != null) {
                    res.setBonusRainInfo(bonusRainInfo);
                }
            } catch (Exception e) {
                LOGGER.error("bonusRainInfo", e);
            }

            try {
                //推荐宝箱
                final ActivityMessage.RewardBoxInfo rewardBoxInfo = buildRewardBoxInfo(player, language, merchantData);
                if (rewardBoxInfo != null) {
                    res.setRewardBoxInfo(rewardBoxInfo);
                }
            } catch (Exception e) {
                LOGGER.error("rewardBoxInfo", e);
            }

            try {
                //神秘奖金
                final ActivityMessage.MysteryBonusInfo mysteryBonusInfo = buildMysteryBonusInfo(player, language, merchantData);
                if (mysteryBonusInfo != null) {
                    res.setMysteryBonusInfo(mysteryBonusInfo);
                }
            } catch (Exception e) {
                LOGGER.error("mysteryBonusInfo", e);
            }

            try {
                //存钱罐
                final ActivityMessage.PiggyBankInfo piggyBankInfo = buildPiggyBankInfo(player, language, merchantData);
                if (piggyBankInfo != null) {
                    res.setPiggyBankInfo(piggyBankInfo);
                }
            } catch (Exception e) {
                LOGGER.error("piggyBankInfo", e);
            }

            try {
                //连续充值
                final ActivityMessage.ContinuousDepositInfo continuousDepositInfo = buildContinuousDepositInfo(player, language, merchantData);
                if (continuousDepositInfo != null) {
                    res.setContinuousDepositInfo(continuousDepositInfo);
                }
            } catch (Exception e) {
                LOGGER.error("continuousDeposit", e);
            }

            try {
                //首充签到
                final ActivityMessage.FirstChargeSignInInfo firstChargeSignInInfo = buildFirstChargeSignInInfo(player, language, merchantData);
                if (firstChargeSignInInfo != null) {
                    res.setFirstChargeSignInInfo(firstChargeSignInInfo);
                }
            } catch (Exception e) {
                LOGGER.error("firstChargeSignInInfo", e);
            }

            try {
                //充值返奖
                final ActivityMessage.RechargeRecoverInfo rechargeRecoverInfo = buildRechargeRecoverInfo(player, language, merchantData);
                if (rechargeRecoverInfo != null) {
                    res.setRechargeRecoverInfo(rechargeRecoverInfo);
                }
            } catch (Exception e) {
                LOGGER.error("rechargeRecoverInfo", e);
            }

            try {
                //投注返利
                final ActivityMessage.WageredRebatesInfo wageredRebatesInfo = buildWageredRebatesInfo(player, language, merchantData);
                if (wageredRebatesInfo != null) {
                    res.setWageredRebatesInfo(wageredRebatesInfo);
                }
            } catch (Exception e) {
                LOGGER.error("wageredRebatesInfo", e);
            }

            try {
                //充值邀请
                final ActivityMessage.DepositInviteBonusInfo depositInviteBonusInfo = buildDepositInviteBonusInfo(player, language, merchantData);
                if (depositInviteBonusInfo != null) {
                    res.setDepositInviteBonusInfo(depositInviteBonusInfo);
                }
            } catch (Exception e) {
                LOGGER.error("depositInviteBonusInfo", e);
            }

            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqActivityDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    private ActivityMessage.BonusRainInfo buildBonusRainInfo(Player player, int language, MerchantData merchantData) {
        final ActivityMessage.BonusRainInfo.Builder bonusRainInfo = ActivityMessage.BonusRainInfo.newBuilder();

        final C_RedEnvelopeRain c_redEnvelopeRain = findC_RedEnvelopeRain(language, merchantData);
        if (c_redEnvelopeRain == null) {
            return null;
        }

        final C_RedEnvelopeRain.Rule rule = c_redEnvelopeRain.getRuleMap().get(language);
        if (rule != null) {
            bonusRainInfo.setRule(rule.getRule())
                    .setIcon(rule.getIcon())
                    .setActivityId(ActivityMrg.RED_ENVELOPE_RAIN)
                    .setCId(c_redEnvelopeRain.getC_id());
        }

        bonusRainInfo.setMaxAmount(buildItemShow(c_redEnvelopeRain.getCurrencyId(), c_redEnvelopeRain.getShowTotalAmount()))
                .setShowSingleAmount(buildItemShow(c_redEnvelopeRain.getCurrencyId(), c_redEnvelopeRain.getShowSingleAmount()))
                .setReceiveType(c_redEnvelopeRain.getReceiveType());

        final C_RedEnvelopeRain.RedEnvelope redEnvelope = c_redEnvelopeRain.getRedEnvelopeMap().values().stream().findFirst().get();

        final List<C_RedEnvelopeRain.RedEnvelope> c_RedEnvelopeList = new ArrayList<>(c_redEnvelopeRain.getRedEnvelopeMap().values());
        Collections.reverse(c_RedEnvelopeList);

        for (C_RedEnvelopeRain.RedEnvelope c_redEnvelope : c_RedEnvelopeList) {
            bonusRainInfo.addRedEnvelopeList(buildRedEnvelopeInfo(c_redEnvelope));
        }

        for (C_RedEnvelopeRain.TimePeriod timePeriod : c_redEnvelopeRain.getTimePeriodMap().values()) {
            bonusRainInfo.addTimePeriodList(buildTimePeriodInfo(c_redEnvelopeRain.getTimeZone(), timePeriod));
        }

        if (player != null) {
            final int recentTime = Math.min(c_redEnvelopeRain.getRecentTime(), 15);
            final RedEnvelopeRainInfo redEnvelopeRainInfo = player.getRedEnvelopeRainInfo();
            bonusRainInfo.addAllReceive(redEnvelopeRainInfo.getReceive());

            final long start1 = TimeUtil.currentTimeMillis();
            final GameNote gameNote = EntityDaoMrg.getInstance().getDao(GameNoteDao.class)
                    .aggregateBet(pid, player.getCurrencyId(), GameNoteFields.validBets, TimeUtil.currentTimeMillis() - recentTime * TimeUtil.DAY, TimeUtil.currentTimeMillis());
            if (TimeUtil.currentTimeMillis() - start1 > 10) {
                LOGGER.warn("gameNote query cost time：{}", TimeUtil.currentTimeMillis() - start1);
            }
            if (redEnvelope.getW_currencyId() == Currency.USD.getCurrencyId()) {
                final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), player.getCurrencyId());
                if (c_baseExchangeRate != null) {
                    bonusRainInfo.setCurrWagered(buildItemShow(Currency.USD.getCurrencyId(), BigDecimalUtils.mul(gameNote.getValidBets(), c_baseExchangeRate.getExchangeRate(), 9)));
                }
            } else {
                bonusRainInfo.setCurrWagered(buildItemShow(player.getCurrencyId(), gameNote.getValidBets()));
            }

            final long start = TimeUtil.currentTimeMillis();
            double rechargeAmount = 0;
            final String withdrawExchangeOpen = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "withdrawExchangeOpen");
            if (!StringUtil.isNullOrEmpty(withdrawExchangeOpen) && Boolean.parseBoolean(withdrawExchangeOpen)) {
                for (C_RechargeExchange c_rechargeExchange : merchantData.getC_rechargeExchangeMap().values()) {
                    final RechargeOrder rechargeOrder = EntityDaoMrg.getInstance().getDao(RechargeOrderDao.class)
                            .aggregateRechargeAmount(pid, c_rechargeExchange.getCurrencyId(), RechargeOrderFields.amounts, TimeUtil.currentTimeMillis() - recentTime * TimeUtil.DAY, TimeUtil.currentTimeMillis());

                    final double exchangeAmount = BigDecimalUtils.mul(rechargeOrder.getAmounts(), c_rechargeExchange.getRate(), 9);
                    rechargeAmount = BigDecimalUtils.add(rechargeAmount, exchangeAmount, 4);
                }
            } else {
                final RechargeOrder rechargeOrder = EntityDaoMrg.getInstance().getDao(RechargeOrderDao.class)
                        .aggregateRechargeAmount(pid, player.getCurrencyId(), RechargeOrderFields.amounts, TimeUtil.currentTimeMillis() - recentTime * TimeUtil.DAY, TimeUtil.currentTimeMillis());
                rechargeAmount = rechargeOrder.getAmounts();
            }

            if (TimeUtil.currentTimeMillis() - start > 10) {
                LOGGER.warn("rechargeOrder query cost time：{}", TimeUtil.currentTimeMillis() - start);
            }
            if (redEnvelope.getR_currencyId() == Currency.USD.getCurrencyId()) {
                final C_BaseExchangeRate c_baseExchangeRate = DataHallMrg.getInstance().findC_BaseExchangeRate(this.getClass().getSimpleName(), player.getCurrencyId());
                if (c_baseExchangeRate != null) {
                    bonusRainInfo.setCurrRecharge(buildItemShow(Currency.USD.getCurrencyId(), BigDecimalUtils.mul(rechargeAmount, c_baseExchangeRate.getExchangeRate(), 9)));
                }
            } else {
                bonusRainInfo.setCurrRecharge(buildItemShow(player.getCurrencyId(), rechargeAmount));
            }

            final double finalRechargeAmount = rechargeAmount;
            HallServer.getInstance().asyncExecute(pid, () -> {
                final RedEnvelopeRainInfo redEnvelopeRain = player.getRedEnvelopeRainInfo();
                redEnvelopeRain.getWageredMap().put(player.getCurrencyId(), gameNote.getValidBets());
                redEnvelopeRain.getRechargeAmountMap().put(player.getCurrencyId(), finalRechargeAmount);
            });
        }
        return bonusRainInfo.build();
    }

    private ActivityMessage.RedEnvelopeInfo buildRedEnvelopeInfo(C_RedEnvelopeRain.RedEnvelope redEnvelope) {
        final ActivityMessage.RedEnvelopeInfo.Builder redEnvelopeInfo = ActivityMessage.RedEnvelopeInfo.newBuilder();
        redEnvelopeInfo.setId(redEnvelope.getId())
                .setRCurrencyId(redEnvelope.getR_currencyId())
                .setMinRecharge(redEnvelope.getMinRecharge())
                .setMaxRecharge(redEnvelope.getMaxRecharge())
                .setWCurrencyId(redEnvelope.getW_currencyId())
                .setMinWagered(redEnvelope.getMinWagered())
                .setMaxWagered(redEnvelope.getMaxWagered())
                .setMinVip(redEnvelope.getMinVip())
                .setMaxVip(redEnvelope.getMaxVip());
        return redEnvelopeInfo.build();
    }

    private ActivityMessage.TimePeriodInfo buildTimePeriodInfo(String timeZone, C_RedEnvelopeRain.TimePeriod timePeriod) {
        final long time = TimeUtil.getTimeBeginOfToday(TimeUtil.currentTimeMillis(), timeZone);
        final ActivityMessage.TimePeriodInfo.Builder timePeriodInfo = ActivityMessage.TimePeriodInfo.newBuilder();
        timePeriodInfo.setId(timePeriod.getId())
                .setStartTime(time + timePeriod.getStartTime())
                .setEndTime(time + timePeriod.getEndTime())
                .setShowAmount(buildItemShow(timePeriod.getCurrencyId(), timePeriod.getAmount()));
        return timePeriodInfo.build();
    }

    private CommonMessage.DItemShow buildItemShow(int itemId, double num) {
        return CommonMessage.DItemShow.newBuilder()
                .setItemId(itemId)
                .setNum(num)
                .build();
    }

    private C_RedEnvelopeRain findC_RedEnvelopeRain(int language, MerchantData merchantData) {
        final Map<Integer, C_RedEnvelopeRain> c_redEnvelopeRainWeekMap = merchantData.getC_redEnvelopeRainWeekMap();
        for (C_RedEnvelopeRain c_redEnvelopeRain : c_redEnvelopeRainWeekMap.values()) {
            if (!c_redEnvelopeRain.getLanguages().contains(language)) {
                continue;
            }

            final String timeZone = c_redEnvelopeRain.getTimeZone();
            final int month = TimeUtil.getDayOfMonth(timeZone);
            if (c_redEnvelopeRain.getMonthlys().contains(month)) {
                return c_redEnvelopeRain;
            }
        }

        final Map<Integer, C_RedEnvelopeRain> c_redEnvelopeRainMap = merchantData.getC_redEnvelopeRainDayMap();
        for (C_RedEnvelopeRain c_redEnvelopeRain : c_redEnvelopeRainMap.values()) {
            if (!c_redEnvelopeRain.getLanguages().contains(language)) {
                continue;
            }

            final String timeZone = c_redEnvelopeRain.getTimeZone();
            final int day = TimeUtil.getDayOfWeek(timeZone);
            if (c_redEnvelopeRain.getCycles().contains(day)) {
                return c_redEnvelopeRain;
            }
        }
        return null;
    }

    private ActivityMessage.RewardBoxInfo buildRewardBoxInfo(Player player, int language, MerchantData merchantData) {
        final ActivityMessage.RewardBoxInfo.Builder rewardBoxInfo = ActivityMessage.RewardBoxInfo.newBuilder();
        final C_RewardBox c_rewardBox = merchantData.findC_RewardBox(this.getClass().getSimpleName(), ActivityMrg.REWARD_BOX);
        if (c_rewardBox == null) {
            return null;
        }

        if (TimeUtil.currentTimeMillis() < c_rewardBox.getStartTime() || TimeUtil.currentTimeMillis() >= c_rewardBox.getEndTime()) {
            return null;
        }

        final C_RewardBox.Rule rule = c_rewardBox.getRuleMap().get(language);
        if (rule == null) {
            return null;
        }

        rewardBoxInfo.setIcon(rule.getIcon())
                .setRule(rule.getRule())
                .setCurrencyId(c_rewardBox.getInviteInfo().currencyId)
                .setRechargeAmount(c_rewardBox.getInviteInfo().rechargeAmount)
                .setWageredAmount(c_rewardBox.getInviteInfo().wageredAmount)
                .setActivityId(ActivityMrg.REWARD_BOX)
                .setCId(c_rewardBox.getC_id());
        for (C_RewardBox.Box box : c_rewardBox.getBoxMap().values()) {
            rewardBoxInfo.addBoxList(buildBoxInfo(box));
        }

        if (player != null) {
            final String referralLink = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "referralLink");
            final String url = referralLink + "?" + "referralCode=" + player.getInvitationCode() + "-~." + "activity=" + c_rewardBox.getActivityId() + "_" + c_rewardBox.getC_id();
            rewardBoxInfo.setReferralLink(url);

            final Set<String> invitePlayerIds = RedisPoolManager.getInstance().function(jedis ->
                    jedis.sync().smembers(RedisHall.Platform_Role_Map_RewardBoxInvite.getKey(pid)));

            final Set<String> referralPlayerIds = RedisPoolManager.getInstance().function(jedis ->
                    jedis.sync().smembers(RedisHall.Platform_Role_Map_RewardBoxReferral.getKey(pid)));

            final RewardBoxInfo boxInfo = player.getRewardBoxInfo();
            final List<Long> allPlayers = new ArrayList<>();
            if (invitePlayerIds != null) {
                allPlayers.addAll(invitePlayerIds.stream().map(Long::parseLong).toList());
            }
            if (referralPlayerIds != null) {
                allPlayers.addAll(referralPlayerIds.stream().map(Long::parseLong).toList());
            }
            rewardBoxInfo.setDirectNum(allPlayers.stream().distinct().toList().size())
                    .addAllReceive(boxInfo.getReceive());
        }
        return rewardBoxInfo.build();
    }

    private ActivityMessage.BoxInfo buildBoxInfo(C_RewardBox.Box box) {
        return ActivityMessage.BoxInfo.newBuilder()
                .setNum(box.num)
                .setCurrencyId(box.currencyId)
                .setAmount(box.amount)
                .build();
    }

    private ActivityMessage.MysteryBonusInfo buildMysteryBonusInfo(Player player, int language, MerchantData merchantData) {
        final ActivityMessage.MysteryBonusInfo.Builder mysteryBonusInfo = ActivityMessage.MysteryBonusInfo.newBuilder();
        final C_MysteryBonus c_mysteryBonus = merchantData.findC_MysteryBonus(this.getClass().getSimpleName(), ActivityMrg.MYSTERY_BONUS);
        if (c_mysteryBonus == null) {
            return null;
        }

        if (TimeUtil.currentTimeMillis() < c_mysteryBonus.getStartTime() || TimeUtil.currentTimeMillis() >= c_mysteryBonus.getEndTime()) {
            return null;
        }

        final C_MysteryBonus.Rule rule = c_mysteryBonus.getRuleMap().get(language);
        if (rule == null) {
            return null;
        }

        mysteryBonusInfo.setIcon(rule.getIcon())
                .setRule(rule.getRule())
                .setActivityId(ActivityMrg.MYSTERY_BONUS)
                .setCId(c_mysteryBonus.getC_id());

        if (player != null) {
            final MysteryBonusInfo playerMysteryBonusInfo = player.getMysteryBonusInfo();

            final ActivityMessage.MysteryReward.Builder mysteryReward = ActivityMessage.MysteryReward.newBuilder();
            mysteryBonusInfo.setSignUpTime(player.getCreateTime())
                    .addAllReceive(playerMysteryBonusInfo.getReceive());

            final double recharge = playerMysteryBonusInfo.getRechargeAmountMap().getOrDefault(player.getCurrencyId(), 0d);
            for (C_MysteryBonus.MysteryRewardInfo c_mysteryReward : c_mysteryBonus.getMysteryRewardInfoMap().values()) {
                final MysteryRewardInfo playerMysteryRewardInfo = playerMysteryBonusInfo.getMysteryRewardInfoMap().get(c_mysteryReward.getId());
                if (playerMysteryRewardInfo == null) {
                    continue;
                }
                mysteryReward.clear();
                mysteryReward.setId(playerMysteryRewardInfo.getC_id())
                        .setSettlementTime(playerMysteryRewardInfo.getSettlementTime())
                        .setExpiredTime(playerMysteryRewardInfo.getExpiredTime())
                        .setCurrRecharge(CommonMrg.buildDItemShow(player.getCurrencyId(), recharge));
                if (TimeUtil.currentTimeMillis() >= playerMysteryRewardInfo.getSettlementTime() && TimeUtil.currentTimeMillis() < playerMysteryRewardInfo.getExpiredTime()) {
                    final C_MysteryBonus.RewardGear rewardGear = findRewardGear(mysteryReward.getId(), recharge, c_mysteryBonus);
                    if (rewardGear != null) {
                        final double reward = BigDecimalUtils.round(MathUtils.random(rewardGear.getRewardMin(), rewardGear.getRewardMax()), 4);
                        playerMysteryRewardInfo.setCurrencyId(rewardGear.getCurrencyId());
                        playerMysteryRewardInfo.setBonus(reward);

                        mysteryReward.setCurrencyId(rewardGear.getCurrencyId())
                                .setBonus(reward);
                    }
                }

                if (TimeUtil.currentTimeMillis() >= playerMysteryRewardInfo.getExpiredTime()) {
                    mysteryReward.setCurrencyId(playerMysteryRewardInfo.getCurrencyId())
                            .setBonus(playerMysteryRewardInfo.getBonus());
                }

                for (C_MysteryBonus.RewardGear rewardGear : c_mysteryReward.getShowRewardGears()) {
                    mysteryReward.addMysteryGearList(buildRewardGear(rewardGear));
                }
                mysteryBonusInfo.addMysteryReward(mysteryReward.build());
            }
        }

        return mysteryBonusInfo.build();
    }

    private ActivityMessage.MysteryGear buildRewardGear(C_MysteryBonus.RewardGear rewardGear) {
        final ActivityMessage.MysteryGear.Builder mysteryGear = ActivityMessage.MysteryGear.newBuilder();
        mysteryGear.setId(rewardGear.getId())
                .setCurrencyId(rewardGear.getCurrencyId())
                .setRechargeMin(rewardGear.getRechargeMin())
                .setRechargeMax(rewardGear.getRechargeMax())
                .setRewardMin(rewardGear.getRewardMin())
                .setRewardMax(rewardGear.getRewardMax());
        return mysteryGear.build();
    }

    private ActivityMessage.PiggyBankInfo buildPiggyBankInfo(Player player, int language, MerchantData merchantData) {
        final C_PiggyBank c_piggyBank = merchantData.findC_PiggyBank(this.getClass().getSimpleName(), ActivityMrg.PIGGY_BANK);
        if (c_piggyBank == null) {
            return null;
        }

        final C_PiggyBank.Rule rule = c_piggyBank.getRuleMap().get(language);
        if (rule == null) {
            return null;
        }

        final ActivityMessage.PiggyBankInfo.Builder piggyBankInfo = ActivityMessage.PiggyBankInfo.newBuilder();
        piggyBankInfo.setIcon(StringUtil.isNullOrEmpty(rule.getIcon()) ? "" : rule.getIcon())
                .setRule(rule.getRule())
                .setActivityId(ActivityMrg.PIGGY_BANK)
                .setCId(c_piggyBank.getC_id());
        if (player != null) {
            final PiggyBankInfo playerPiggyBankInfo = player.getPiggyBankInfo();
            final double recharge = playerPiggyBankInfo.getRechargeAmountMap().getOrDefault(player.getCurrencyId(), 0d);
            int currDay = playerPiggyBankInfo.getCurrDay();
            //判断是否重置
            final int day = TimeUtil.getNaturalDays(TimeUtil.currentTimeMillis(), playerPiggyBankInfo.getReceiveTime(), c_piggyBank.getTimeZone());
            if (playerPiggyBankInfo.getReceiveTime() > 0 && day > currDay + 1) {
                playerPiggyBankInfo.setCurrDay(0);
                final int receive = playerPiggyBankInfo.getReceive().isEmpty() ? -1 : playerPiggyBankInfo.getReceive().getFirst();
                playerPiggyBankInfo.getReceive().clear();
                if (receive == 0) {
                    playerPiggyBankInfo.getReceive().add(0);
                }
                playerPiggyBankInfo.setReceiveTime(0);
                currDay = 0;
            }

            if (playerPiggyBankInfo.getReceiveTime() > 0) {
                if (Objects.equals(TimeUtil.getDateTimeFormat(TimeUtil.currentTimeMillis(), TimeUtil.YYYYMMDD, player.getTimeZone()),
                        TimeUtil.getDateTimeFormat(playerPiggyBankInfo.getReceiveTime(), TimeUtil.YYYYMMDD, player.getTimeZone()))) {
                    currDay -= 1;
                }
            }

            piggyBankInfo.setCurrRecharge(CommonMrg.buildDItemShow(player.getCurrencyId(), recharge))
                    .setCurrDay(currDay + 1)
                    .addAllReceiveDay(playerPiggyBankInfo.getReceive());
        }

        for (C_PiggyBank.PiggyInfo c_piggyInfo : c_piggyBank.getPiggyInfoMap().values()) {
            final ActivityMessage.PiggyBankRewardInfo.Builder piggyBankRewardInfo = ActivityMessage.PiggyBankRewardInfo.newBuilder();
            piggyBankRewardInfo.setLevel(c_piggyInfo.getId())
                    .setAchieveRecharge(c_piggyInfo.getRecharge());
            for (C_PiggyBank.PiggyReward piggyReward : c_piggyInfo.getPiggyRewards()) {
                piggyBankRewardInfo.addPiggyBankRewardList(buildPiggyBankReward(piggyReward));
            }
            piggyBankInfo.addPiggyBankRewardList(piggyBankRewardInfo.build());
        }

        return piggyBankInfo.build();
    }

    private ActivityMessage.PiggyBankReward buildPiggyBankReward(C_PiggyBank.PiggyReward piggyReward) {
        final ActivityMessage.PiggyBankReward.Builder piggyBankReward = ActivityMessage.PiggyBankReward.newBuilder();
        piggyBankReward.setId(piggyReward.getId())
                .setCurrencyId(piggyReward.getCurrencyId())
                .setRewardAmount(piggyReward.getBonus());
        return piggyBankReward.build();
    }

    private C_MysteryBonus.RewardGear findRewardGear(int day, double recharge, C_MysteryBonus c_mysteryBonus) {
        final C_MysteryBonus.MysteryRewardInfo mysteryRewardInfo = c_mysteryBonus.getMysteryRewardInfoMap().get(day);
        if (mysteryRewardInfo == null) {
            return null;
        }

        final List<C_MysteryBonus.RewardGear> rewardGearList = mysteryRewardInfo.getRewardGears();
        for (C_MysteryBonus.RewardGear rewardGear : rewardGearList) {
            if (rewardGear.getRechargeMin() <= recharge && recharge < rewardGear.getRechargeMax()) {
                return rewardGear;
            }
        }
        return null;
    }

    private ActivityMessage.ContinuousDepositInfo buildContinuousDepositInfo(Player player, int language, MerchantData merchantData) {
        final C_ContinuousDeposit c_continuousDeposit = merchantData.findC_ContinuousDeposit(this.getClass().getSimpleName(), ActivityMrg.CONTINUOUS_DEPOSIT);
        if (c_continuousDeposit == null) {
            return null;
        }

        final C_ContinuousDeposit.Rule rule = c_continuousDeposit.getRuleMap().get(language);
        if (rule == null) {
            return null;
        }

        final ActivityMessage.ContinuousDepositInfo.Builder continuousDepositInfo = ActivityMessage.ContinuousDepositInfo.newBuilder();
        for (C_ContinuousDeposit.ContinuousDeposit continuousDeposit : c_continuousDeposit.getContinuousDepositMap().values()) {
            continuousDepositInfo.addDepositList(buildRechargeInfo(continuousDeposit));
        }

        for (C_ContinuousDeposit.Button button : rule.getButtonMap().values()) {
            continuousDepositInfo.addButtonList(buildButtonInfo(button));
        }
        continuousDepositInfo.setActivityId(ActivityMrg.CONTINUOUS_DEPOSIT)
                .setCId(c_continuousDeposit.getC_id());
        return continuousDepositInfo.build();
    }

    private ActivityMessage.RechargeInfo buildRechargeInfo(C_ContinuousDeposit.ContinuousDeposit continuousDeposit) {
        final ActivityMessage.RechargeInfo.Builder rechargeInfo = ActivityMessage.RechargeInfo.newBuilder();
        rechargeInfo.setId(continuousDeposit.id)
                .setIcon(continuousDeposit.getIcon())
                .setGiveawayRate(continuousDeposit.giveawayRate);
        return rechargeInfo.build();
    }

    private ActivityMessage.ButtonInfo buildButtonInfo(C_ContinuousDeposit.Button button) {
        final ActivityMessage.ButtonInfo.Builder buttonInfo = ActivityMessage.ButtonInfo.newBuilder();
        buttonInfo.setName(button.getName())
                .setJumpType(button.getJumpType())
                .setPopupLinks(button.getPopupLinks())
                .setInnerLinks(button.getInnerLinks())
                .setExternalLinks(button.getExternalLinks());
        return buttonInfo.build();
    }

    private ActivityMessage.FirstChargeSignInInfo buildFirstChargeSignInInfo(Player player, int language, MerchantData merchantData) {
        final C_FirstChargeSignIn c_firstChargeSignIn = merchantData.findC_FirstChargeSignIn(this.getClass().getSimpleName(), ActivityMrg.FIRSTCHARGE_SIGNIN);
        if (c_firstChargeSignIn == null) {
            return null;
        }

        final C_FirstChargeSignIn.Rule rule = c_firstChargeSignIn.getRuleMap().get(language);
        if (rule == null) {
            return null;
        }

        final ActivityMessage.FirstChargeSignInInfo.Builder firstChargeSignInInfo = ActivityMessage.FirstChargeSignInInfo.newBuilder();
        firstChargeSignInInfo.setIcon(rule.getIcon())
                .setRule(rule.getRule())
                .setCId(c_firstChargeSignIn.getC_id())
                .setActivityId(ActivityMrg.FIRSTCHARGE_SIGNIN)
                .setResetDay(c_firstChargeSignIn.getResetDay());

        for (C_FirstChargeSignIn.FirstChargeReward firstChargeReward : c_firstChargeSignIn.getFirstChargeSignInMap().values()) {
            firstChargeSignInInfo.addFirstChargeRewards(buildFirstChargeReward(firstChargeReward));
        }

        if (player != null) {
            final FirstChargeSignInInfo firstChargeSignIn = player.getFirstChargeSignInInfo();
            final int currDay = TimeUtil.getNaturalDays(TimeUtil.currentTimeMillis(), firstChargeSignIn.getRechargeTime(), player.getTimeZone());
            firstChargeSignInInfo.setCurrDay(currDay)
                    .setRechargeTime(firstChargeSignIn.getRechargeTime())
                    .addAllReceiveDay(firstChargeSignIn.getReceive());
        }
        return firstChargeSignInInfo.build();
    }

    private ActivityMessage.FirstChargeReward buildFirstChargeReward(C_FirstChargeSignIn.FirstChargeReward firstChargeSignIn) {
        final ActivityMessage.FirstChargeReward.Builder firstChargeRewardInfo = ActivityMessage.FirstChargeReward.newBuilder();
        firstChargeRewardInfo.setId(firstChargeSignIn.id)
                .setCurrencyId(firstChargeSignIn.currencyId)
                .setRewardAmount(firstChargeSignIn.bonus);
        return firstChargeRewardInfo.build();
    }

    private ActivityMessage.RechargeRecoverInfo buildRechargeRecoverInfo(Player player, int language, MerchantData merchantData) {
        final C_RechargeRecover c_rechargeRecover = merchantData.findC_RechargeRecover(this.getClass().getSimpleName(), ActivityMrg.RECHARGE_RECOVER);
        if (c_rechargeRecover == null) {
            return null;
        }

        final C_RechargeRecover.Rule ruleWagered = c_rechargeRecover.getRuleWageredMap().get(language);
        final C_RechargeRecover.Rule ruleRedEnvelopeRain = c_rechargeRecover.getRuleRedEnvelopeRainMap().get(language);
        if (ruleWagered == null && ruleRedEnvelopeRain == null) {
            return null;
        }

        final ActivityMessage.RechargeRecoverInfo.Builder rechargeRecoverInfo = ActivityMessage.RechargeRecoverInfo.newBuilder();
        rechargeRecoverInfo.setCId(c_rechargeRecover.getC_id())
                .setActivityId(ActivityMrg.RECHARGE_RECOVER);

        if (player != null) {
            final long todayBegin = TimeUtil.getTimeBeginOfToday(TimeUtil.currentTimeMillis(), player.getTimeZone());
//            final long todayBegin = TimeUtil.currentTimeMillis();

            final int recentTime = Math.min(c_rechargeRecover.getRecentTime(), 15);
            final long start1 = TimeUtil.currentTimeMillis();
            final GameNote gameNote = EntityDaoMrg.getInstance().getDao(GameNoteDao.class)
                    .aggregateBet(pid, player.getCurrencyId(), GameNoteFields.validBets, todayBegin - recentTime * TimeUtil.DAY, todayBegin);
            if (TimeUtil.currentTimeMillis() - start1 > 10) {
                LOGGER.warn("gameNote query cost time：{}", TimeUtil.currentTimeMillis() - start1);
            }

            final long start = TimeUtil.currentTimeMillis();
            final RechargeOrder rechargeOrder = EntityDaoMrg.getInstance().getDao(RechargeOrderDao.class)
                    .aggregateRechargeAmount(pid, player.getCurrencyId(), RechargeOrderFields.amounts, todayBegin - recentTime * TimeUtil.DAY, todayBegin);
            if (TimeUtil.currentTimeMillis() - start > 10) {
                LOGGER.warn("rechargeOrder query cost time：{}", TimeUtil.currentTimeMillis() - start);
            }

            int wageredGear = 0;
            if (c_rechargeRecover.isWageredOpen()) {
                final C_RechargeRecover.RewardInfo wagered = c_rechargeRecover.findMaxReward(c_rechargeRecover.getWageredRewardMap(), rechargeOrder.getAmounts(), gameNote.getValidBets());
                if (ruleWagered != null && wagered != null) {
                    wageredGear = wagered.id;
                    rechargeRecoverInfo.addRewards(buildRechargeRecoverReward(1, ruleWagered.getName(), c_rechargeRecover.getCurrencyId(), wagered.min, wagered.max));

                    HallServer.getInstance().asyncExecute(pid, () -> {
                        final RechargeRecoverInfo info = player.getRechargeRecoverInfo();
                        info.getWageredMap().put(player.getCurrencyId(), BigDecimalUtils.round(MathUtils.random(wagered.min, wagered.max), 2));
                    });
                }
            }

            int redEnvelopeRainGear = 0;
            if (c_rechargeRecover.isRedEnvelopeRainOpen()) {
                final C_RechargeRecover.RewardInfo redEnvelopeRain = c_rechargeRecover.findMaxReward(c_rechargeRecover.getRedEnvelopeRainRewardMap(), rechargeOrder.getAmounts(), gameNote.getValidBets());
                if (ruleRedEnvelopeRain != null && redEnvelopeRain != null) {
                    redEnvelopeRainGear = redEnvelopeRain.id;
                    rechargeRecoverInfo.addRewards(buildRechargeRecoverReward(2, ruleRedEnvelopeRain.getName(), c_rechargeRecover.getCurrencyId(), redEnvelopeRain.min, redEnvelopeRain.max));

                    HallServer.getInstance().asyncExecute(pid, () -> {
                        final RechargeRecoverInfo info = player.getRechargeRecoverInfo();
                        info.getRedEnvelopeRainMap().put(player.getCurrencyId(), BigDecimalUtils.round(MathUtils.random(redEnvelopeRain.min, redEnvelopeRain.max), 2));
                    });
                }
            }

            final int gear = Math.max(wageredGear, redEnvelopeRainGear);
            if (gear > 0) {
                final C_RechargeRecover.RewardInfo rewardInfo = c_rechargeRecover.getRechargeRewardMap().get(gear);
                if (rewardInfo != null) {
                    rechargeRecoverInfo.setRechargeAmount(buildItemShow(rewardInfo.currencyId, rewardInfo.rechargeAmount));

                    HallServer.getInstance().asyncExecute(pid, () -> {
                        final RechargeRecoverInfo info = player.getRechargeRecoverInfo();
                        info.getRechargeAmountMap().put(rewardInfo.currencyId, rewardInfo.rechargeAmount);
                    });
                }
            }

            final RechargeRecoverInfo recoverInfo = player.getRechargeRecoverInfo();
            rechargeRecoverInfo.setReceive(recoverInfo.isStatus());

            final double currRechargeAmount = recoverInfo.getCurrRechargeMap().getOrDefault(player.getCurrencyId(), 0d);
            final double rechargeAmount = recoverInfo.getRechargeAmountMap().getOrDefault(player.getCurrencyId(), 0d);
            if (!recoverInfo.isStatus() && currRechargeAmount >= rechargeAmount) {
                rechargeRecoverInfo.setStatus(1);
            }
        }
        return rechargeRecoverInfo.build();
    }

    private ActivityMessage.RechargeRecoverReward buildRechargeRecoverReward(int receiveType, String name, int currencyId, double min, double max) {
        final ActivityMessage.RechargeRecoverReward.Builder rechargeRecoverReward = ActivityMessage.RechargeRecoverReward.newBuilder();
        rechargeRecoverReward.setReceiveType(receiveType)
                .setName(name)
                .setCurrencyId(currencyId)
                .setMinAmount(min)
                .setMaxAmount(max);
        return rechargeRecoverReward.build();
    }

    private ActivityMessage.ActivityRankInfo buildRankInfo(Player player, int ranking, int currencyId, double wagered) {
        return ActivityMessage.ActivityRankInfo.newBuilder()
                .setRanking(ranking)
                .setHeadId(player.getHeadId())
                .setName(player.getPlayerName())
                .setCurrencyId(currencyId)
                .setWagered(wagered)
                .build();
    }

    private ActivityMessage.WageredRebatesInfo buildWageredRebatesInfo(Player player, int language, MerchantData merchantData) {
        final C_WageredRebates c_wageredRebates = merchantData.findC_WageredRebates(this.getClass().getSimpleName(), ActivityMrg.WAGERED_REBATES);
        if (c_wageredRebates == null) {
            return null;
        }

        final C_WageredRebates.Rule rule = c_wageredRebates.getRuleMap().get(language);
        if (rule == null) {
            return null;
        }

        final String business_no = merchantData.getBusiness_no();
        final ActivityMessage.WageredRebatesInfo.Builder wageredRebatesInfo = ActivityMessage.WageredRebatesInfo.newBuilder();
        if (player != null) {
            //清空奖励
            final WageredRebatesInfo wageredRebates = player.getWageredRebatesInfo();
            if (wageredRebates.getLastEndTime() > 0 && TimeUtil.currentTimeMillis() >= wageredRebates.getLastEndTime() + c_wageredRebates.getAwardClearTime()) {
                wageredRebates.getBonusMap().clear();
                wageredRebates.setStatus(false);
            }

            {
                final double bonus = wageredRebates.getBonusMap().getOrDefault(player.getCurrencyId(), 0d);
                wageredRebatesInfo.setClaim(CommonMrg.buildDItemShow(player.getCurrencyId(), bonus));
            }

            Long ranking = RedisPoolManager.getInstance().function(jedis ->
                    jedis.sync().zrevrank(RedisRanking.RANKING_WAGEREDREBATES.getKey(business_no, TimeUtil.getDateTimeFormat(TimeUtil.YYYYMMDD, player.getTimeZone())), player.getCurrencyId() + "_" + player.getPlayerId()));
            if (ranking == null) {
                ranking = 0L;
            } else {
                ranking++;
            }
            final double wagered = wageredRebates.getWageredMap().getOrDefault(player.getCurrencyId(), 0d);
            final double loss = wageredRebates.getLossMap().getOrDefault(player.getCurrencyId(), 0d);

            final double customerLoss = BigDecimalUtils.sub(wagered, loss, 4);
            if (customerLoss > 0) {//输分
                double refund = BigDecimalUtils.mul(customerLoss, c_wageredRebates.getLosePlayer(), 9);
                if (refund > c_wageredRebates.getRewardLimit()) {
                    refund = c_wageredRebates.getRewardLimit();
                }
                wageredRebatesInfo.setRefund(CommonMrg.buildDItemShow(player.getCurrencyId(), refund));
            } else {
                double bonus = BigDecimalUtils.mul(Math.abs(customerLoss), c_wageredRebates.getWinPlayer(), 9);
                if (bonus > c_wageredRebates.getRewardLimit()) {
                    bonus = c_wageredRebates.getRewardLimit();
                }
                wageredRebatesInfo.setBonus(CommonMrg.buildDItemShow(player.getCurrencyId(), bonus));
            }
            wageredRebatesInfo.setStatus(wageredRebates.isStatus())
                    .setAwardClearTime(wageredRebates.getLastEndTime() + c_wageredRebates.getAwardClearTime())
                    .setWin(CommonMrg.buildDItemShow(player.getCurrencyId(), customerLoss > 0 ? -customerLoss : Math.abs(customerLoss)))
                    .setMyRank(buildRankInfo(player, Math.toIntExact(ranking), player.getCurrencyId(), wagered));
        }

        int topNum = 0;
        if (c_wageredRebates.getRankType() == 1) {// 比例
            final long totalNum = RedisPoolManager.getInstance().function(jedis ->
                    jedis.sync().zcard(RedisRanking.RANKING_WAGEREDREBATES.getKey(business_no, TimeUtil.getDateTimeFormat(TimeUtil.YYYYMMDD, c_wageredRebates.getTimeZone()))));
            topNum = (int) BigDecimalUtils.mul((double) totalNum, c_wageredRebates.getRankNum(), 9);
            topNum = Math.max(10, topNum);
        } else {//
            topNum = (int) c_wageredRebates.getRankNum();
        }

        wageredRebatesInfo.setCId(c_wageredRebates.getC_id())
                .setActivityId(c_wageredRebates.getActivityId())
                .setTimeZone(c_wageredRebates.getTimeZone())
                .setIcon(rule.getIcon())
                .setRule(rule.getRule())
                .setTopNum(topNum)
                .setEndTime(TimeUtil.getTimeEndOfToday(TimeUtil.currentTimeMillis(), c_wageredRebates.getTimeZone()))
                .setActiveTime(TimeUtil.currentTimeMillis());

        final int finalTopNum = topNum;

        final List<ScoredValue<String>> players = RedisPoolManager.getInstance().function(jedis ->
                jedis.sync().zrevrangeWithScores(RedisRanking.RANKING_WAGEREDREBATES.getKey(business_no, TimeUtil.getDateTimeFormat(TimeUtil.YYYYMMDD, c_wageredRebates.getTimeZone())), 0, finalTopNum - 1));
        int ranking = 0;
        if (players != null) {
            for (ScoredValue<String> tuple : players) {
                ++ranking;
                final double score = tuple.getScore();

                final String element = tuple.getValue();
                final int currencyId = Integer.parseInt(element.split("_")[0]);
                final long playerId = Long.parseLong(element.split("_")[1]);

                Player rankPlayer = null;
                if (String.valueOf(playerId).length() < 8) {
                    rankPlayer = new Player(playerId);
                    rankPlayer.setHeadId(getFixedHeadId(playerId) + "");
                    rankPlayer.setPlayerName("WG" + playerId);
                } else {
                    rankPlayer = PlayerMrg.getInstance().findDbPlayer(playerId);
                }
                if (rankPlayer == null) {
                    continue;
                }

                wageredRebatesInfo.addRankInfo(buildRankInfo(rankPlayer, ranking, currencyId, score));
            }
        }

        final List<ScoredValue<String>> historyPlayers = RedisPoolManager.getInstance().function(jedis ->
                jedis.sync().zrevrangeWithScores(RedisRanking.RANKING_WAGEREDREBATES.getKey(business_no, TimeUtil.getDateTimeFormat(TimeUtil.currentTimeMillis() - TimeUtil.DAY, TimeUtil.YYYYMMDD, c_wageredRebates.getTimeZone())), 0, finalTopNum - 1));
        int historyRanking = 0;
        if (historyPlayers != null) {
            for (ScoredValue<String> tuple : historyPlayers) {
                ++historyRanking;
                final double score = tuple.getScore();

                final String element = tuple.getValue();
                final int currencyId = Integer.parseInt(element.split("_")[0]);
                final long playerId = Long.parseLong(element.split("_")[1]);

                Player rankPlayer = null;
                if (String.valueOf(playerId).length() < 8) {
                    rankPlayer = new Player(playerId);
                    rankPlayer.setHeadId(getFixedHeadId(playerId) + "");
                    rankPlayer.setPlayerName("WG" + playerId);
                } else {
                    rankPlayer = PlayerMrg.getInstance().findDbPlayer(playerId);
                }
                if (rankPlayer == null) {
                    continue;
                }
                wageredRebatesInfo.addHistory(buildRankInfo(rankPlayer, historyRanking, currencyId, score));
            }
        }
        return wageredRebatesInfo.build();
    }

    /**
     * int32 currencyId                                         = 1;
     * double lockedBonus                                       = 2; //解锁奖励
     * double claimedBonus                                      = 3; //领取奖励
     * double unClaimedBonus                                    = 4; //未领取奖励
     * double validWagered                                      = 5; //有效投注
     * string icon                                              = 6; //icon
     * string rule                                              = 7; //规则
     * int32 turnoverMul                                        = 8; //打码量
     * string referralLink                                      = 9; //推荐连接
     * string referralCode                                      =10; //邀请码
     * repeated RewardData rewardData                           =11; //奖励数据
     * repeated FriendsValidWageredData friendsValidWageredList =12; //有效朋友下注
     *
     * @param player
     * @param language
     * @param merchantData
     * @return
     */
    private ActivityMessage.DepositInviteBonusInfo buildDepositInviteBonusInfo(Player player, int language, MerchantData merchantData) {
        final ActivityMessage.DepositInviteBonusInfo.Builder depositInviteBonusInfo = ActivityMessage.DepositInviteBonusInfo.newBuilder();
        final C_FirstDepositInviteBonus c_firstDepositInviteBonus = merchantData.findC_FirstDepositInviteBonus(this.getClass().getSimpleName(), ActivityMrg.FIRSTDEPOSIT_INIVTEBONUS);
        if (c_firstDepositInviteBonus == null) {
            return null;
        }

        final C_FirstDepositInviteBonus.Rule rule = c_firstDepositInviteBonus.getRuleMap().get(language);
        if (rule == null) {
            return null;
        }

        if (player == null) {
            return null;
        }

        final FirstDepositInviteBonusInfo firstDepositInviteBonusInfo = player.getFirstDepositInviteBonusInfo();

        final long start = TimeUtil.currentTimeMillis();
        final double totalValidBet = totalValidBet(player, c_firstDepositInviteBonus);
//        LOGGER.warn("playerId：{}，totalValidBet：{}", player.getPlayerId(), totalValidBet);
        if (TimeUtil.currentTimeMillis() - start > 10) {
            LOGGER.warn("depositInviteBonusInfo query cost time：{}", TimeUtil.currentTimeMillis() - start);
        }

        final double charge = firstDepositInviteBonusInfo.getChargeMap()
                .getOrDefault(firstDepositInviteBonusInfo.getCurrencyId(), 0d);
        double process = 0;
        if (charge > 0) {
            process = BigDecimalUtils.div(totalValidBet, charge * c_firstDepositInviteBonus.getTurnoverMul(), 4);
        }

        final int vipLevel = firstDepositInviteBonusInfo.getVipLevel();
        final List<C_FirstDepositInviteBonus.RewardInfo> rewardInfos = c_firstDepositInviteBonus.findRewardsList(vipLevel, firstDepositInviteBonusInfo.getReceiveBonus(), process);
        final C_FirstDepositInviteBonus.VipRewardInfo vipRewardInfo = c_firstDepositInviteBonus.findVipRewardsList(vipLevel);
        //已领取
        double totalClaimedBonus = 0;
        {
            if (vipRewardInfo != null) {
                for (int receiveId : firstDepositInviteBonusInfo.getReceiveBonus()) {
                    for (C_FirstDepositInviteBonus.RewardInfo rewardInfo : vipRewardInfo.getRewardInfos()) {
                        if (rewardInfo.id != receiveId) {
                            continue;
                        }
                        final double value = BigDecimalUtils.mul(charge, rewardInfo.getUnlockRate(), 4);
                        totalClaimedBonus = BigDecimalUtils.add(totalClaimedBonus, value, 4);
                    }
                }
            }
        }


        double totalBonus = 0;
        {
            if (vipRewardInfo != null) {
                for (C_FirstDepositInviteBonus.RewardInfo rewardInfo : vipRewardInfo.getRewardInfos()) {
                    final double value = BigDecimalUtils.mul(charge, rewardInfo.getUnlockRate(), 4);
                    totalBonus = BigDecimalUtils.add(totalBonus, value, 4);
                }
            }
        }

        //未领取
        double totalUnlockClaimedBonus = 0;
        {
            for (C_FirstDepositInviteBonus.RewardInfo rewardInfo : rewardInfos) {
                final double value = BigDecimalUtils.mul(charge, rewardInfo.getUnlockRate(), 4);
                totalUnlockClaimedBonus = BigDecimalUtils.add(totalUnlockClaimedBonus, value, 4);
            }
            //发送邮件
            final C_BaseCurrency c_baseCurrency = DataHallMrg.getInstance().findC_BaseCurrency(this.getClass().getSimpleName(), player.getCurrencyId());
            for (C_FirstDepositInviteBonus.RewardInfo rewardInfo : rewardInfos) {
                if (firstDepositInviteBonusInfo.getReceiveMails().contains(rewardInfo.id)) {
                    continue;
                }
                final List<String> params = new ArrayList<>();
                if (c_baseCurrency != null) {
                    params.add(c_baseCurrency.getSymbol());
                }
                final double value = BigDecimalUtils.mul(charge, rewardInfo.getUnlockRate(), 4);
                params.add(value + "");
                ScriptLoader.getInstance().consumerScript("InboxScript",
                        (IInboxScript script) -> script.sendInboxMail(player, InboxMrg.FIRST_DEPOSIT_INVITE_BONUS, params));
                firstDepositInviteBonusInfo.getReceiveMails().add(rewardInfo.id);
            }
        }

        HallServer.getInstance().asyncExecute(pid, () -> {
            firstDepositInviteBonusInfo.setTotalValidWagered(totalValidBet);
        });

        long startTime = 0;
        long endTime = 0;
        if (c_firstDepositInviteBonus.getCycleType() == 1) {
            startTime = c_firstDepositInviteBonus.getLimitTimeStart();
            endTime = c_firstDepositInviteBonus.getLimitTimeEnd();
        }
        if (c_firstDepositInviteBonus.getCycleType() == 2) {// 每周
            startTime = TimeUtil.getTimeBeginOfWeek(TimeUtil.currentTimeMillis(), player.getTimeZone());
            endTime = TimeUtil.getTimeEndOfWeek(TimeUtil.currentTimeMillis(), player.getTimeZone());
        }
        if (c_firstDepositInviteBonus.getCycleType() == 3) {// 每月
            startTime = TimeUtil.getTimeBeginOfMonth(TimeUtil.currentTimeMillis(), player.getTimeZone());
            endTime = TimeUtil.getTimeEndOfMonth(TimeUtil.currentTimeMillis(), player.getTimeZone());
        }

        depositInviteBonusInfo.setCId(c_firstDepositInviteBonus.getC_id())
                .setActivityId(c_firstDepositInviteBonus.getActivityId())
                .setCurrencyId(player.getCurrencyId())
                .setLockedBonus(BigDecimalUtils.sub(totalBonus - totalUnlockClaimedBonus, totalClaimedBonus, 4))
                .setClaimedBonus(totalClaimedBonus)
                .setUnClaimedBonus(totalUnlockClaimedBonus)
                .setValidWagered(totalValidBet)
                .setChargeAmount(charge)
                .setIcon(rule.getIcon())
                .setRule(rule.getRule())
                .addAllReceiveBonus(firstDepositInviteBonusInfo.getReceiveBonus())
                .setTurnoverMul(c_firstDepositInviteBonus.getTurnoverMul())
                .setTimeZone(c_firstDepositInviteBonus.getTimeZone())
                .setLimitTimeStart(startTime)
                .setLimitTimeEnd(endTime)
                .setNeedChargeAmount(c_firstDepositInviteBonus.getNeedChargeAmount());

        final String referralLink = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "referralLink");
        final String url = referralLink + "?" + "referralCode=" + player.getInvitationCode() + "-~." + "activity=" + c_firstDepositInviteBonus.getActivityId() + "_" + c_firstDepositInviteBonus.getC_id();
        depositInviteBonusInfo.setReferralLink(url)
                .setReferralCode(player.getInvitationCode());

        if (vipRewardInfo != null) {
            for (C_FirstDepositInviteBonus.RewardInfo rewardInfo : vipRewardInfo.getRewardInfos()) {
                depositInviteBonusInfo.addRewardData(buildRewardData(rewardInfo));
            }
        }
        return depositInviteBonusInfo.build();
    }

    private ActivityMessage.RewardData buildRewardData(C_FirstDepositInviteBonus.RewardInfo reward) {
        final ActivityMessage.RewardData.Builder rewardData = ActivityMessage.RewardData.newBuilder();
        rewardData.setId(reward.getId())
                .setRewardRate(reward.getRewardRate())
                .setUnlockRate(reward.getUnlockRate())
                .setTotalUnlockRate(reward.getTotalUnlockRate());
        return rewardData.build();
    }

    private List<Long> findSubordinates(Player player) {
        final int limit = 1000;
        int totalCount = 0;
        final List<Long> subordinates = new ArrayList<>();
        while (true) {
            final List<PlayerPromote> friendsList = EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class).loadAllBySuperiorId(player.getPlayerId(), totalCount, limit);
            if (friendsList == null || friendsList.isEmpty()) break;

            for (var friend : friendsList) {
                if (friend == null) continue;
                subordinates.add(friend.getPlayerId());
            }

            totalCount += friendsList.size();
            if (friendsList.size() < limit) break;
        }
        return subordinates;
    }

    private double totalValidBet(Player player, C_FirstDepositInviteBonus c_firstDepositInviteBonus) {
        final FirstDepositInviteBonusInfo firstDepositInviteBonusInfo = player.getFirstDepositInviteBonusInfo();

        long startTime = firstDepositInviteBonusInfo.getFirstDepositTime();
        long endTime = TimeUtil.currentTimeMillis();
        if (c_firstDepositInviteBonus.getCycleType() == 2) {// 每周
            startTime = TimeUtil.getTimeBeginOfWeek(TimeUtil.currentTimeMillis(), player.getTimeZone());
            endTime = TimeUtil.getTimeEndOfWeek(TimeUtil.currentTimeMillis(), player.getTimeZone());
        }
        if (c_firstDepositInviteBonus.getCycleType() == 3) {// 每月
            startTime = TimeUtil.getTimeBeginOfMonth(TimeUtil.currentTimeMillis(), player.getTimeZone());
            endTime = TimeUtil.getTimeEndOfMonth(TimeUtil.currentTimeMillis(), player.getTimeZone());
        }

        if (startTime == 0) {
            return 0;
        }

        final List<Long> allFriends = findSubordinates(player);

        /**
         * Casino_Original(101, "原创"),
         *     Casino_Slots(102, "电子"),
         *     Casino_Live(103, "赌场视讯"),
         *     Casino_Poker(104, "棋牌牌桌"),
         *     Casino_Fish(105, "捕鱼"),
         *     Casino_Arcade(106, "街机"),
         *     Casino_Bingo(107, "宾果"),
         */
        final GameWagerStat gameWagerStat = EntityDaoMrg.getInstance().getDao(GameNoteDao.class)
                .aggregateGameWagerStat(allFriends, player.getCurrencyId(), GameNoteFields.validBets, startTime, endTime);

        //TODO 计算有效
        final double originalValidBet = ScriptLoader.getInstance().functionScript("GameScript",
                (IGameScript script) -> script.calculateValidBettingVolume(player, GameType.Casino_Original.getType(), player.getCurrencyId(), gameWagerStat.getTotalWagerOriginal()));

        final double slotsValidBet = ScriptLoader.getInstance().functionScript("GameScript",
                (IGameScript script) -> script.calculateValidBettingVolume(player, GameType.Casino_Slots.getType(), player.getCurrencyId(), gameWagerStat.getTotalWagerSlots()));

        final double liveValidBet = ScriptLoader.getInstance().functionScript("GameScript",
                (IGameScript script) -> script.calculateValidBettingVolume(player, GameType.Casino_Live.getType(), player.getCurrencyId(), gameWagerStat.getTotalWagerLive()));

        final double pokerValidBet = ScriptLoader.getInstance().functionScript("GameScript",
                (IGameScript script) -> script.calculateValidBettingVolume(player, GameType.Casino_Poker.getType(), player.getCurrencyId(), gameWagerStat.getTotalWagerPoker()));

        final double fishValidBet = ScriptLoader.getInstance().functionScript("GameScript",
                (IGameScript script) -> script.calculateValidBettingVolume(player, GameType.Casino_Fish.getType(), player.getCurrencyId(), gameWagerStat.getTotalWagerFish()));

        final double arcadeValidBet = ScriptLoader.getInstance().functionScript("GameScript",
                (IGameScript script) -> script.calculateValidBettingVolume(player, GameType.Casino_Arcade.getType(), player.getCurrencyId(), gameWagerStat.getTotalWagerArcade()));

        final double bingoValidBet = ScriptLoader.getInstance().functionScript("GameScript",
                (IGameScript script) -> script.calculateValidBettingVolume(player, GameType.Casino_Bingo.getType(), player.getCurrencyId(), gameWagerStat.getTotalWagerBingo()));

        return BigDecimalUtils.add(originalValidBet + slotsValidBet + liveValidBet, pokerValidBet + fishValidBet + arcadeValidBet + bingoValidBet, 9);
    }

    private static int getFixedHeadId(long playerId) {
        final int min = 1000;
        int max = 1015;
        // 使用玩家ID作为种子
        final Random random = new Random(playerId);
        return random.nextInt(max - min + 1) + min;
    }

}

