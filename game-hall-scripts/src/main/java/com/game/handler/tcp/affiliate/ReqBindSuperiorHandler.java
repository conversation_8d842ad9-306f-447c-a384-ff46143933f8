package com.game.handler.tcp.affiliate;

import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.log.GameLog;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.GuidGeneratorUtils;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.entity.player.promote.PlayerPromote;
import com.game.enums.ErrorCode;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.IPromoteScript;
import com.game.manager.EntityDaoMrg;
import com.proto.AffiliateMessage;
import com.proto.MIDMessage;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Update;

@IHandlerEntity(mid = MIDMessage.MID.ReqBindSuperior_VALUE, msg = AffiliateMessage.ReqBindSuperiorMessage.class)
public class ReqBindSuperiorHandler extends TcpHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(ReqBindSuperiorHandler.class);

    @Override
    public void run() {
        final AffiliateMessage.ResBindSuperiorMessage.Builder res = AffiliateMessage.ResBindSuperiorMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResBindSuperior_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final AffiliateMessage.ReqBindSuperiorMessage req = (AffiliateMessage.ReqBindSuperiorMessage) getMessage();
            final String referralCode = req.getReferrerCode();

            final PlayerPromote playerPromote = player.getPlayerPromote();
            if (player.getAgentId() != 0 || player.getChannelId() != 0 || playerPromote.getSuperiorId() != 0) {
                res.setError(ErrorCode.Already_Bound_To_The_Superior.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final String host = player.getHost();
            ErrorCode errorCode = ErrorCode.Success;
            if (StringUtils.isNumeric(referralCode)) {
                if (referralCode.length() != 8) {
                    res.setError(ErrorCode.ReferralCode_Error.getCode());
                    MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                    return;
                }

                final Update update = new Update();
                if (Integer.parseInt(referralCode) / 10000000 == 3) {//代理
                    player.setAgentId(Integer.parseInt(referralCode));
                    update.set(PlayerFields.agentId, player.getAgentId());
                } else if (Integer.parseInt(referralCode) / 10000000 == 5) {//币商
                    player.setCurrencyTraderId(Integer.parseInt(referralCode));
                    update.set(PlayerFields.currencyTraderId, player.getCurrencyTraderId());
                }

                if (!update.getUpdateObject().isEmpty()) {
                    player.setHost("https://" + host + "?" + "al=" + referralCode);
                    update.set(PlayerFields.host, player.getHost());
                    EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                            .updatePlayer(player.getPlayerId(), update);
                }

                //绑定代理或者channelId日志
                final GameLog playerPromotionLog = new GameLog("platform_playerPromotionLog");
                playerPromotionLog.append("number", GuidGeneratorUtils.generateOrderId())
                        .append("business_no", player.getBusiness_no())
                        .append("site", player.getWebSite())
                        .append("playerId", player.getPlayerId())
                        .append("playerName", player.getPlayerName())
                        .append("registerTime", player.getCreateTime())
                        .append("agentId", player.getAgentId())
                        .append("channelId", player.getChannelId())
                        .append("mediaId", player.getMediaId())
                        .append("adId", player.getAdId())
                        .append("region", player.getRegisterRegion())
                        .append("registerIp", player.getRegisterIp())
                        .append("logTime", TimeUtil.currentTimeMillis());
                HallServer.getInstance().getLogProducerMrg().send(playerPromotionLog);
            } else {
                errorCode = ScriptLoader.getInstance().functionScript("PromoteScript",
                        (IPromoteScript script) -> script.bindSuperiorId(player, "", referralCode, true));
                if (errorCode == ErrorCode.Success) {
                    player.setHost("https://" + host + "?" + "referralCode=" + referralCode);
                    final Update update = new Update();
                    update.set(PlayerFields.host, player.getHost());
                    EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                            .updatePlayer(player.getPlayerId(), update);
                }
            }

            res.setError(errorCode.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqBindSuperiorHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }
}
