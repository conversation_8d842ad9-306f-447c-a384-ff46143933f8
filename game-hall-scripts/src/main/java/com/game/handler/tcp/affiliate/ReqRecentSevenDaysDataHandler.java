package com.game.handler.tcp.affiliate;

import com.game.dao.promote.CommissionRewardsNoteDao;
import com.game.dao.promote.TeamRewardsNoteDao;
import com.game.dao.promote.ThreeLevelRewardsNoteDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.entity.player.promote.CommissionRewardsNoteFields;
import com.game.entity.player.promote.TeamRewardsNoteFields;
import com.game.entity.player.promote.ThreeLevelRewardsNoteFields;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import com.proto.AffiliateMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.LinkedHashMap;
import java.util.Map;

@IHandlerEntity(mid = MIDMessage.MID.ReqRecentSevenDaysData_VALUE, msg = AffiliateMessage.ReqRecentSevenDaysDataMessage.class)
public class ReqRecentSevenDaysDataHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqRecentSevenDaysDataHandler.class);

    @Override
    public void run() {
        final AffiliateMessage.ResRecentSevenDaysDataMessage.Builder res = AffiliateMessage.ResRecentSevenDaysDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResRecentSevenDaysData_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }
            final AffiliateMessage.ReqRecentSevenDaysDataMessage req = (AffiliateMessage.ReqRecentSevenDaysDataMessage) getMessage();
            final int currencyId = req.getCurrencyId();
            final String date = req.getDate();

//            LOGGER.warn("playerId：{}，date：{}", player.getPlayerId(), date);

            final Map<Long, Double> recentRebateMap = getRecentRebateList(player, date, currencyId);
            for (Map.Entry<Long, Double> entry : recentRebateMap.entrySet()) {
                res.addSevenRebateList(buildSevenRebateInfo(entry.getKey(), entry.getValue()));
            }

            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqRecentSevenDaysDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    private Map<Long, Double> getRecentRebateList(Player player, String date, int currencyId) {
        final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-M-d");
        final LocalDate localDate = LocalDate.parse(date, formatter);

        final long timestamp = localDate.atStartOfDay(ZoneId.of(player.getTimeZone()))
                .toInstant()
                .toEpochMilli();

        final Map<Long, Double> rebateMap = new LinkedHashMap<>();
        final long startTime = TimeUtil.getTimeBeginOfToday(timestamp, player.getTimeZone());
        final long endTime = TimeUtil.getTimeEndOfToday(timestamp, player.getTimeZone());
        for (int i = 0; i <= 6; i++) {
            final long start = startTime - i * TimeUtil.DAY;
            final long end = endTime - i * TimeUtil.DAY;

            final double totalCommissionRewards = EntityDaoMrg.getInstance().getDao(CommissionRewardsNoteDao.class)
                    .aggregateRewards(player.getPlayerId(), currencyId, CommissionRewardsNoteFields.amount, start, end);
            final double totalTeamRewards = EntityDaoMrg.getInstance().getDao(TeamRewardsNoteDao.class)
                    .aggregateRewards(player.getPlayerId(), currencyId, TeamRewardsNoteFields.amount, start, end);
            final double totalThreeLevelRewards = EntityDaoMrg.getInstance().getDao(ThreeLevelRewardsNoteDao.class)
                    .aggregateRewards(player.getPlayerId(), currencyId, ThreeLevelRewardsNoteFields.amount, start, end);
            final double totalRebate = BigDecimalUtils.add(totalCommissionRewards + totalTeamRewards, totalThreeLevelRewards, 9);
            rebateMap.put(start, totalRebate);
        }
        return rebateMap;
    }

    private AffiliateMessage.SevenRebateInfo buildSevenRebateInfo(long date, double amount) {
        return AffiliateMessage.SevenRebateInfo.newBuilder()
                .setDate(date)
                .setAmount(amount)
                .build();
    }
}
