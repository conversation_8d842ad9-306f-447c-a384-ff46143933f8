package com.game.handler.tcp.activity;

import com.game.c_entity.merchant.C_TgReward;
import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.util.misc.Tuple2;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.entity.player.PlayerFields;
import com.game.enums.ErrorCode;
import com.game.enums.RewardReason;
import com.game.enums.TransactionFrom;
import com.game.enums.TurnoverReason;
import com.game.hall.mrg.CommonMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.mrg.currency.RewardRequest;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.IBonusScript;
import com.game.hall.script.IPlayerScript;
import com.game.manager.EntityDaoMrg;
import com.proto.ActivityMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Update;

@IHandlerEntity(mid = MIDMessage.MID.ReqReceiveTgShareReward_VALUE, msg = ActivityMessage.ReqReceiveTgShareRewardMessage.class)
public class ReqReceiveTgShareRewardHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqReceiveTgShareRewardHandler.class);

    @Override
    public void run() {
        final ActivityMessage.ResReceiveTgShareRewardMessage.Builder res = ActivityMessage.ResReceiveTgShareRewardMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResReceiveTgShareReward_VALUE);
        try {
            final ActivityMessage.ReqReceiveTgShareRewardMessage req = (ActivityMessage.ReqReceiveTgShareRewardMessage) getMessage();
            final int activityId = req.getActivityId();

            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final C_TgReward c_tgReward = merchantData.findC_TgReward(this.getClass().getSimpleName(), activityId);
            if (c_tgReward == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final String tgShareTimesStr = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "tgShareTimes");
            final int tgShareTimes = StringUtil.isNullOrEmpty(tgShareTimesStr) ? 0 : Integer.parseInt(tgShareTimesStr);

            if (player.getTgShareTimes() >= tgShareTimes) {
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final RewardRequest rewardRequest = new RewardRequest();
            rewardRequest.addCurrency(c_tgReward.getCurrencyId(), c_tgReward.getAmount());
            final RewardReason rewardReason = RewardReason.Tg_Share;
            rewardReason.setSource(activityId + "");
            if (c_tgReward.getRewardType() == 1) {
                CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getCurrencyMap(), rewardReason);
            } else {
                CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getBonusCurrencyMap(), rewardReason);
            }

            //TODO 打码
            final TurnoverReason turnoverReason = TurnoverReason.Tg_Share;
            turnoverReason.setSource(activityId + "");
            ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                    (IPlayerScript script) -> script.drawStandard(player, turnoverReason, c_tgReward.getCurrencyId(), c_tgReward.getAmount(),
                            BigDecimalUtils.mul(c_tgReward.getAmount(), c_tgReward.getTurnoverMul(), 4)));

            if (!rewardRequest.getCurrencyMap().isEmpty()) {
                final Tuple2<Integer, Double> tuple2 = rewardRequest.getReward();
                ScriptLoader.getInstance().consumerScript("BonusScript",
                        (IBonusScript script) -> script.addBonusNote(TransactionFrom.Tg_Share, player, tuple2.getFirst(), tuple2.getSecond()));
            }

            player.setTgShareTimes(player.getTgShareTimes() + 1);
            final Update update = new Update();
            update.set(PlayerFields.tgShareTimes, player.getTgShareTimes());
            EntityDaoMrg.getInstance().getDao(PlayerDao.class).updatePlayer(pid, update);

            res.setRewardShow(CommonMrg.buildDItemShow(c_tgReward.getCurrencyId(), c_tgReward.getAmount()));
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqReceiveTgShareRewardHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }
}
