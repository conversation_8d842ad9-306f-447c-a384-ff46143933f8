package com.game.handler.tcp.billing;

import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.io.redis.RedisPoolManager;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.UniqueIDGenerator;
import com.game.entity.player.PayInfo;
import com.game.entity.player.Player;
import com.game.entity.player.WithdrawAccount;
import com.game.enums.Currency;
import com.game.enums.ErrorCode;
import com.game.enums.PaymentMethod;
import com.game.enums.redis.RedisAllGame;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import com.proto.BillingMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import it.unimi.dsi.fastutil.longs.LongLists;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

@IHandlerEntity(mid = MIDMessage.MID.ReqAddDeleteWithdrawAccount_VALUE, msg = BillingMessage.ReqAddDeleteWithdrawAccountMessage.class)
public class ReqAddWithdrawAccountHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqAddWithdrawAccountHandler.class);

    @Override
    public void run() {
        final BillingMessage.ResAddDeleteWithdrawAccountMessage.Builder res = BillingMessage.ResAddDeleteWithdrawAccountMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResAddDeleteWithdrawAccount_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final BillingMessage.ReqAddDeleteWithdrawAccountMessage req = (BillingMessage.ReqAddDeleteWithdrawAccountMessage) getMessage();
            final long withdrawAccountId = StringUtil.isNullOrEmpty(req.getWithdrawAccountId()) ? 0 : Long.parseLong(req.getWithdrawAccountId());
            final int currencyId = req.getCurrencyId();
            final int paymentMethod = req.getPaymentMethod();
            final int channel = req.getChannel();
            res.setType(req.getType());

            String currencyIdPayment;
            if (currencyId / 1000 == 2) {
                currencyIdPayment = currencyId + "_" + PaymentMethod.Encrypt.getType();
            } else {
                currencyIdPayment = currencyId + "_" + paymentMethod + "_" + channel;
            }

            WithdrawAccount withdrawAccount = player.getWithdrawAccountMap().get(currencyIdPayment);
            switch (req.getType()) {
                case 1://添加
                    if (withdrawAccount == null) {
                        withdrawAccount = new WithdrawAccount();
                        player.getWithdrawAccountMap().put(currencyIdPayment, withdrawAccount);
                    }
                    final Map<Long, PayInfo> payInfoMap = withdrawAccount.getPayInfoMap();

                    if (payInfoMap.size() >= 20) {
                        res.setError(ErrorCode.Add_WithdrawAccount_UpperLimit.getCode());
                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                        return;
                    }

                    if (currencyId == Currency.BRL.getCurrencyId() && !payInfoMap.isEmpty()) {
                        res.setError(ErrorCode.Add_WithdrawAccount_UpperLimit.getCode());
                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                        return;
                    }

                    final BillingMessage.WithdrawAccount withdrawAccountMsg = req.getWithdrawAccount();
                    final String extend = withdrawAccountMsg.getExtend0();
                    final String extend1 = withdrawAccountMsg.getExtend1();
                    final String extend2 = withdrawAccountMsg.getExtend2();
                    final String extend3 = withdrawAccountMsg.getExtend3();

                    String withdrawAccount_ = extend;
                    if (!NumberUtils.isCreatable(withdrawAccount_)) {
                        withdrawAccount_ = extend1;
                    }
                    if (StringUtil.isNullOrEmpty(withdrawAccount_)) {
                        res.setError(ErrorCode.Not_WithdrawAccount.getCode());
                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                        return;
                    }

                    final String finalWithdrawAccount_ = withdrawAccount_;
                    switch (currencyId) {
                        case 1000://巴西
                        case 1001://印度
                        {
                            final boolean isExist = RedisPoolManager.getInstance().function(jedis ->
                                    jedis.sync().sismember(RedisAllGame.Platform_All_WithdrawAccount.getKey(player.getBusiness_no(), currencyIdPayment), finalWithdrawAccount_));
                            if (isExist) {
                                res.setError(ErrorCode.Withdraw_Account_Exist.getCode());
                                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                                return;
                            }
                        }
                        break;
                        case 1003://墨西哥
                        {
                            final boolean isExist = RedisPoolManager.getInstance().function(jedis ->
                                    jedis.sync().sismember(RedisAllGame.Platform_All_WithdrawAccount.getKey(player.getBusiness_no(), currencyIdPayment), extend2));
                            if (isExist) {
                                res.setError(ErrorCode.Withdraw_Account_Exist.getCode());
                                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                                return;
                            }
                        }
                        break;
                    }

                    final UniqueIDGenerator uniqueIDGenerator = HallServer.getInstance().getUniqueIDGenerator();
                    final PayInfo payInfo = new PayInfo();
                    payInfo.setPayId(uniqueIDGenerator.nextId());
                    payInfo.setCurrencyId(currencyId);
                    payInfo.setPaymentMethod(paymentMethod);
                    if (currencyId / 1000 == 2) {
                        payInfo.setPaymentMethod(PaymentMethod.Encrypt.getType());
                    }
                    payInfo.setExtend(extend);
                    payInfo.setExtend_1(extend1);
                    payInfo.setExtend_2(extend2);
                    payInfo.setExtend_3(extend3);
                    payInfoMap.put(payInfo.getPayId(), payInfo);
                    EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                            .updateInsertWithdrawAccount(player, currencyIdPayment, LongLists.singleton(payInfo.getPayId()));
                    res.setWithdrawAccountId(payInfo.getPayId() + "");

                    switch (currencyId) {
                        case 1000:
                        case 1001:
                            RedisPoolManager.getInstance().executeAsync(commands ->
                                    commands.sadd(RedisAllGame.Platform_All_WithdrawAccount.getKey(player.getBusiness_no(), currencyIdPayment), finalWithdrawAccount_)
                            );
                            break;
                        case 1003:
                            RedisPoolManager.getInstance().executeAsync(commands ->
                                    commands.sadd(RedisAllGame.Platform_All_WithdrawAccount.getKey(player.getBusiness_no(), currencyIdPayment), extend2)
                            );
                            break;
                    }
                    break;
                case 2://删除
                    final Map<Long, PayInfo> payInfosMap = withdrawAccount.getPayInfoMap();
                    if (payInfosMap.isEmpty()) {
                        break;
                    }

                    if (currencyId == Currency.BRL.getCurrencyId()) {
                        res.setError(ErrorCode.AccountBound_NotDelete.getCode());
                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                        return;
                    }

                    payInfosMap.remove(withdrawAccountId);
                    EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                            .updateDeleteWithdrawAccount(player, currencyIdPayment, LongLists.singleton(withdrawAccountId));
                    break;
            }
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqAddWithdrawAccountHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

}
