package com.game.handler.tcp.billing;

import com.alibaba.fastjson.JSONObject;
import com.game.c_entity.merchant.C_Activity;
import com.game.c_entity.merchant.C_ContinuousDeposit;
import com.game.c_entity.merchant.C_PlatformRecharge;
import com.game.c_entity.merchant.C_RechargeExchange;
import com.game.c_entity.middleplatform.C_BasePaymentMethod;
import com.game.engine.io.conf.ConstantConfig;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.HttpUtils11;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.PayInfo;
import com.game.entity.player.Player;
import com.game.entity.player.activity.ActivityData;
import com.game.entity.player.activity.continuousDeposit.ContinuousDeposit;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.player.PlayerMrg;
import com.proto.BillingMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import org.apache.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.http.HttpResponse;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@IHandlerEntity(mid = MIDMessage.MID.ReqDepositData_VALUE, msg = BillingMessage.ReqDepositDataMessage.class)
public class ReqDepositDataHandler extends TcpHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(ReqDepositDataHandler.class);

    @Override
    public void run() {
        final BillingMessage.ResDepositDataMessage.Builder res = BillingMessage.ResDepositDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResDepositData_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final BillingMessage.ReqDepositDataMessage req = (BillingMessage.ReqDepositDataMessage) getMessage();
            final int currencyId = req.getCurrencyId();

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            switch (currencyId / 1000) {
                case 1://法币
                    final Map<String, C_PlatformRecharge> c_platformRechargeFiatMap = merchantData.findC_PlatformRechargeFiat(this.getClass().getSimpleName(), currencyId);
                    if (c_platformRechargeFiatMap == null || c_platformRechargeFiatMap.isEmpty()) {
//                        res.setError(ErrorCode.Data_Error.getCode());
                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                        return;
                    }
                    for (C_PlatformRecharge c_platformRecharge : c_platformRechargeFiatMap.values()) {
                        res.addDepositFiatData(buildDepositFiatData(player, currencyId, merchantData, c_platformRecharge));
                    }
                    MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                    break;
                case 2://加密
                    final List<C_PlatformRecharge> c_platformRechargeCryptoList = merchantData.findC_PlatformRechargeCrypto(this.getClass().getSimpleName(), currencyId);
                    if (c_platformRechargeCryptoList == null || c_platformRechargeCryptoList.isEmpty()) {
//                        res.setError(ErrorCode.Data_Error.getCode());
                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                        return;
                    }
                    final String network = req.getNetwork();
                    final Optional<C_PlatformRecharge> optional = c_platformRechargeCryptoList.stream()
                            .filter(c_platform -> Objects.equals(c_platform.getNetwork(), network)).findFirst();
                    if (optional.isEmpty()) {
                        LOGGER.warn("playerId：{}，C_PlatformRecharge network：{}，not exist", pid, network);
                        res.setError(ErrorCode.Data_Error.getCode());
                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                        return;
                    }

                    if (player.isDepositLimit()) {
                        res.setError(ErrorCode.DepositLimit.getCode());
                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                        return;
                    }

                    final C_PlatformRecharge c_platformRecharge = optional.get();
                    final String url = "https://" + ConstantConfig.getInstance().getBackstageUrl() + "/gateway/blockchain/createWallet?"
                            + "playerId=" + pid + "&"
                            + "currencyId=" + currencyId + "&"
                            + "network=" + network;
                    HttpUtils11.sendGetAsyncHttp(url)
                            .whenComplete(((httpResponse, throwable) ->
                                    notifyClient(player, currencyId, network, merchantData, c_platformRecharge, httpResponse, throwable)));
                    break;
            }
        } catch (Exception e) {
            LOGGER.error("ReqDepositDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    private BillingMessage.DepositFiatData buildDepositFiatData(Player player, int currencyId, MerchantData merchantData, C_PlatformRecharge c_platformRecharge) {
        final BillingMessage.DepositFiatData.Builder depositFiatData = BillingMessage.DepositFiatData.newBuilder();
        depositFiatData.setCurrencyId(c_platformRecharge.getCurrencyId())
                .setChannel(c_platformRecharge.getChannel())
                .setSort(c_platformRecharge.getSort())
                .setCategory(c_platformRecharge.getCategory())
                .setPaymentMethod(c_platformRecharge.getPaymentMethod())
                .setLowerLimit(c_platformRecharge.getSingleRechargeLowerLimit())
                .setUpperLimit(c_platformRecharge.getSingleRechargeUpperLimit());
        for (Map.Entry<String, C_PlatformRecharge.RechargeInfo> entry : c_platformRecharge.getRechargeInfoMap().entrySet()) {
            depositFiatData.addDepositInfo(buildDepositInfoInfo(entry.getKey(), entry.getValue()));
        }
        final C_BasePaymentMethod c_basePaymentMethod = DataHallMrg.getInstance().findC_BasePaymentMethod(this.getClass().getSimpleName(), c_platformRecharge.getPaymentMethod());
        if (c_basePaymentMethod != null) {
            depositFiatData.setPaymentMethodName(c_basePaymentMethod.getName())
                    .setPaymentMethodLog(c_basePaymentMethod.getIcon());
        }

        final C_Activity c_activity = findManyRecharge(player, merchantData);
        final C_ContinuousDeposit.ContinuousDeposit continuousDeposit = findContinuousDeposit(player);
        for (C_PlatformRecharge.PayAmountInfo payAmountInfo : c_platformRecharge.getPayAmountInfoMap().values()) {
            final double amount = payAmountInfo.getAmount();
            final int tag = payAmountInfo.getTag();
            final BillingMessage.RechargeAmount.Builder rechargeAmount = BillingMessage.RechargeAmount.newBuilder();
            if (c_activity != null) {
                final C_Activity.RewardInfo recharge = c_activity.findRechargeRewardInfo(currencyId, amount);
                if (recharge != null) {
                    rechargeAmount.setType(recharge.getRewardType())
                            .setExtra(recharge.getReward())
                            .setMaxExtra(recharge.getMaxReward());
                }
            } else {
                if (continuousDeposit != null) {
                    if (amount >= continuousDeposit.getRecharge()) {
                        double giveawayAmount = BigDecimalUtils.mul(amount, continuousDeposit.getGiveawayRate(), 4);
                        if (giveawayAmount > continuousDeposit.getGiveawayUpper()) {
                            giveawayAmount = continuousDeposit.getGiveawayUpper();
                        }
                        rechargeAmount.setType(1)
                                .setExtra(giveawayAmount);
                    }
                }
            }
            rechargeAmount.setAmount(amount)
                    .setTag(tag);
            depositFiatData.addRechargeAmount(rechargeAmount.build());
        }

        if (c_activity != null) {
            final BillingMessage.FirstRecharge.Builder firstRecharge = BillingMessage.FirstRecharge.newBuilder();
            final List<C_Activity.RewardInfo> rechargeList = c_activity.getRewardsMap().get(currencyId);
            if (rechargeList != null && !rechargeList.isEmpty()) {
                for (C_Activity.RewardInfo recharge : rechargeList) {
                    firstRecharge.setRechargeAmountMin(recharge.getMin())
                            .setRechargeAmountMax(recharge.getMax())
                            .setRewardType(recharge.getRewardType())
                            .setReward(recharge.getReward())
                            .setMaxReward(recharge.getMaxReward());
                    depositFiatData.addFirstRechargeList(firstRecharge.build());
                }
            }
        }

//        if (c_platformRecharge.getPaymentMethod() == 101) {//pix
//            final String currencyIdChannel = c_platformRecharge.getCurrencyId() + "_" + c_platformRecharge.getPaymentMethod();
//            final WithdrawAccount withdrawAccount = player.getWithdrawAccountMap().get(currencyIdChannel);
//            if (withdrawAccount != null) {
//                for (PayInfo payInfo : withdrawAccount.getPayInfoMap().values()) {
//                    depositFiatData.setWithdrawAccount(buildWithdrawAccount(payInfo));
//                }
//            }
//        }
        return depositFiatData.build();
    }

    private BillingMessage.DepositInfo buildDepositInfoInfo(String filed, C_PlatformRecharge.RechargeInfo rechargeInfo) {
        return BillingMessage.DepositInfo.newBuilder()
                .setField(filed)
                .addAllParams(rechargeInfo.getParams())
                .setType(rechargeInfo.getType())
                .build();
    }

    private BillingMessage.WithdrawAccount buildWithdrawAccount(PayInfo payInfo) {
        return BillingMessage.WithdrawAccount.newBuilder()
                .setWithdrawAccountId(payInfo.getPayId() + "")
                .setExtend0(payInfo.getExtend())
                .setExtend1(payInfo.getExtend_1())
                .setExtend2(payInfo.getExtend_2())
                .setExtend3(StringUtil.isNullOrEmpty(payInfo.getExtend_3()) ? "" : payInfo.getExtend_3())
                .build();
    }

    private void notifyClient(Player player, int currencyId, String network, MerchantData merchantData, C_PlatformRecharge c_platformRecharge, HttpResponse<String> httpResponse, Throwable throwable) {
        final BillingMessage.ResDepositDataMessage.Builder res = BillingMessage.ResDepositDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResDepositData_VALUE);

        if (throwable != null || httpResponse.statusCode() != HttpStatus.SC_OK) {
            res.setError(ErrorCode.Data_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
            throw new IllegalArgumentException(throwable);
        }

        try {
            final String body = httpResponse.body();
            final JSONObject dataMap = JsonUtils.readFromJson(body, JSONObject.class);
            final String code = dataMap.getString("code");
            if (!Objects.equals(code, "0")) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }
            final String walletAddress = dataMap.getString("walletAddress");

            final BillingMessage.DepositCryptoData.Builder depositCrypto = BillingMessage.DepositCryptoData.newBuilder();
            final C_Activity c_activity = findManyRecharge(player, merchantData);
            if (c_activity != null) {
                final List<C_Activity.RewardInfo> recharges = c_activity.getRewardsMap().get(currencyId);
                if (recharges != null && !recharges.isEmpty()) {
                    final C_Activity.RewardInfo recharge = recharges.getLast();
                    depositCrypto.setType(recharge.getRewardType())
                            .setExtra(recharge.getReward());
                    final C_Activity.RewardInfo minRecharge = recharges.getFirst();
                    if (recharge.getRewardType() == 1) {
                        depositCrypto.setMinimumExtra(recharge.getReward());
                    } else {
                        depositCrypto.setMinimumExtra(BigDecimalUtils.mul(recharge.getReward(), minRecharge.getMin(), 4));
                    }
                }
            }
            depositCrypto.setCurrencyId(c_platformRecharge.getCurrencyId())
                    .setNetwork(network)
                    .setWalletAddress(walletAddress)
                    .setMinimum(c_platformRecharge.getSingleRechargeLowerLimit());
            final String rechargeExchangeOpen = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "rechargeExchangeOpen");
            if (!StringUtil.isNullOrEmpty(rechargeExchangeOpen) && Boolean.parseBoolean(rechargeExchangeOpen)) {
                final C_RechargeExchange c_rechargeExchange = merchantData.findC_RechargeExchange(this.getClass().getSimpleName(), currencyId);
                if (c_rechargeExchange != null) {
                    depositCrypto.setExchangeRate(c_rechargeExchange.getRate());
                }
            }
            res.setDepositCryptoData(depositCrypto.build());

            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqDepositDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    private C_Activity findManyRecharge(Player player, MerchantData merchantData) {
        final List<C_Activity> c_activityList = merchantData.getC_activityListMap().get(ActivityMrg.MANY_DEPOSIT);

        if (c_activityList == null || c_activityList.isEmpty()) {
            return null;
        }

        final C_Activity first = c_activityList.stream().findFirst().get();

        int rechargeTimes = 0;
        if (first.getSettlementCycle() == 0) {
            rechargeTimes = player.getTotalRechargeTimes();
        } else if (first.getSettlementCycle() == 1) {
            rechargeTimes = player.getDailyRechargeTimes();
        } else {
            rechargeTimes = player.getWeeklyRechargeTimes();
        }

        if (rechargeTimes <= 2) {
            final int finalRechargeTimes = rechargeTimes;
            final Optional<C_Activity> optional = c_activityList.stream().filter(activity ->
                    activity.getActivitySubType() == finalRechargeTimes + 1).findFirst();
            if (optional.isEmpty()) {
                return null;
            }
            final C_Activity c_activity = optional.get();
            final int activityId = Integer.parseInt(c_activity.getActivityId() + "" + c_activity.getActivitySubType());
            final ActivityData activityData = player.getActivityInfo().getActivityData(activityId);
            if (activityData != null) {
                if (activityData.getRegisterExpired() > 0 && TimeUtil.currentTimeMillis() >= activityData.getRegisterExpired()) {
                    return null;
                }
            }
            if (TimeUtil.currentTimeMillis() < c_activity.getStartTime() || TimeUtil.currentTimeMillis() >= c_activity.getEndTime()) {
                return null;
            }
            return c_activity;
        }
        return null;
    }

    private C_ContinuousDeposit.ContinuousDeposit findContinuousDeposit(Player player) {
        final ContinuousDeposit continuousDepositInfo = player.getContinuousDepositInfo();
        if (!continuousDepositInfo.isStart()) {
            return null;
        }

        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return null;
        }

        final C_ContinuousDeposit c_continuousDeposit = merchantData.findC_ContinuousDeposit(this.getClass().getSimpleName(), ActivityMrg.CONTINUOUS_DEPOSIT);
        if (c_continuousDeposit == null) {
            return null;
        }

        return c_continuousDeposit.getContinuousDepositMap().get(continuousDepositInfo.getRechargeTimes() + 1);
    }
}
