package com.game.handler.tcp.affiliate;

import com.alibaba.fastjson.JSONObject;
import com.game.c_entity.middleplatform.C_BaseGameType;
import com.game.c_entity.merchant.C_CashBack;
import com.game.dao.game.GameNoteDao;
import com.game.dao.player.PlayerPromoteDao;
import com.game.dao.promote.CommissionRewardsNoteDao;
import com.game.dao.promote.TeamRewardsNoteDao;
import com.game.dao.promote.ThreeLevelRewardsNoteDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.util.misc.Tuple2;
import com.game.engine.utils.FastCollectionsUtils;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.Symbol;
import com.game.engine.utils.TimeUtil;
import com.game.entity.MonthlyReport;
import com.game.entity.game.GameNoteFields;
import com.game.entity.game.GameWagerStat;
import com.game.entity.player.Player;
import com.game.entity.player.promote.*;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.CommonMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.mrg.currency.RewardRequest;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import com.proto.AffiliateMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import it.unimi.dsi.fastutil.ints.Int2DoubleMap;
import it.unimi.dsi.fastutil.objects.ObjectIterator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@IHandlerEntity(mid = MIDMessage.MID.ReqDashboardData_VALUE, msg = AffiliateMessage.ReqDashboardDataMessage.class)
public class ReqDashboardDataHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqDashboardDataHandler.class);

    @Override
    public void run() {
        final AffiliateMessage.ResDashboardDataMessage.Builder res = AffiliateMessage.ResDashboardDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResDashboardData_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }
            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }
            final PlayerPromote playerPromote = player.getPlayerPromote();

            final AffiliateMessage.ReqDashboardDataMessage req = (AffiliateMessage.ReqDashboardDataMessage) getMessage();
            final int currencyId = req.getCurrencyId();

            if (currencyId == 0) {
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final Map<String, ReferralCode> referralCodeMap = playerPromote.getReferralCodeMap();
            int totalFriends = 0;
            {
                final Tuple2<Integer, List<Long>> totalOneLevelsTuple = EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class)
                        .findByCodeOneLevels(new ArrayList<>(referralCodeMap.keySet()), 0, 0);
                final Tuple2<Integer, List<Long>> totalTwoLevelsTuple = EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class)
                        .findByCodeTwoLevels(new ArrayList<>(referralCodeMap.keySet()), 0, 0);
                final Tuple2<Integer, List<Long>> totalThreeLevelsTuple = EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class)
                        .findByCodeThreeLevels(new ArrayList<>(referralCodeMap.keySet()), 0, 0);
                totalFriends = totalOneLevelsTuple.getFirst() + totalTwoLevelsTuple.getFirst() + totalThreeLevelsTuple.getFirst();
            }

            double availableAmount = 0;
            double totalRebate = 0;
            {
                final CommissionRewards commissionRewards = playerPromote.getCommissionRewardsMap().get(currencyId);
                if (commissionRewards != null) {
                    availableAmount = BigDecimalUtils.add(commissionRewards.getAvailable(), availableAmount, 9);
                    totalRebate = BigDecimalUtils.add(commissionRewards.getTotalReceived(), totalRebate, 9);
                }
                final TeamRewards teamRewards = playerPromote.getTeamRewardsMap().get(currencyId);
                if (teamRewards != null) {
                    availableAmount = BigDecimalUtils.add(teamRewards.getAvailable(), availableAmount, 9);
                    totalRebate = BigDecimalUtils.add(teamRewards.getTotalReceived(), totalRebate, 9);
                }
                final ThreeLevelRewards threeLevelRewards = playerPromote.getThreeLevelRewardsMap().get(currencyId);
                if (threeLevelRewards != null) {
                    availableAmount = BigDecimalUtils.add(threeLevelRewards.getAvailable(), availableAmount, 9);
                    totalRebate = BigDecimalUtils.add(threeLevelRewards.getTotalReceived(), totalRebate, 9);
                }
            }
//            LOGGER.warn("playerId：{}，currencyId：{}", pid, currencyId);
//            LOGGER.warn("playerId：{}，totalRebate：{}", pid, totalRebate);
//            LOGGER.warn("playerId：{}，availableAmount：{}", pid, availableAmount);

            double totalReferralRewards = 0;
            {
                final ReferralRewards referralRewards = playerPromote.getReferralRewardsMap().get(currencyId);
                if (referralRewards != null) {
                    totalReferralRewards = referralRewards.getTotalReceived();
                }
            }

            final long startTime = TimeUtil.getTimeBeginOfToday(TimeUtil.currentTimeMillis(), player.getTimeZone());
            final long endTime = TimeUtil.getTimeEndOfToday(TimeUtil.currentTimeMillis(), player.getTimeZone());
            final Tuple2<Integer, List<Long>> oneLevelsTuple = EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class)
                    .findByCodeOneLevels(new ArrayList<>(referralCodeMap.keySet()), startTime, endTime);
            final Tuple2<Integer, List<Long>> twoLevelsTuple = EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class)
                    .findByCodeTwoLevels(new ArrayList<>(referralCodeMap.keySet()), startTime, endTime);
            final Tuple2<Integer, List<Long>> threeLevelsTuple = EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class)
                    .findByCodeThreeLevels(new ArrayList<>(referralCodeMap.keySet()), startTime, endTime);

            //人数
            final int oneLevelFriends = oneLevelsTuple.getFirst();
            final int twoLevelFriends = twoLevelsTuple.getFirst();
            final int threeLevelFriends = threeLevelsTuple.getFirst();
            final int registered = oneLevelFriends + twoLevelFriends + threeLevelFriends;

            //返水
            final double oneRebate = EntityDaoMrg.getInstance().getDao(CommissionRewardsNoteDao.class)
                    .aggregateRewards(player.getPlayerId(), currencyId, CommissionRewardsNoteFields.amount, startTime, endTime);
            final double twoRebate = EntityDaoMrg.getInstance().getDao(TeamRewardsNoteDao.class)
                    .aggregateRewards(player.getPlayerId(), currencyId, TeamRewardsNoteFields.amount, startTime, endTime);
            final double threeRebate = EntityDaoMrg.getInstance().getDao(ThreeLevelRewardsNoteDao.class)
                    .aggregateRewards(player.getPlayerId(), currencyId, ThreeLevelRewardsNoteFields.amount, startTime, endTime);

            //有效下注
            final List<Long> friends = new ArrayList<>();
            friends.addAll(oneLevelsTuple.getSecond());
            friends.addAll(twoLevelsTuple.getSecond());
            friends.addAll(threeLevelsTuple.getSecond());
            final GameWagerStat gameWagerStat = EntityDaoMrg.getInstance().getDao(GameNoteDao.class)
                    .aggregateGameWagerStat(friends, currencyId, GameNoteFields.validBets, startTime, endTime);

            final String referralLink = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "referralLink");
            if (!StringUtil.isNullOrEmpty(referralLink)) {
                final String url = referralLink + "?" + "referralCode=" + player.getInvitationCode();
                res.setReferralLink(url);
            }
            final String lockedRewards = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "lockedRewards");
            if (!StringUtil.isNullOrEmpty(lockedRewards)) {
                final String[] mh = lockedRewards.split(Symbol.MAOHAO_REG);
                if (mh.length > 0) {
                    res.setReferralBonus(CurrencyMrg.getInstance().buildCurrency(Integer.parseInt(mh[0]), Integer.parseInt(mh[1])));
                }
            }

            final String referralRewardOpen = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "referralRewardOpen");

            res.setReferralCode(player.getInvitationCode())
                    .setTotalFriends(totalFriends)
                    .setTotalRebate(totalRebate)
                    .setTotalRewards(totalReferralRewards)
                    .setDayRebateInfo(buildDayRebateInfo(registered, gameWagerStat.getTotalWager()
                            , oneLevelFriends, oneRebate
                            , twoLevelFriends, twoRebate
                            , threeLevelFriends, threeRebate))
                    .setReferralRewardOpen(!StringUtil.isNullOrEmpty(referralRewardOpen) && Boolean.parseBoolean(referralRewardOpen));

            final OptionalDouble commissionRate = merchantData.getC_cashBackMap().values().stream()
                    .mapToDouble(C_CashBack::getSuperiorCashBackRate).max();

            final OptionalDouble teamRate = merchantData.getC_cashBackMap().values().stream()
                    .mapToDouble(C_CashBack::getTeamCashBackRate).max();

            final OptionalDouble threeLevelRate = merchantData.getC_cashBackMap().values().stream()
                    .mapToDouble(C_CashBack::getThreeLevelCashBackRate).max();

            final String rebateMethod = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "rebateMethod");
            res.setOneLevelRate(commissionRate.isPresent() ? commissionRate.getAsDouble() : 0)
                    .setTwoLevelRate(teamRate.isPresent() ? teamRate.getAsDouble() : 0)
                    .setThreeLevelRate(threeLevelRate.isPresent() ? threeLevelRate.getAsDouble() : 0)
                    .setRebateMethod(Integer.parseInt(StringUtil.isNullOrEmpty(rebateMethod) ? "0" : rebateMethod));

            if (res.getRebateMethod() == 4) {//佣金
                for (C_CashBack c_cashBack : merchantData.getC_cashBackMap().values()) {
                    final C_BaseGameType c_baseGameType = DataHallMrg.getInstance().findC_GameType(this.getClass().getSimpleName(), c_cashBack.getGameType());
                    if (c_baseGameType == null) {
                        continue;
                    }
                    res.addRebateRateList(buildRebateRate(player.getLanguage(), c_cashBack, c_baseGameType));
                }
            } else {
                for (C_CashBack c_cashBack : merchantData.getC_cashBackMap().values()) {
                    final C_BaseGameType c_baseGameType = DataHallMrg.getInstance().findC_GameType(this.getClass().getSimpleName(), c_cashBack.getGameType());
                    if (c_baseGameType == null) {
                        continue;
                    }
                    res.addRebateRateList(buildRebateRate(player.getLanguage(), c_cashBack, c_baseGameType));
                }
            }

            {
                final RewardRequest rewardRequest = new RewardRequest();
                final ReferralRewards availableRewards = playerPromote.getReferralRewardsMap().get(currencyId);
                if (availableRewards != null) {
                    rewardRequest.addCurrency(currencyId, availableRewards.getAvailable());
                }

                if (availableAmount > 0) {
                    rewardRequest.addCurrency(currencyId, availableAmount);
                }

                for (ObjectIterator<Int2DoubleMap.Entry> iterator = FastCollectionsUtils.fastIterator(rewardRequest.getCurrencyMap()); iterator.hasNext(); ) {
                    final Int2DoubleMap.Entry entry = iterator.next();
                    res.setAvailableReward(CurrencyMrg.getInstance().buildCurrency(entry.getIntKey(), entry.getDoubleValue()));
                }
            }

            final Map<Long, Double> recentRebateMap = getRecentRebateList(player, currencyId);
            for (Map.Entry<Long, Double> entry : recentRebateMap.entrySet()) {
                res.addSevenRebateList(buildSevenRebateInfo(entry.getKey(), entry.getValue()));
            }

            
            final int year = TimeUtil.getYear();
            final List<MonthlyReport> monthlyReports = getMonthlyReport(player, currencyId, year);
            for (final MonthlyReport monthlyReport : monthlyReports) {
                res.addMonthlyReport(buildMonthlyReport(monthlyReport));
            }

            final String invitedWagered = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "invitedWagered");
            if (!StringUtil.isNullOrEmpty(invitedWagered)) {
                final String[] wagereds = invitedWagered.split(Symbol.MAOHAO_REG);
                res.setValidWagered(Double.parseDouble(wagereds[1]));
            }
            final String invitedRecharge = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "invitedRecharge");
            if (!StringUtil.isNullOrEmpty(invitedRecharge)) {
                final String[] recharges = invitedRecharge.split(Symbol.MAOHAO_REG);
                res.setDeposit(Double.parseDouble(recharges[1]));
            }

            final String affilateText = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "affilateText");
            if (!StringUtil.isNullOrEmpty(affilateText)) {
                final List<Rule> rules = JSONObject.parseArray(affilateText, Rule.class);
                for (Rule rule : rules) {
                    if (rule.language == player.getLanguage()) {
                        res.setText(rule.getRule());
                    }
                }
            }

            final String invitedRewards = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "invitedRewards");
            if (!StringUtil.isNullOrEmpty(invitedRewards)) {
                final String[] rewards = invitedRewards.split(Symbol.MAOHAO_REG);
                res.setInvitedRewards(CurrencyMrg.getInstance().buildCurrency(Integer.parseInt(rewards[0]), Double.parseDouble(rewards[1])));
            }

//            LOGGER.warn("ReqDashboardDataHandler：{}", res.build());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqDashboardDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    public static class Rule {
        public int language;
        public String rule;

        public int getLanguage() {
            return language;
        }

        public void setLanguage(int language) {
            this.language = language;
        }

        public String getRule() {
            return StringUtil.isNullOrEmpty(rule) ? "" : rule;
        }

        public void setRule(String rule) {
            this.rule = rule;
        }
    }

    /**
     * int32 registered                    = 1; //注册人数
     * double validWagered                 = 2; //有效下注
     * int32 oneLevels                     = 3; //一级人数
     * double oneRebate                    = 4; //一级奖励
     * int32 twoLevels                     = 5; //二级人数
     * double twoRebate                    = 6; //二级奖励
     * int32 threeLevels                   = 7; //三级人数
     * double threeRebate                  = 8; //三级奖励
     *
     * @return
     */
    private AffiliateMessage.DayRebateInfo buildDayRebateInfo(int registered, double validWagered,
                                                              int oneLevels, double oneRebate,
                                                              int twoLevels, double twoRebate,
                                                              int threeLevels, double threeRebate) {
        return AffiliateMessage.DayRebateInfo.newBuilder()
                .setRegistered(registered)
                .setValidWagered(validWagered)
                .setOneLevels(oneLevels)
                .setOneRebate(oneRebate)
                .setTwoLevels(twoLevels)
                .setTwoRebate(twoRebate)
                .setThreeLevels(threeLevels)
                .setThreeRebate(threeRebate)
                .build();
    }

    private AffiliateMessage.SevenRebateInfo buildSevenRebateInfo(long date, double amount) {
        return AffiliateMessage.SevenRebateInfo.newBuilder()
                .setDate(date)
                .setAmount(amount)
                .build();
    }

    private AffiliateMessage.MonthlyReport buildMonthlyReport(MonthlyReport monthlyReport) {
        return AffiliateMessage.MonthlyReport.newBuilder()
                .setMonth(monthlyReport.getMonth())
                .setActivePlayer(monthlyReport.getActivePlayer())
                .setWagered(monthlyReport.getWagered())
                .setRebate(monthlyReport.getRebate())
                .build();
    }

    private AffiliateMessage.RebateRate buildRebateRate(int language, C_CashBack c_cashBack, C_BaseGameType c_Base_gameType) {
        final AffiliateMessage.RebateRate.Builder rebateRate = AffiliateMessage.RebateRate.newBuilder()
                .setGameType(c_cashBack.getGameType())
                .setRate(c_cashBack.getGameCashBackRate())
                .setEffectiveRate(c_cashBack.getEffectiveRate())
                .setOneLevelRate(c_cashBack.getSuperiorCashBackRate())
                .setTwoLevelRate(c_cashBack.getTeamCashBackRate())
                .setThreeLevelRate(c_cashBack.getThreeLevelCashBackRate());
        final C_BaseGameType.GameTypeInfo gameTypeInfo = c_Base_gameType.getGameTypeInfoMap().get(language);
        if (gameTypeInfo != null) {
            rebateRate.setGameCategory(gameTypeInfo.getGameCategoryName())
                    .setTypeName(gameTypeInfo.getGameTypeName());
        }
        return rebateRate.build();
    }

    private Map<Long, Double> getRecentRebateList(Player player, int currencyId) {
        final Map<Long, Double> rebateMap = new LinkedHashMap<>();
        long startTime = TimeUtil.getTimeBeginOfToday(TimeUtil.currentTimeMillis(), player.getTimeZone());
        long endTime = TimeUtil.getTimeEndOfToday(TimeUtil.currentTimeMillis(), player.getTimeZone());
        for (int i = 0; i <= 6; i++) {
            final long start = startTime - i * TimeUtil.DAY;
            final long end = endTime - i * TimeUtil.DAY;

            final double totalCommissionRewards = EntityDaoMrg.getInstance().getDao(CommissionRewardsNoteDao.class)
                    .aggregateRewards(player.getPlayerId(), currencyId, CommissionRewardsNoteFields.amount, start, end);
            final double totalTeamRewards = EntityDaoMrg.getInstance().getDao(TeamRewardsNoteDao.class)
                    .aggregateRewards(player.getPlayerId(), currencyId, TeamRewardsNoteFields.amount, start, end);
            final double totalThreeLevelRewards = EntityDaoMrg.getInstance().getDao(ThreeLevelRewardsNoteDao.class)
                    .aggregateRewards(player.getPlayerId(), currencyId, ThreeLevelRewardsNoteFields.amount, start, end);
            final double totalRebate = BigDecimalUtils.add(totalCommissionRewards + totalTeamRewards, totalThreeLevelRewards, 9);
            rebateMap.put(start, totalRebate);
        }
        return rebateMap;
    }

    public List<MonthlyReport> getMonthlyReport(Player player, int currencyId, int year) {
        final PlayerPromote playerPromote = player.getPlayerPromote();
        final Map<String, ReferralCode> referralCodeMap = playerPromote.getReferralCodeMap();

        final List<MonthlyReport> monthlyReports = new ArrayList<>();

        final ZoneId zoneId = ZoneId.of(player.getTimeZone());
        final int currentYear = LocalDate.now().getYear();
        final int currentMonth = LocalDate.now().getMonthValue();

        // 如果是今年，只查到当前月；否则查到12月
        final int maxMonth = (year == currentYear) ? currentMonth : 12;

        for (int month = 1; month <= maxMonth; month++) {
            final LocalDate startDate = LocalDate.of(year, month, 1);
            final LocalDate endDate = startDate.plusMonths(1);

            final long startTime = startDate.atStartOfDay(zoneId).toInstant().toEpochMilli();
            final long endTime = endDate.atStartOfDay(zoneId).toInstant().toEpochMilli();

            final Tuple2<Integer, List<Long>> oneLevelsTuple = EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class)
                    .findByCodeOneLevels(new ArrayList<>(referralCodeMap.keySet()), startTime, endTime);
            final Tuple2<Integer, List<Long>> twoLevelsTuple = EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class)
                    .findByCodeTwoLevels(new ArrayList<>(referralCodeMap.keySet()), startTime, endTime);
            final Tuple2<Integer, List<Long>> threeLevelsTuple = EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class)
                    .findByCodeThreeLevels(new ArrayList<>(referralCodeMap.keySet()), startTime, endTime);

            final List<Long> friends = new ArrayList<>();
            friends.addAll(oneLevelsTuple.getSecond());
            friends.addAll(twoLevelsTuple.getSecond());
            friends.addAll(threeLevelsTuple.getSecond());
            final GameWagerStat gameWagerStat = EntityDaoMrg.getInstance().getDao(GameNoteDao.class)
                    .aggregateActivePlayerAndWagered(friends, currencyId, startTime, endTime);

            final double totalCommissionRewards = EntityDaoMrg.getInstance().getDao(CommissionRewardsNoteDao.class)
                    .aggregateRewards(player.getPlayerId(), currencyId, CommissionRewardsNoteFields.amount, startTime, endTime);
            final double totalTeamRewards = EntityDaoMrg.getInstance().getDao(TeamRewardsNoteDao.class)
                    .aggregateRewards(player.getPlayerId(), currencyId, TeamRewardsNoteFields.amount, startTime, endTime);
            final double totalThreeLevelRewards = EntityDaoMrg.getInstance().getDao(ThreeLevelRewardsNoteDao.class)
                    .aggregateRewards(player.getPlayerId(), currencyId, ThreeLevelRewardsNoteFields.amount, startTime, endTime);
            final double totalRebate = BigDecimalUtils.add(totalCommissionRewards + totalTeamRewards, totalThreeLevelRewards, 9);

            final MonthlyReport monthlyReport = new MonthlyReport();
            monthlyReport.setMonth(month);
            monthlyReport.setActivePlayer(gameWagerStat.getTotalActivePlayerCount());
            monthlyReport.setWagered(gameWagerStat.getTotalWager());
            monthlyReport.setRebate(totalRebate);

            monthlyReports.add(monthlyReport);
        }
        return monthlyReports;
    }

}
