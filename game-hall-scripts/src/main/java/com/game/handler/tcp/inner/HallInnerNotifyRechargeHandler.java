package com.game.handler.tcp.inner;

import com.game.c_entity.merchant.C_Activity;
import com.game.c_entity.merchant.C_RechargeExchange;
import com.game.c_entity.merchant.C_RechargeRecover;
import com.game.c_entity.merchant.C_RechargeTurnover;
import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.log.GameLog;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.util.misc.Tuple2;
import com.game.engine.utils.GuidGeneratorUtils;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.*;
import com.game.entity.player.activity.rechargeRecover.RechargeRecoverInfo;
import com.game.entity.player.bonus.BonusDetailsInfo;
import com.game.entity.player.bonus.BonusInfo;
import com.game.entity.player.stats.Stats;
import com.game.enums.*;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.*;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.mrg.currency.RewardRequest;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.mrg.quest.DefaultBlackboard;
import com.game.hall.mrg.quest.QuestMrg;
import com.game.hall.script.*;
import com.game.hall.script.activity.IActivityScript;
import com.game.manager.EntityDaoMrg;
import com.proto.CommonMessage;
import com.proto.InnerMessage;
import com.proto.MIDMessage;
import com.proto.TcpMessage;
import com.wjybxx.util.box.Tuple3;
import io.netty.util.AttributeKey;
import io.netty.util.internal.StringUtil;
import it.unimi.dsi.fastutil.ints.IntLists;
import it.unimi.dsi.fastutil.longs.LongArrayList;
import it.unimi.dsi.fastutil.longs.LongList;
import it.unimi.dsi.fastutil.longs.LongLists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

@IHandlerEntity(mid = MIDMessage.MID.InnerNotifyRecharge_VALUE, msg = InnerMessage.InnerNotifyRechargeMessage.class)
public class HallInnerNotifyRechargeHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(HallInnerNotifyRechargeHandler.class);

    @Override
    public void run() {
        final InnerMessage.InnerNotifyRechargeMessage rechargeSuccess = (InnerMessage.InnerNotifyRechargeMessage) getMessage();
        int rechargeCurrencyId = rechargeSuccess.getCurrencyId();
        double rechargeAmount = rechargeSuccess.getAmount();
        final long orderId = rechargeSuccess.getOrderId();
        final int status = rechargeSuccess.getStatus();
        final CommonMessage.FbInfo fbInfo = rechargeSuccess.getFbInfo();

        final Player player = PlayerMrg.getInstance().findDbPlayer(pid);
        if (player == null) {
            LOGGER.warn("playerId：{}，not exist", pid);
            return;
        }

        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData == null) {
            return;
        }

        final String rechargeExchangeOpen = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "rechargeExchangeOpen");
        if (!StringUtil.isNullOrEmpty(rechargeExchangeOpen) && Boolean.parseBoolean(rechargeExchangeOpen)) {
            final C_RechargeExchange c_rechargeExchange = merchantData.findC_RechargeExchange(this.getClass().getSimpleName(), rechargeCurrencyId);
            if (c_rechargeExchange != null) {
                rechargeAmount = BigDecimalUtils.mul(rechargeAmount, c_rechargeExchange.getRate(), 9);
                rechargeCurrencyId = c_rechargeExchange.getExchangeCurrency();
            }
        }
        final double amount = rechargeAmount;
        final int currencyId = rechargeCurrencyId;
        try {
            if (status == 3 || status == 4) {//失败
                //TODO 邮件通知
                //InboxMrg.getInstance().sendInboxMail(player, InboxMrg.DEPOSIT_FAIL, Collections.singletonList(player.getPlayerName()));
                final Stats stats = player.getStats(currencyId);
                final TcpMessage.ResTcpRechargeSuccessMessage.Builder res = TcpMessage.ResTcpRechargeSuccessMessage.newBuilder();
                res.setMsgID(MIDMessage.MID.ResTcpRechargeSuccess_VALUE)
                        .setPlayerId(player.get_id())
                        .setOrderId(orderId)
                        .setCurrencyId(currencyId)
                        .setCurrRecharge(amount)
                        .setRechargeTimes(stats.getTotalRechargeTimes() + 1)
                        .setStatus(status);
                player.sendMsg(res.build());
                return;
            }

            //TODO 充值返奖
            final RechargeRecoverInfo rechargeRecoverInfo = player.getRechargeRecoverInfo();
            final C_RechargeRecover c_rechargeRecover = merchantData.findC_RechargeRecover(this.getClass().getSimpleName(), ActivityMrg.RECHARGE_RECOVER);
            if (c_rechargeRecover != null) {
                if (!rechargeRecoverInfo.isStatus() && !rechargeRecoverInfo.getRechargeAmountMap().isEmpty()) {
                    rechargeRecoverInfo.getCurrRechargeMap().put(currencyId, amount);
                }
            }

            player.setRechargeTime(TimeUtil.currentTimeMillis());
            EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                    .updatePlayerField(pid, PlayerFields.rechargeTime, player.getRechargeTime());

            final Tuple3<Double, Double, Integer> tuple3 = ScriptLoader.getInstance().functionScript("ManyDepositScript",
                    (IActivityScript script) -> script.executeBack(player, currencyId, amount));

            final double extra = tuple3.getFirst();
            final double turnoverMul = tuple3.getSecond();
            final int rechargeTimes = tuple3.getThird() + 1;
            LOGGER.warn("playerId：{}，extra：{}", pid, extra);

            final C_RechargeTurnover c_rechargeTurnover = merchantData.findC_RechargeTurnover(this.getClass().getSimpleName(), currencyId);
            if (c_rechargeTurnover != null) {
                WithdrawStandard withdrawStandard = player.getWithdrawStandard(currencyId);
                //TODO 当余额小于上次提现之后，累计充值总量的一定比例再次充值，打码量按最新的一次充值计算
                final double balance = CurrencyMrg.getInstance().getCurrencyValue(player, currencyId);
                final double bonus = CurrencyMrg.getInstance().getCurrencyValue(player, currencyId * 10);
                if (withdrawStandard.getTotalRecharge() > 0) {
                    final LongList deleteOrderId = new LongArrayList();
                    if (c_rechargeTurnover.getClearValue() != 0 && BigDecimalUtils.add(balance, bonus, 4)
                            < c_rechargeTurnover.getClearValue()) {
//                    withdrawStandard.reset();
                        withdrawStandard.setTotalRecharge(0);
                        ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                                (IPlayerScript script) -> script.drawStandard(player, TurnoverReason.Recharge, currencyId, 0,
                                        -withdrawStandard.getDrawStandard()));
                        ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                                (IPlayerScript script) -> script.bettingTurnover(player, TurnoverReason.Recharge, currencyId,
                                        -withdrawStandard.getBettingVolume()));

                        final Iterator<Map.Entry<Long, TurnoverRecord>> iterator = player.getTurnoverRecordMap().entrySet().iterator();
                        while (iterator.hasNext()) {
                            final Map.Entry<Long, TurnoverRecord> entry = iterator.next();
                            final TurnoverRecord turnoverRecord = entry.getValue();
                            iterator.remove();
                            deleteOrderId.add(turnoverRecord.getOrderId());

                            playerTurnoverRecordLog(player, TurnoverReason.Recharge,
                                    turnoverRecord.getOrderId(), turnoverRecord.getCurrencyId(),
                                    turnoverRecord.getAmount(), turnoverRecord.getDrawStandard(), turnoverRecord.getDrawStandard());
                        }
                    }

                    if (c_rechargeTurnover.getClearRate() != 0 && BigDecimalUtils.add(balance, bonus, 4)
                            < BigDecimalUtils.mul(withdrawStandard.getTotalRecharge(), c_rechargeTurnover.getClearRate(), 4)) {
//                    withdrawStandard.reset();
                        withdrawStandard.setTotalRecharge(0);
                        ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                                (IPlayerScript script) -> script.drawStandard(player, TurnoverReason.Recharge, currencyId, 0,
                                        -withdrawStandard.getDrawStandard()));
                        ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                                (IPlayerScript script) -> script.bettingTurnover(player, TurnoverReason.Recharge, currencyId,
                                        -withdrawStandard.getBettingVolume()));

                        final Iterator<Map.Entry<Long, TurnoverRecord>> iterator = player.getTurnoverRecordMap().entrySet().iterator();
                        while (iterator.hasNext()) {
                            final Map.Entry<Long, TurnoverRecord> entry = iterator.next();
                            final TurnoverRecord turnoverRecord = entry.getValue();
                            iterator.remove();
                            deleteOrderId.add(turnoverRecord.getOrderId());

                            playerTurnoverRecordLog(player, TurnoverReason.Recharge,
                                    turnoverRecord.getOrderId(), turnoverRecord.getCurrencyId(),
                                    turnoverRecord.getAmount(), turnoverRecord.getDrawStandard(), turnoverRecord.getDrawStandard());
                        }
                    }
                    EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                            .updateTurnoverRecord(player, LongLists.EMPTY_LIST, deleteOrderId);
                }

                withdrawStandard.incTotalRecharge(amount);
//            withdrawStandard.incDrawStandard(BigDecimalUtils.mul(amount, c_rechargeTurnover.getTurnoverRate(), 4));
//            withdrawStandard.incDrawStandard(BigDecimalUtils.mul(extra, turnoverMul, 4));
                final TurnoverReason rechargeReason = TurnoverReason.Recharge;
                rechargeReason.setSource(orderId + "");
                ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                        (IPlayerScript script) -> script.drawStandard(player, rechargeReason, currencyId, amount,
                                BigDecimalUtils.mul(amount, c_rechargeTurnover.getTurnoverRate(), 4)));

                final TurnoverReason turnoverReason = TurnoverReason.Recharge_Giveaway;
                turnoverReason.setSource(orderId + "");
                ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                        (IPlayerScript script) -> script.drawStandard(player, turnoverReason, currencyId, extra,
                                BigDecimalUtils.mul(extra, turnoverMul, 4)));

                EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                        .updateWithdrawStandard(player, IntLists.singleton(currencyId));

                EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                        .updateTurnoverRecord(player);
            }

            final RewardRequest rewardRequest = new RewardRequest();
            rewardRequest.addCurrency(currencyId, amount, false);
            final RewardReason rewardReason = RewardReason.Recharge;
            rewardReason.setSource(orderId + "");
            CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getCurrencyMap(), rewardReason);

            //TODO 统计充值
            final Stats stats = player.getStats(currencyId);
            stats.incRechargeAmount(amount);
            EntityDaoMrg.getInstance().getDao(PlayerDao.class).statsDao
                    .updateStats(player.getPlayerId(), player.getStatsInfo(), IntLists.singleton(currencyId));

            //推送通知
            final TcpMessage.ResTcpRechargeSuccessMessage.Builder res = TcpMessage.ResTcpRechargeSuccessMessage.newBuilder();
            res.setMsgID(MIDMessage.MID.ResTcpRechargeSuccess_VALUE)
                    .setPlayerId(player.get_id())
                    .setOrderId(orderId)
                    .setCurrencyId(currencyId)
                    .setCurrRecharge(amount)
                    .setTotalRecharge(stats.getTotalRechargeAmount())
                    .setRechargeTimes(stats.getTotalRechargeTimes())
                    .setStatus(1);
            player.sendMsg(res.build());

            //回传fb
            if (stats.getTotalRechargeTimes() == 1) {
                player.setFirstRechargeTime(TimeUtil.currentTimeMillis());
                EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                        .updatePlayerField(pid, PlayerFields.firstRechargeTime, player.getFirstRechargeTime());

                ScriptLoader.getInstance().consumerScript("FeedbackScript",
                        (IFeedbackScript script) -> script.sendFbFeedback(fbInfo, player, currencyId, 1, amount));
                ScriptLoader.getInstance().consumerScript("FeedbackScript",
                        (IFeedbackScript script) -> script.sendFbFeedback(fbInfo, player, currencyId, 0, amount));

                //KWai
                ScriptLoader.getInstance().consumerScript("FeedbackScript",
                        (IFeedbackScript script) -> script.sendKWaiFeedback(player, currencyId, 1, amount));
                ScriptLoader.getInstance().consumerScript("FeedbackScript",
                        (IFeedbackScript script) -> script.sendKWaiFeedback(player, currencyId, 0, amount));

                ScriptLoader.getInstance().consumerScript("FirstDepositInviteBonusScript",
                        (IActivityScript script) -> script.execute(player, currencyId, 0, amount));
            } else {
                ScriptLoader.getInstance().consumerScript("FeedbackScript",
                        (IFeedbackScript script) -> script.sendFbFeedback(fbInfo, player, currencyId, 0, amount));

                //KWai
                ScriptLoader.getInstance().consumerScript("FeedbackScript",
                        (IFeedbackScript script) -> script.sendKWaiFeedback(player, currencyId, 0, amount));
            }

            if (rechargeTimes != 0 && rechargeTimes < 4 && extra > 0) {
                rewardRequest.reset();
                rewardRequest.addCurrency(currencyId, extra);
                final RewardReason fistRecharge = RewardReason.Activity_Recharge;
                fistRecharge.setSource(rechargeTimes + "");
                CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getBonusCurrencyMap(), fistRecharge);

                final BonusInfo bonusInfo = player.getBonusInfo();
                final BonusDetailsInfo bonusDetailsInfo = bonusInfo.getBonusDetailsInfo(BonusDetail.DepositBonus.getType());
                bonusDetailsInfo.incBonus(currencyId, extra);
                EntityDaoMrg.getInstance().getDao(PlayerDao.class).bonusInfoDao
                        .updateBonusDetails(player, BonusDetail.DepositBonus.getType(), bonusDetailsInfo, IntLists.singleton(currencyId));

                TransactionFrom transaction = null;
                if (rechargeTimes == 1) {
                    transaction = TransactionFrom.Promotion_FirstDeposit;
                } else if (rechargeTimes == 2) {
                    transaction = TransactionFrom.Promotion_SecondDeposit;
                } else if (rechargeTimes == 3) {
                    transaction = TransactionFrom.Promotion_ThirdDeposit;
                }
                TransactionFrom finalTransaction = transaction;
                ScriptLoader.getInstance().consumerScript("BonusScript",
                        (IBonusScript script) -> script.addBonusNote(finalTransaction, player, currencyId, extra));
            }

            //TODO pwa奖励
            ScriptLoader.getInstance().consumerScript("ChannelRewardScript",
                    (IChannelRewardScript script) -> script.receivePwaReward(player, currencyId));

            //TODO 更新任务
            final DefaultBlackboard defaultBlackboard = new DefaultBlackboard();
            defaultBlackboard.set(AttributeKey.valueOf("currencyId"), currencyId);
            defaultBlackboard.set(AttributeKey.valueOf("rechargeAmount"), amount);
            QuestMrg.getInstance().updateGoal(player, QuestGoalType.Recharge, defaultBlackboard);
            QuestMrg.getInstance().updateGoal(player, QuestGoalType.SingleRecharge, defaultBlackboard);

            //TODO vip
            ScriptLoader.getInstance().consumerScript("VipClubScript", (IVipClubScript script) ->
                    script.addRecharge(player, currencyId, amount));

            //TODO 推广
            ScriptLoader.getInstance().consumerScript("PromoteScript", (IPromoteScript script) ->
                    script.invitedRewards(player, currencyId, 0, amount));

            //TODO 邮件通知
            ScriptLoader.getInstance().consumerScript("InboxScript",
                    (IInboxScript script) -> script.sendInboxMail(player, InboxMrg.DEPOSIT_SUCCESS, Collections.singletonList(player.getPlayerName())));
            ScriptLoader.getInstance().consumerScript("InboxScript",
                    (IInboxScript script) -> script.sendEventPubMail(player, InboxMrg.RECHARGE, true));
            ScriptLoader.getInstance().consumerScript("ContinuousDepositScript",
                    (IActivityScript script) -> script.execute(player, currencyId, 0, amount));

            //TODO 活动
            ScriptLoader.getInstance().consumerScript("DepositScript",
                    (IActivityScript script) -> script.execute(player, currencyId, amount));
            ScriptLoader.getInstance().consumerScript("RankScript",
                    (IActivityScript script) -> script.execute(player, ActivityMrg.RANK_RECHARGE, currencyId, 0, amount));
            ScriptLoader.getInstance().consumerScript("RankScript",
                    (IActivityScript script) -> script.execute(player, currencyId, 0, amount));
            ScriptLoader.getInstance().consumerScript("FirstChargeSignInScript",
                    (IActivityScript script) -> script.execute(player, currencyId, 0, amount));

            //TODO 转盘
            ActivityMrg.luckSpinExecute(player, currencyId, 0, amount);

            ScriptLoader.getInstance().consumerScript("RewardBoxScript",
                    (IActivityScript script) -> script.execute(player, currencyId, 0, amount));
//            ScriptLoader.getInstance().consumerScript("RedEnvelopeRainScript",
//                    (IActivityScript script) -> script.execute(player, currencyId, 0, amount));
            ScriptLoader.getInstance().consumerScript("MysteryBonusScript",
                    (IActivityScript script) -> script.execute(player, currencyId, 0, amount));
            ScriptLoader.getInstance().consumerScript("PiggyBankScript",
                    (IActivityScript script) -> script.execute(player, currencyId, 0, amount));
        } catch (Exception e) {
            LOGGER.error("HallInnerNotifyRechargeHandler", e);
        } finally {
            if (!player.isOnline()) {
                EntityDaoMrg.getInstance().getDao(PlayerDao.class).updateTurnoverRecord(player);
                EntityDaoMrg.getInstance().getDao(PlayerDao.class).updateCurrency(player);
                EntityDaoMrg.getInstance().getDao(PlayerDao.class).updateActivity(player, player.getActivityInfo());
                EntityDaoMrg.getInstance().getDao(PlayerDao.class).updateLuckSpinInfo(player.getPlayerId(), player.getLuckSpinInfo());
                EntityDaoMrg.getInstance().getDao(PlayerDao.class).updateVipClubInfo(player.getPlayerId(), player.getVipClub());
                EntityDaoMrg.getInstance().getDao(PlayerDao.class).updateRechargeRecoverInfo(player.getPlayerId(), player.getRechargeRecoverInfo());
                if (player.getWithdrawStandardMap().containsKey(currencyId)) {
                    EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                            .updateWithdrawStandard(player, IntLists.singleton(currencyId));
                }
            }
        }
    }

    private void playerTurnoverRecordLog(Player player, TurnoverReason turnoverReason, long uniqueId
            , int currencyId, double amount, double drawStandard, double bettingVolume) {
        final GameLog platform_playerTurnoverRecordLog = new GameLog("platform_playerTurnoverRecordLog");
        platform_playerTurnoverRecordLog.append("number", GuidGeneratorUtils.generateOrderId())
                .append("site", player.getWebSite())
                .append("business_no", player.getBusiness_no())
                .append("playerId", player.getPlayerId())
                .append("playerName", player.getPlayerName())
                .append("region", player.getRegisterRegion())
                .append("agentId", player.getAgentId())
                .append("channelId", player.getChannelId())
                .append("mediaId", player.getMediaId())
                .append("adId", player.getAdId())
                .append("turnoverReason", turnoverReason.getReason())//改变类型
                .append("orderId", uniqueId)
                .append("currencyId", currencyId)
                .append("amount", amount)
                .append("drawStandard", drawStandard)
                .append("bettingVolume", bettingVolume)
                .append("logTime", TimeUtil.currentTimeMillis());
        HallServer.getInstance().getLogProducerMrg().send(platform_playerTurnoverRecordLog);
    }
}
