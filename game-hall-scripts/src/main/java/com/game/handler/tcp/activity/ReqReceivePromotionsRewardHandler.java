package com.game.handler.tcp.activity;

import com.game.dao.ActivityLimitDao;
import com.game.c_entity.merchant.C_Activity;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.util.misc.Tuple2;
import com.game.engine.utils.MsgUtil;
import com.game.entity.ActivityLimit;
import com.game.entity.player.Player;
import com.game.entity.player.activity.ActivityData;
import com.game.entity.player.activity.ActivityInfo;
import com.game.enums.ActivityType;
import com.game.enums.ErrorCode;
import com.game.enums.TransactionFrom;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.currency.RewardRequest;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.IBonusScript;
import com.game.hall.script.activity.IActivityScript;
import com.game.manager.EntityDaoMrg;
import com.proto.ActivityMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;

@IHandlerEntity(mid = MIDMessage.MID.ReqReceivePromotionsReward_VALUE, msg = ActivityMessage.ReqReceivePromotionsRewardMessage.class)
public class ReqReceivePromotionsRewardHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqReceivePromotionsRewardHandler.class);

    @Override
    public void run() {
        final ActivityMessage.ResReceivePromotionsRewardMessage.Builder res = ActivityMessage.ResReceivePromotionsRewardMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResReceivePromotionsReward_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final ActivityMessage.ReqReceivePromotionsRewardMessage req = (ActivityMessage.ReqReceivePromotionsRewardMessage) getMessage();
            final int cId = req.getCId();

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final C_Activity c_activity = merchantData.findByIdC_Activity(this.getClass().getSimpleName(), cId);
            if (c_activity == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            if (c_activity.getActivityId() == ActivityMrg.COMPENSATE) {
                ActivityLimit activityLimit = EntityDaoMrg.getInstance().getDao(ActivityLimitDao.class).findByIp(player.getBusiness_no(), player.getIp(), c_activity.getActivityId(), c_activity.getC_id());
                if (activityLimit == null) {
                    activityLimit = new ActivityLimit();
                    activityLimit.setBusiness_no(player.getBusiness_no());
                    activityLimit.setActivityId(c_activity.getActivityId());
                    activityLimit.setUniqueId(c_activity.getC_id());
                    activityLimit.setIpAddress(player.getIp());
                    EntityDaoMrg.getInstance().getDao(ActivityLimitDao.class).insert(activityLimit);
                }

                final C_Activity.ConditionInfo conditionInfo = c_activity.findCondition(player.getCurrencyId());
                if (conditionInfo != null && activityLimit.getCount() >= conditionInfo.getReceiveTimes()) {
                    res.setError(ErrorCode.Receive_Limit.getCode());
                    MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                    return;
                }
            }

            final ActivityInfo activityInfo = player.getActivityInfo();
            int activityId = Integer.parseInt(c_activity.getActivityId() + "" + c_activity.getActivitySubType());
            if (c_activity.getActivityId() == ActivityMrg.WAGERED) {
                final C_Activity.WageredCondition condition = c_activity.getWageredCondition();
                final String gameType = condition.getGameType() == 0 ? "" : condition.getGameType() + "";
//                final String platformId = condition.getPlatformId() == 0 ? "" : condition.getPlatformId() + "";
                activityId = Integer.parseInt(c_activity.getActivityId() + gameType);
            }
            final ActivityData activityData = activityInfo.getActivityData(activityId);

            RewardRequest rewardRequest = null;
            final String nameScript = Objects.requireNonNull(ActivityType.valueOf(c_activity.getActivityType())).getName();
            if (activityData.isStart() && c_activity.isNowReceive()) {
                rewardRequest = ScriptLoader.getInstance().functionScript(nameScript,
                        (IActivityScript script) -> script.receiveReward(player, c_activity));
            } else if (activityData.isStart()) {
                rewardRequest = ScriptLoader.getInstance().functionScript(nameScript,
                        (IActivityScript script) -> script.receiveSettlementReward(player, c_activity));
            }

            if (rewardRequest != null && !rewardRequest.getCurrencyMap().isEmpty()) {
                rewardRequest.writeToItemShowMsg(res::addReward);

                final RewardRequest finalRewardRequest = rewardRequest;
                ScriptLoader.getInstance().consumerScript("ActivityScript",
                        (IActivityScript script) -> script.sendActivityLog(player, 2, c_activity, finalRewardRequest));

                final Tuple2<Integer, Double> tuple2 = rewardRequest.getReward();
                ScriptLoader.getInstance().consumerScript("BonusScript", (IBonusScript script) ->
                        script.addBonusNote(switchTransaction(c_activity), player, tuple2.getFirst(), tuple2.getSecond()));

                EntityDaoMrg.getInstance().getDao(ActivityLimitDao.class)
                        .updateActivityLimit(player.getBusiness_no(), player.getIp(), c_activity.getActivityId(), c_activity.getC_id());
            }

            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqReceivePromotionsRewardHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    private TransactionFrom switchTransaction(C_Activity c_activity) {
        TransactionFrom transaction = null;
        switch (c_activity.getActivityId()) {
            case ActivityMrg.DEPOSIT:
                if (c_activity.getActivitySubType() == 1) {
                    transaction = TransactionFrom.Promotion_AccumulatedDeposit;
                } else {
                    transaction = TransactionFrom.Promotion_SingleDeposit;
                }
                break;

            case ActivityMrg.WAGERED:
                transaction = TransactionFrom.Promotion_WageringEvent;
                break;

            case ActivityMrg.FREE_GIVE:
                if (c_activity.getActivitySubType() == 1) {//注册
                    transaction = TransactionFrom.Promotion_SignUpGift;
                } else if (c_activity.getActivitySubType() == 2) {//登录
                    transaction = TransactionFrom.Promotion_LoginGift;
                } else {//回归
                    transaction = TransactionFrom.Promotion_ReturnGift;
                }
                break;

            case ActivityMrg.COMPENSATE:
                if (c_activity.getActivitySubType() == 1) {//每日
                    transaction = TransactionFrom.Promotion_DailyRelief;
                } else {
                    transaction = TransactionFrom.Promotion_WeeklyRelief;
                }
                break;

            case ActivityMrg.RANK:
                if (c_activity.getActivitySubType() == ActivityMrg.RANK_RECHARGE) {
                    transaction = TransactionFrom.Promotion_DepositRank;
                } else if (c_activity.getActivitySubType() == ActivityMrg.RANK_WAGERED) {
                    transaction = TransactionFrom.Promotion_WageringRank;
                } else if (c_activity.getActivitySubType() == ActivityMrg.RANK_WIN) {
                    transaction = TransactionFrom.Promotion_WinningRank;
                } else if (c_activity.getActivitySubType() == ActivityMrg.RANK_MULTIPLE) {
                    transaction = TransactionFrom.Promotion_MultiplierRank;
                } else if (c_activity.getActivitySubType() == ActivityMrg.RANK_AFFILIATE) {
                    transaction = TransactionFrom.Promotion_InvitationRank;
                }
                break;
            case ActivityMrg.FIRSTDEPOSIT_GIVEUP:
                transaction = TransactionFrom.FirstDepositGiveUp;
                break;
            default:
                transaction = TransactionFrom.None;
                break;
        }
        return transaction;
    }
}
