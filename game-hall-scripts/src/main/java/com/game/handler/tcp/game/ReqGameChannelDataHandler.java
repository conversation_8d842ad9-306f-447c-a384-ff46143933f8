package com.game.handler.tcp.game;

import com.game.c_entity.merchant.C_GameChannel;
import com.game.c_entity.middleplatform.C_BaseMerchant;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.proto.HallMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

@IHandlerEntity(mid = MIDMessage.MID.ReqGameChannelData_VALUE, msg = HallMessage.ReqGameChannelDataMessage.class)
public class ReqGameChannelDataHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqGameChannelDataHandler.class);

    @Override
    public void run() {
        final HallMessage.ResGameChannelDataMessage.Builder res = HallMessage.ResGameChannelDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResGameChannelData_VALUE);
        try {
            final HallMessage.ReqGameChannelDataMessage req = (HallMessage.ReqGameChannelDataMessage) message;
            final String host = req.getHost();
            final int language = req.getLanguage();

            final C_BaseMerchant c_baseHostMerchant = DataHallMrg.getInstance().findC_BaseHostMerchant(this.getClass().getSimpleName(), host);
            if (c_baseHostMerchant == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final String business_no = c_baseHostMerchant.getBusiness_no();

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), business_no);
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final Map<Integer, List<C_GameChannel>> c_gameChannelMap = merchantData.getC_gameChannelListMap();
            if (c_gameChannelMap == null) {
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final HallMessage.GameChannelInfo.Builder gameChannelBuild = HallMessage.GameChannelInfo.newBuilder();
            for (Map.Entry<Integer, List<C_GameChannel>> entry : c_gameChannelMap.entrySet()) {
                final int gameChannel = entry.getKey();
                final C_GameChannel c_gameChannel = merchantData.findC_GameChannel(this.getClass().getSimpleName(), gameChannel);
                if (c_gameChannel == null) {
                    continue;
                }
                final List<C_GameChannel> c_gameSubChannelList = entry.getValue();
                gameChannelBuild.clear();
                gameChannelBuild.setChannel(c_gameChannel.getChannelId())
                        .setChannelType(c_gameChannel.getChannelType())
                        .setChannelIcon(c_gameChannel.getChannelIcon())
                        .setChannelIcon1(c_gameChannel.getChannelIcon1())
                        .setChannelSort(c_gameChannel.getChannelSort());
                final C_GameChannel.DescInfo descInfo = c_gameChannel.getDescInfoMap().get(language);
                if (descInfo != null) {
                    gameChannelBuild.setChannelName(descInfo.getChannelName());
                }
                for (C_GameChannel c_gameSubChannel : c_gameSubChannelList) {
                    gameChannelBuild.addGameSubChannelList(buildGameSubChannel(language, c_gameSubChannel));
                }
                res.addGameChannelInfo(gameChannelBuild.build());
            }

            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqGameChannelDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    /**
     * * int32 subChannel           = 6; //子频道
     * * string subChannelName      = 7; //子频道名字
     * * string subChannelIcon      = 8; //子频道图标
     * * string subChannelIcon1     = 9; //子频道图标1
     * * int32 subChannelSort       =10; //子频道排序
     * * int32 channelSort          =11; //频道排序
     * * int32 subChannelType       =12; //1.厂商 2.游戏 3.最近 4.收藏
     * * bool menuShow              =13; //是否显示菜单界面
     * * bool homeShow              =14; //是否显示首页
     * * bool channelHomeShow       =15; //是否显示频道首页
     * * int32 homeSort             =16; //首页排序
     * * int32 channelHomeSort      =17; //频道首页排序
     * * string channelHomeIcon     =18; //频道首页图标
     * * string channelHomeIcon1    =19; //频道首页图标1
     * s
     *
     * @param c_gameChannel
     * @return
     */
    private HallMessage.GameSubChannelInfo buildGameSubChannel(int language, C_GameChannel c_gameChannel) {
        final HallMessage.GameSubChannelInfo.Builder gameSubChannelInfo = HallMessage.GameSubChannelInfo.newBuilder();
        gameSubChannelInfo.setSubChannel(c_gameChannel.getSubChannel())
                .setSubChannelIcon(c_gameChannel.getSubChannelIcon())
                .setSubChannelIcon1(c_gameChannel.getSubChannelIcon1())
                .setSubChannelSort(c_gameChannel.getSubChannelSort())
                .setSubChannelType(c_gameChannel.getSubChannelType())
                .setSubChannelInner(c_gameChannel.getSubChannelInner())
                .setMenuShow(c_gameChannel.isMenuShow())
                .setHomeShow(c_gameChannel.isHomeContextShow())
                .setHomeContextShow(c_gameChannel.isHomeContextShow())
                .setHomeContextSort(c_gameChannel.getHomeContextSort())
                .setHomeMenuShow(c_gameChannel.isHomeMenuShow())
                .setHomeMenuSort(c_gameChannel.getHomeMenuSort())
                .setChannelHomeShow(c_gameChannel.isChannelHomeShow())
                .setChannelHomeSort(c_gameChannel.getChannelHomeSort())
                .setChannelHomeIcon(c_gameChannel.getChannelHomeIcon())
                .setChannelHomeIcon1(c_gameChannel.getChannelHomeIcon1());
        final C_GameChannel.DescInfo descInfo = c_gameChannel.getDescInfoMap().get(language);
        if (descInfo != null) {
            gameSubChannelInfo.setSubChannelName(descInfo.getSubChannelName());
        }
        return gameSubChannelInfo.build();
    }

}
