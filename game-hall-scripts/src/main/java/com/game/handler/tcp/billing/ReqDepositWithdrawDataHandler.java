package com.game.handler.tcp.billing;

import com.game.c_entity.merchant.C_PlatformRecharge;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.player.PlayerMrg;
import com.proto.BillingMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

@IHandlerEntity(mid = MIDMessage.MID.ReqDepositWithdrawData_VALUE, msg = BillingMessage.ReqDepositWithdrawDataMessage.class)
public class ReqDepositWithdrawDataHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqDepositWithdrawDataHandler.class);

    @Override
    public void run() {
        final BillingMessage.ResDepositWithdrawDataMessage.Builder res = BillingMessage.ResDepositWithdrawDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResDepositWithdrawData_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final Map<Integer, Map<String, C_PlatformRecharge>> c_platformRechargeFiatMap = merchantData.getC_platformRecharge_fiatMap();
            for (final Map.Entry<Integer, Map<String, C_PlatformRecharge>> entry : c_platformRechargeFiatMap.entrySet()) {
                final int currencyId = entry.getKey();
                res.addDepositWithdrawData(buildDepositWithdrawData(currencyId, new ArrayList<>()));
            }

            final Map<Integer, List<C_PlatformRecharge>> c_platformRechargeCryptoMap = merchantData.getC_platformRecharge_cryptoMap();
            for (final Map.Entry<Integer, List<C_PlatformRecharge>> entry : c_platformRechargeCryptoMap.entrySet()) {
                final int currencyId = entry.getKey();
                final List<C_PlatformRecharge> rechargeMap = entry.getValue();
                res.addDepositWithdrawData(buildDepositWithdrawData(currencyId, rechargeMap));
            }

            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqDepositWithdrawDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    private BillingMessage.DepositWithdrawData buildDepositWithdrawData(int currencyId, List<C_PlatformRecharge> rechargeMap) {
        final BillingMessage.DepositWithdrawData.Builder networkInfo = BillingMessage.DepositWithdrawData.newBuilder();
        networkInfo.setCurrencyId(currencyId);
        for (final C_PlatformRecharge recharge : rechargeMap) {
            networkInfo.addNetworkList(buildNetworkInfo(recharge.getNetwork(), recharge.getNetworkShow()));
        }
        return networkInfo.build();
    }

    private BillingMessage.NetworkInfo buildNetworkInfo(String network, String remark) {
        return BillingMessage.NetworkInfo.newBuilder()
                .setNetwork(network)
                .setRemark(remark)
                .build();
    }
}
