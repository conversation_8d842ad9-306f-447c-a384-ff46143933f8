package com.game.handler.tcp.affiliate;

import com.game.c_entity.merchant.C_CashBack;
import com.game.dao.player.PlayerPromoteDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.mongo.DBConnectionMrg;
import com.game.engine.mongo.MongoConverterMrg;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.util.misc.Tuple2;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.Player;
import com.game.entity.player.promote.*;
import com.game.enums.ErrorCode;
import com.game.enums.RewardReason;
import com.game.enums.TransactionFrom;
import com.game.enums.TurnoverReason;
import com.game.hall.mrg.CommonMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.mrg.currency.RewardRequest;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.IBonusScript;
import com.game.hall.script.IPlayerScript;
import com.game.manager.EntityDaoMrg;
import com.proto.AffiliateMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Update;

import java.util.Optional;

@IHandlerEntity(mid = MIDMessage.MID.ReqWithdrawToWallet_VALUE, msg = AffiliateMessage.ReqWithdrawToWalletMessage.class)
public class ReqWithdrawToWalletHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqWithdrawToWalletHandler.class);

    @Override
    public void run() {
        final AffiliateMessage.ResWithdrawToWalletMessage.Builder res = AffiliateMessage.ResWithdrawToWalletMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResWithdrawToWallet_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final AffiliateMessage.ReqWithdrawToWalletMessage req = (AffiliateMessage.ReqWithdrawToWalletMessage) getMessage();
            final int currencyId = req.getCurrencyId();

//            LOGGER.warn("playerId：{}，currencyId：{}", player.getPlayerId(), currencyId);

            final PlayerPromote playerPromote = player.getPlayerPromote();
            final RewardRequest rewardRequest = new RewardRequest();
            final RewardRequest withdrawStandard = new RewardRequest();

            //推广奖励
            final ReferralRewards availableRewards = playerPromote.getReferralRewardsMap().get(currencyId);
            if (availableRewards != null) {
                rewardRequest.addCurrency(currencyId, availableRewards.getAvailable());
                ScriptLoader.getInstance().consumerScript("BonusScript",
                        (IBonusScript script) -> script.addBonusNote(TransactionFrom.Affiliate_ReferralRewards, player, currencyId, availableRewards.getAvailable()));

                final String referralTurnoverMul = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "referralTurnoverMul");
                final int turnoverMul = StringUtil.isNullOrEmpty(referralTurnoverMul) ? 5 : Integer.parseInt(referralTurnoverMul);
                withdrawStandard.addCurrency(currencyId, BigDecimalUtils.mul(availableRewards.getAvailable(), turnoverMul, 9));

                final TurnoverReason turnoverReason = TurnoverReason.Withdraw_ToWallet;
                turnoverReason.setSource("referralRewards");
                ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                        (IPlayerScript script) -> script.drawStandard(player, turnoverReason, currencyId, availableRewards.getAvailable(),
                                BigDecimalUtils.mul(availableRewards.getAvailable(), turnoverMul, 9)));
            }


            final Optional<C_CashBack> optional = merchantData.getC_cashBackMap().values().stream().findFirst();

            //上级返水
            final CommissionRewards commissionRewards = playerPromote.getCommissionRewardsMap().get(currencyId);
            if (commissionRewards != null) {
                rewardRequest.addCurrency(currencyId, commissionRewards.getAvailable());
                ScriptLoader.getInstance().consumerScript("BonusScript",
                        (IBonusScript script) -> script.addBonusNote(TransactionFrom.Affiliate_CommissionRewards, player, currencyId, commissionRewards.getAvailable()));

                if (optional.isPresent()) {
                    final C_CashBack c_cashBack = optional.get();
                    final TurnoverReason turnoverReason = TurnoverReason.Withdraw_ToWallet;
                    turnoverReason.setSource("1st-Lvl Rewards");
                    ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                            (IPlayerScript script) -> script.drawStandard(player, turnoverReason, currencyId, commissionRewards.getAvailable(),
                                    BigDecimalUtils.mul(commissionRewards.getAvailable(), c_cashBack.getTurnoverMul(), 9)));
                }
            }

            //团长返水
            final TeamRewards teamRewards = playerPromote.getTeamRewardsMap().get(currencyId);
            if (teamRewards != null) {
                rewardRequest.addCurrency(currencyId, teamRewards.getAvailable());
                ScriptLoader.getInstance().consumerScript("BonusScript",
                        (IBonusScript script) -> script.addBonusNote(TransactionFrom.Affiliate_TeamRewards, player, currencyId, teamRewards.getAvailable()));

                if (optional.isPresent()) {
                    final C_CashBack c_cashBack = optional.get();
                    final TurnoverReason turnoverReason = TurnoverReason.Withdraw_ToWallet;
                    turnoverReason.setSource("2nd-Lvl Rewards");
                    ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                            (IPlayerScript script) -> script.drawStandard(player, turnoverReason, currencyId, teamRewards.getAvailable(),
                                    BigDecimalUtils.mul(teamRewards.getAvailable(), c_cashBack.getTurnoverMul(), 9)));
                }
            }

            //三级返水
            final ThreeLevelRewards threeLevelRewards = playerPromote.getThreeLevelRewardsMap().get(currencyId);
            if (threeLevelRewards != null) {
                rewardRequest.addCurrency(currencyId, threeLevelRewards.getAvailable());
                ScriptLoader.getInstance().consumerScript("BonusScript",
                        (IBonusScript script) -> script.addBonusNote(TransactionFrom.Affiliate_ThreeLevelRewards, player,
                                currencyId, threeLevelRewards.getAvailable()));

                if (optional.isPresent()) {
                    final C_CashBack c_cashBack = optional.get();
                    final TurnoverReason turnoverReason = TurnoverReason.Withdraw_ToWallet;
                    turnoverReason.setSource("3rd-Lvl Rewards");
                    ScriptLoader.getInstance().consumerScript("PlayerWithdrawStandardScript",
                            (IPlayerScript script) -> script.drawStandard(player, turnoverReason, currencyId, threeLevelRewards.getAvailable(),
                                    BigDecimalUtils.mul(threeLevelRewards.getAvailable(), c_cashBack.getTurnoverMul(), 9)));
                }
            }

            if (!rewardRequest.getCurrencyMap().isEmpty()) {
                final Tuple2<Integer, Double> tuple2 = rewardRequest.getReward();
                res.setRewardShow(CommonMrg.buildDItemShow(tuple2.getFirst(), tuple2.getSecond()));
            }
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
            CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getBonusCurrencyMap(), RewardReason.Withdraw_ToWallet);

            if (availableRewards != null) {
                availableRewards.reset();
            }
            if (commissionRewards != null) {
                commissionRewards.reset();
            }
            if (teamRewards != null) {
                teamRewards.reset();
            }
            if (threeLevelRewards != null) {
                threeLevelRewards.reset();
            }

            final MongoConverterMrg converterMrg = DBConnectionMrg.getInstance().getConvertMrg();
            final Update update = new Update();
            update.set(PlayerPromoteFields.referralRewardsMap, converterMrg.writeNoTypeKey(playerPromote.getReferralRewardsMap()))
                    .set(PlayerPromoteFields.commissionRewardsMap, converterMrg.writeNoTypeKey(playerPromote.getCommissionRewardsMap()))
                    .set(PlayerPromoteFields.teamRewardsMap, converterMrg.writeNoTypeKey(playerPromote.getTeamRewardsMap()))
                    .set(PlayerPromoteFields.threeLevelRewardsMap, converterMrg.writeNoTypeKey(playerPromote.getThreeLevelRewardsMap()));
            EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class)
                    .updatePromotion(player.getPlayerId(), update);
        } catch (Exception e) {
            LOGGER.error("ReqWithdrawToWalletHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }
}
