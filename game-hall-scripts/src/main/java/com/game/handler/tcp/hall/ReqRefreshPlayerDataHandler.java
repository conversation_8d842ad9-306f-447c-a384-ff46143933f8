package com.game.handler.tcp.hall;

import com.game.c_entity.merchant.C_Currency;
import com.game.c_entity.merchant.C_CurrencyTraderWithdraw;
import com.game.c_entity.merchant.C_VipClub;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.entity.account.AccountFields;
import com.game.entity.account.ThreePartyInfo;
import com.game.entity.player.Player;
import com.game.entity.player.stats.Stats;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.redis.RedisUtils;
import com.proto.CommonMessage;
import com.proto.HallMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@IHandlerEntity(mid = MIDMessage.MID.ReqRefreshPlayerData_VALUE, msg = HallMessage.ReqRefreshPlayerDataMessage.class)
public class ReqRefreshPlayerDataHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqRefreshPlayerDataHandler.class);

    @Override
    public void run() {
        final HallMessage.ResRefreshPlayerDataMessage.Builder res = HallMessage.ResRefreshPlayerDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResRefreshPlayerData_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            res.setPlayerInfo(builderPlayerInfo(player));
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqRefreshPlayerDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    private CommonMessage.PlayerInfo builderPlayerInfo(Player player) {
        final Stats stats = player.getStats(player.getCurrencyId());
        final CommonMessage.PlayerInfo.Builder playerInfo = CommonMessage.PlayerInfo.newBuilder();
        playerInfo.setPlayerId(player.getPlayerId())
                .setBusinessNo(player.getBusiness_no())
                .setPlayerName(player.getPlayerName())
                .setHeadId(player.getHeadId())
                .setCreateTime(player.getCreateTime())
                .setRegion(player.getRegion())
                .setCurrencyId(player.getCurrencyId())
                .setViewFiat(player.getViewFiat())
                .setVipLevel(player.getVipClub().getVipLevel())
                .setCurVipExp(player.getVipClub().getCurExp())
                .setReferralCode(StringUtil.isNullOrEmpty(player.getInvitationCode()) ? "" : player.getInvitationCode())
                .setHideName(player.isHideName())
                .setLanguage(player.getLanguage())
                .setBasicVerification(player.getBasicVerification())
                .setAdvancedVerification(player.getAdvancedVerification())
                .setRechargeTimes(player.getTotalRechargeTimes())
                .setAgentId(player.getAgentId())
                .setChannelId(player.getChannelId())
                .setEmail(player.getEmail())
                .setAccount(player.getAccount())
                .setAreaCode(player.getAreaCode())
                .setPhone(player.getPhone())
                .setRechargeAmount(stats.getTotalRechargeAmount())
                .setCurVipRecharge(player.getVipClub().getCurRecharge())
                .setRechargeBonus(player.isRechargeBonus())
                .setReceivePwa(player.isReceivePwa())
                .addAllChannels(player.getChannels())
                .setDailyRechargeTimes(stats.getDailyRechargeTimes())
                .setWeeklyRechargeTimes(stats.getWeeklyRechargeTimes())
                .setLoginTime(player.getLoginTime())
                .setLogoutTime(player.getLogoutTime());
        final String loginTimes = RedisUtils.getPlayerInfo(player.getPlayerId(), AccountFields.loginTimes);
        if (!StringUtil.isNullOrEmpty(loginTimes)) {
            playerInfo.setLoginTimes(Integer.parseInt(loginTimes));
        }
        if (player.isEmailBind()) {
            playerInfo.addAccountBind(1);//邮件
        }
        if (player.isPhoneBind()) {
            playerInfo.addAccountBind(6);//电话
        }
        if (!player.getThreePartyInfoMap().isEmpty()) {
            playerInfo.addAllAccountBind(player.getThreePartyInfoMap().keySet());
            for (ThreePartyInfo threePartyInfo : player.getThreePartyInfoMap().values()) {
                playerInfo.addThreePartyList(buildThreePartyInfo(threePartyInfo));
            }
        }
        final long superiorId = player.getPlayerPromote().getSuperiorId();
        if (superiorId != 0) {
            playerInfo.setSuperiorId(superiorId + "");
        }
        if (player.isVerify2FA()) {
            playerInfo.setEnable2FA(true);
        }

        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData != null) {
            for (C_Currency c_currency : merchantData.getC_currencyMap().values()) {
                final double amount = player.getCurrencyMap().getOrDefault(c_currency.getCurrencyId(), 0d);
                final double bonus = player.getCurrencyMap().getOrDefault(c_currency.getCurrencyId() * 10, 0d);
                playerInfo.addCItem(CurrencyMrg.getInstance().buildCurrency(c_currency.getCurrencyId(), amount, bonus, c_currency));
            }
            final String referralLink = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "referralLink");
            final String url = referralLink + "?" + "referralCode=" + player.getInvitationCode();
            playerInfo.setReferralLink(url);

            final C_VipClub c_vipClub = merchantData.findC_VipClub(this.getClass().getSimpleName(), player.getVipClub().getVipLevel());
            if (c_vipClub != null) {
                playerInfo.setNeedVipExp(c_vipClub.getNeedExp());
            }
        }

        if (merchantData != null) {
            final String tgShareTimesStr = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "tgShareTimes");
            final int tgShareTimes = StringUtil.isNullOrEmpty(tgShareTimesStr) ? 0 : Integer.parseInt(tgShareTimesStr);
            playerInfo.setTgShareTimes(tgShareTimes - player.getTgShareTimes());
            if (playerInfo.getTgShareTimes() <= 0) {
                playerInfo.setTgShareTimes(0);
            }
        }

        final int agentId = player.getAgentId();
        if (agentId != 0 && merchantData != null) {
            final C_CurrencyTraderWithdraw c_currencyTraderWithdraw = merchantData.findC_CurrencyTraderWithdraw(this.getClass().getSimpleName(), agentId);
            if (c_currencyTraderWithdraw != null) {
                playerInfo.setCurrencyTraderData(buildCurrencyTraderData(c_currencyTraderWithdraw));
            }
        }
        return playerInfo.build();
    }

    private CommonMessage.ThreePartyInfo buildThreePartyInfo(ThreePartyInfo threePartyInfo) {
        return CommonMessage.ThreePartyInfo.newBuilder()
                .setThreePartyId(threePartyInfo.getThreePartyId())
                .setAccount(threePartyInfo.getAccount())
                .setThreeParty(threePartyInfo.getThreeParty())
                .build();
    }

    private CommonMessage.CurrencyTraderData buildCurrencyTraderData(C_CurrencyTraderWithdraw c_currencyTraderWithdraw) {
        return CommonMessage.CurrencyTraderData.newBuilder()
                .setHead(c_currencyTraderWithdraw.getHead())
                .setName(c_currencyTraderWithdraw.getName())
                .setMediaLink(c_currencyTraderWithdraw.getMediaLink())
                .setContactDetails(c_currencyTraderWithdraw.getContactDetails())
                .setMediaIcon(c_currencyTraderWithdraw.getMediaIcon())
                .build();
    }
}
