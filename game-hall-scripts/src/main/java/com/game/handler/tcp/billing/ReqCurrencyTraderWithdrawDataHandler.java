package com.game.handler.tcp.billing;

import com.game.c_entity.merchant.C_CurrencyTraderWithdraw;
import com.game.c_entity.merchant.C_PlatformWithdraw;
import com.game.c_entity.merchant.C_RechargeWithdrawLimit;
import com.game.c_entity.middleplatform.C_BasePaymentMethod;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.util.misc.Tuple2;
import com.game.engine.utils.MsgUtil;
import com.game.entity.player.PayInfo;
import com.game.entity.player.Player;
import com.game.entity.player.WithdrawAccount;
import com.game.entity.player.WithdrawStandard;
import com.game.enums.Currency;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.IBillingScript;
import com.proto.BillingMessage;
import com.proto.MIDMessage;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

@IHandlerEntity(mid = MIDMessage.MID.ReqCurrencyTraderWithdrawData_VALUE, msg = BillingMessage.ReqCurrencyTraderWithdrawDataMessage.class)
public class ReqCurrencyTraderWithdrawDataHandler extends TcpHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(ReqCurrencyTraderWithdrawDataHandler.class);

    @Override
    public void run() {
        final BillingMessage.ResCurrencyTraderWithdrawDataMessage.Builder res = BillingMessage.ResCurrencyTraderWithdrawDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResCurrencyTraderWithdrawData_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final BillingMessage.ReqCurrencyTraderWithdrawDataMessage req = (BillingMessage.ReqCurrencyTraderWithdrawDataMessage) getMessage();
            final int currencyId = req.getCurrencyId();
            if (currencyId == 0) {
                LOGGER.error("playerId：{}，currencyId is 0", player.getPlayerId());
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final C_RechargeWithdrawLimit c_limit = merchantData.findC_RechargeWithdrawLimit(this.getClass().getSimpleName(), currencyId);
            if (c_limit == null) {
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final Tuple2<Double, Double> tuple2 = ScriptLoader.getInstance().functionScript("BillingScript",
                    (IBillingScript script) -> script.calculateAvailableAmount(player, currencyId, c_limit.getWithdrawType()));
            final double available = tuple2.getFirst();
            final double lockedFunds = tuple2.getSecond();

            final Map<Integer, C_CurrencyTraderWithdraw> c_currencyTraderWithdrawMap = merchantData.getC_currencyTraderWithdrawMap();
            if (c_currencyTraderWithdrawMap.isEmpty()) {
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            for (final Map.Entry<Integer, C_CurrencyTraderWithdraw> entry : c_currencyTraderWithdrawMap.entrySet()) {
                final C_CurrencyTraderWithdraw currencyTraderWithdraw = entry.getValue();
                if (currencyTraderWithdraw.getType() != 1) {
                    continue;
                }
                res.addWithdraw(buildCurrencyTraderWithdrawData(currencyTraderWithdraw));
            }

            switch (currencyId / 1000) {
                case 1://法币
                    final Map<String, C_PlatformWithdraw> c_platformWithdrawFiatMap = merchantData.findC_PlatformWithdrawFiat(this.getClass().getSimpleName(), currencyId);
                    if (c_platformWithdrawFiatMap == null || c_platformWithdrawFiatMap.isEmpty()) {
                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                        return;
                    }
                    for (final Map.Entry<String, C_PlatformWithdraw> entry : c_platformWithdrawFiatMap.entrySet()) {
                        final C_PlatformWithdraw c_platformWithdraw = entry.getValue();
                        res.addWithdrawFiatData(buildWithdrawFiatData(player, c_platformWithdraw));
                    }
                    break;
                case 2://加密
                    final List<C_PlatformWithdraw> c_platformWithdrawCryptoList = merchantData.findC_PlatformWithdrawCrypto(this.getClass().getSimpleName(), currencyId);
                    if (c_platformWithdrawCryptoList == null || c_platformWithdrawCryptoList.isEmpty()) {
                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                        return;
                    }
                    final String network = req.getNetwork();
                    final Optional<C_PlatformWithdraw> optional = c_platformWithdrawCryptoList.stream().filter(c_platform -> Objects.equals(c_platform.getNetwork(), network)).findFirst();
                    if (optional.isPresent()) {
                        C_PlatformWithdraw c_platformWithdraw = optional.get();
                        res.setWithdrawCryptoData(buildWithdrawCryptoData(player, c_platformWithdraw));
                    }
                    break;
            }

            final WithdrawStandard withdrawStandard = player.getWithdrawStandard(currencyId);
            double turnoverRate = 0;
            if (withdrawStandard.getDrawStandard() > 0) {
                turnoverRate = BigDecimalUtils.div(withdrawStandard.getBettingVolume(), withdrawStandard.getDrawStandard(), 4);
            }

            res.setCurrencyTraderId(player.getCurrencyTraderId())
                    .setAvailable(available)
                    .setLockedFunds(lockedFunds)
                    .setTurnoverRate(turnoverRate);
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqWithDrawDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    private BillingMessage.CurrencyTraderWithdrawData buildCurrencyTraderWithdrawData(C_CurrencyTraderWithdraw c_currencyTraderWithdraw) {
        final BillingMessage.CurrencyTraderWithdrawData.Builder currencyTraderWithdrawData = BillingMessage.CurrencyTraderWithdrawData.newBuilder()
                .setCurrencyTraderId(c_currencyTraderWithdraw.getCurrencyTraderId())
                .setMinimum(c_currencyTraderWithdraw.getSingleWithdrawLowerLimit())
                .setMaxMum(c_currencyTraderWithdraw.getSingleWithdrawUpperLimit())
                .setWithdrawalFeeRate(c_currencyTraderWithdraw.getWithdrawFeeRate())
                .setHead(c_currencyTraderWithdraw.getHead())
                .setName(c_currencyTraderWithdraw.getName())
                .setMediaLink(c_currencyTraderWithdraw.getMediaLink())
                .setMediaIcon(c_currencyTraderWithdraw.getMediaIcon())
                .setContactDetails(c_currencyTraderWithdraw.getContactDetails())
                .setWithdrawTimes(c_currencyTraderWithdraw.getWithdrawTimes());
        return currencyTraderWithdrawData.build();
    }

    private BillingMessage.CurrencyTraderWithdrawFiatData buildWithdrawFiatData(Player player, C_PlatformWithdraw c_platformWithdraw) {
        final BillingMessage.CurrencyTraderWithdrawFiatData.Builder withdrawFiatData = BillingMessage.CurrencyTraderWithdrawFiatData.newBuilder()
                .setCurrencyId(c_platformWithdraw.getCurrencyId())
                .setPaymentMethod(c_platformWithdraw.getPaymentMethod());
        for (Map.Entry<String, C_PlatformWithdraw.WithdrawInfo> entry : c_platformWithdraw.getWithdrawInfoMap().entrySet()) {
            withdrawFiatData.addWithdrawInfos(buildWithdrawInfo(entry.getKey(), entry.getValue()));
        }
        final String currencyIdChannel = c_platformWithdraw.getCurrencyId() + "_" + c_platformWithdraw.getPaymentMethod();
        final WithdrawAccount withdrawAccount = player.getWithdrawAccountMap().get(currencyIdChannel);
        if (withdrawAccount != null) {
            if (c_platformWithdraw.getCurrencyId() == Currency.BRL.getCurrencyId()) {
                if (!withdrawAccount.getPayInfoMap().isEmpty()) {
                    final List<PayInfo> payInfos = new ArrayList<>(withdrawAccount.getPayInfoMap().values());
                    payInfos.sort(Comparator.comparingLong(PayInfo::getCreateTime));
                    final PayInfo payInfo = payInfos.getFirst();
                    withdrawFiatData.addWithdrawAccount(buildWithdrawAccount(payInfo));
                }
            } else {
                for (PayInfo payInfo : withdrawAccount.getPayInfoMap().values()) {
                    withdrawFiatData.addWithdrawAccount(buildWithdrawAccount(payInfo));
                }
            }
        }
        final C_BasePaymentMethod c_basePaymentMethod = DataHallMrg.getInstance().findC_BasePaymentMethod(this.getClass().getSimpleName(), c_platformWithdraw.getPaymentMethod());
        if (c_basePaymentMethod != null) {
            withdrawFiatData.setPaymentMethodName(c_basePaymentMethod.getName())
                    .setPaymentMethodLog(c_basePaymentMethod.getIcon());
        }
        return withdrawFiatData.build();
    }

    private BillingMessage.CurrencyTraderWithdrawCryptoData buildWithdrawCryptoData(Player player, C_PlatformWithdraw c_platformWithdraw) {
        final BillingMessage.CurrencyTraderWithdrawCryptoData.Builder withdrawCryptoData = BillingMessage.CurrencyTraderWithdrawCryptoData.newBuilder();
        final String currencyIdChannel = c_platformWithdraw.getCurrencyId() + "_" + c_platformWithdraw.getPaymentMethod();
        final WithdrawAccount withdrawAccount = player.getWithdrawAccountMap().get(currencyIdChannel);
        if (withdrawAccount != null) {
            for (PayInfo payInfo : withdrawAccount.getPayInfoMap().values()) {
                withdrawCryptoData.addWithdrawAccount(buildWithdrawAccount(payInfo));
            }
        }
        return withdrawCryptoData.build();
    }

    private BillingMessage.WithdrawAccount buildWithdrawAccount(PayInfo payInfo) {
        return BillingMessage.WithdrawAccount.newBuilder()
                .setWithdrawAccountId(payInfo.getPayId() + "")
                .setExtend0(payInfo.getExtend())
                .setExtend1(payInfo.getExtend_1())
                .setExtend2(payInfo.getExtend_2())
                .setExtend3(StringUtil.isNullOrEmpty(payInfo.getExtend_3()) ? "" : payInfo.getExtend_3())
                .build();
    }

    private BillingMessage.WithdrawInfo buildWithdrawInfo(String filed, C_PlatformWithdraw.WithdrawInfo withdrawInfo) {
        return BillingMessage.WithdrawInfo.newBuilder()
                .setField(filed)
                .addAllParams(withdrawInfo.getParams())
                .setType(withdrawInfo.getType())
                .build();
    }
}
