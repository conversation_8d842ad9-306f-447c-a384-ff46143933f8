package com.game.handler.tcp.inner;

import com.game.c_entity.merchant.C_WithdrawExchange;
import com.game.dao.order.WithdrawOrderDao;
import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.utils.JsonUtils;
import com.game.engine.utils.UniqueIDGenerator;
import com.game.entity.order.WithdrawInfo;
import com.game.entity.order.WithdrawOrder;
import com.game.entity.player.*;
import com.game.entity.player.stats.Stats;
import com.game.enums.PaymentMethod;
import com.game.enums.FunctionEnabled;
import com.game.enums.RewardReason;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.InboxMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.mrg.currency.RewardRequest;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.IFunctionEnabledScript;
import com.game.hall.script.IInboxScript;
import com.game.manager.EntityDaoMrg;
import com.proto.InnerMessage;
import com.proto.MIDMessage;
import com.proto.TcpMessage;
import io.netty.util.internal.StringUtil;
import it.unimi.dsi.fastutil.ints.IntLists;
import it.unimi.dsi.fastutil.longs.LongLists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Update;

import java.util.Collections;
import java.util.Map;
import java.util.Objects;

@IHandlerEntity(mid = MIDMessage.MID.InnerNotifyWithdraw_VALUE, msg = InnerMessage.InnerNotifyWithdrawMessage.class)
public class HallInnerNotifyWithdrawHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(HallInnerNotifyWithdrawHandler.class);

    @Override
    public void run() {
        final InnerMessage.InnerNotifyWithdrawMessage withDraw = (InnerMessage.InnerNotifyWithdrawMessage) getMessage();
        final int currencyId = withDraw.getCurrencyId();
        int exchangeCurrency = withDraw.getCurrencyId();
        double amount = withDraw.getAmount();
        final long orderId = withDraw.getOrderId();
        final int status = withDraw.getStatus();

        final Player player = PlayerMrg.getInstance().findDbPlayer(pid);
        if (player == null) {
            LOGGER.warn("playerId：{}，not exist", pid);
            return;
        }

        player.setWithdrawInProgress(0);
        final Update update = new Update();
        update.set(PlayerFields.withdrawInProgress, player.getWithdrawInProgress());
        EntityDaoMrg.getInstance().getDao(PlayerDao.class).updatePlayer(player.getPlayerId(), update);

        final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
        if (merchantData != null) {
            final String withdrawExchangeOpen = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "withdrawExchangeOpen");
            if (!StringUtil.isNullOrEmpty(withdrawExchangeOpen) && Boolean.parseBoolean(withdrawExchangeOpen)) {
                final C_WithdrawExchange c_withdrawExchange = merchantData.findC_WithdrawExchange(this.getClass().getSimpleName(), currencyId);
                if (c_withdrawExchange != null) {
                    exchangeCurrency = c_withdrawExchange.getExchangeCurrency();
                    amount = BigDecimalUtils.mul(amount, c_withdrawExchange.getRate(), 4);
                }
            }
        }

        //提现通知
        final TcpMessage.ResTcpWithdrawNotifyMessage.Builder res = TcpMessage.ResTcpWithdrawNotifyMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResTcpWithdrawNotify_VALUE)
                .setPlayerId(player.getPlayerId())
                .setOrderId(orderId)
                .setCurrencyId(exchangeCurrency)
                .setAmount(amount)
                .setStatus(status);
        player.sendMsg(res.build());

        if (status == 3 || status == 4) {//失败
            if (player.getDailyFeeTimes() > 0) {
                player.setDailyFeeTimes(player.getDailyFeeTimes() - 1);
                EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                        .updatePlayerField(pid, PlayerFields.dailyFeeTimes, player.getDailyFeeTimes());
            }

            final RewardRequest rewardRequest = new RewardRequest();
            final RewardReason rewardReason = RewardReason.Draw_fail;
            rewardReason.setSource(orderId + "");
            rewardRequest.addCurrency(exchangeCurrency, amount, false);
            CurrencyMrg.getInstance().rewardNoTest(player, rewardRequest.getCurrencyMap(), rewardReason);
            EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                    .updateCurrency(player, IntLists.singleton(exchangeCurrency));

            //TODO 邮件通知
            ScriptLoader.getInstance().consumerScript("InboxScript",
                    (IInboxScript script) -> script.sendInboxMail(player, InboxMrg.WITHDRAW_REJECT, Collections.singletonList(player.getPlayerName())));
            return;
        }

        if (currencyId / 1000 == 2) {
            final WithdrawOrder withdrawOrder = EntityDaoMrg.getInstance().getDao(WithdrawOrderDao.class).findWithdrawOrder(orderId);
            if (withdrawOrder != null) {
                final WithdrawInfo.WithdrawAccount withdraw = JsonUtils.readFromJson(withdrawOrder.getWithdrawAccount(), WithdrawInfo.WithdrawAccount.class);
                final String currencyIdPayment = currencyId + "_" + PaymentMethod.Encrypt.getType();
                WithdrawAccount withdrawAccount = player.getWithdrawAccountMap().get(currencyIdPayment);
                if (withdrawAccount == null) {
                    withdrawAccount = new WithdrawAccount();
                    player.getWithdrawAccountMap().put(currencyIdPayment, withdrawAccount);
                }
                final Map<Long, PayInfo> payInfoMap = withdrawAccount.getPayInfoMap();
                final long count = payInfoMap.values().stream().filter(payInfo -> Objects.equals(payInfo.getExtend(), withdraw.getExtend())).count();
                if (count == 0) {
                    final UniqueIDGenerator uniqueIDGenerator = HallServer.getInstance().getUniqueIDGenerator();
                    final PayInfo payInfo = new PayInfo();
                    payInfo.setPayId(uniqueIDGenerator.nextId());
                    payInfo.setCurrencyId(currencyId);
                    payInfo.setPaymentMethod(PaymentMethod.Encrypt.getType());
                    payInfo.setExtend(withdraw.getExtend());
                    payInfo.setExtend_1(withdraw.getExtend_1());
                    payInfoMap.put(payInfo.getPayId(), payInfo);
                    EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                            .updateInsertWithdrawAccount(player, currencyIdPayment, LongLists.singleton(payInfo.getPayId()));
                }

                if (withDraw.getCurrencyTraderId() > 0) {
                    if (player.getCurrencyTraderId() == 0) {
                        player.setCurrencyTraderId(withDraw.getCurrencyTraderId());
                        EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                                .updatePlayerField(pid, PlayerFields.currencyTraderId, player.getCurrencyTraderId());
                    }
                }

                //TODO 统计提现
                final Stats stats = player.getStats(exchangeCurrency);
                stats.incWithdrawAmount(amount);
                final boolean functionEnabled = ScriptLoader.getInstance().functionScript("FunctionEnabledScript", (IFunctionEnabledScript script)
                        -> script.functionEnabled(player.getBusiness_no(), FunctionEnabled.CurrencyTrader.getType()));
                if (functionEnabled) {
                    stats.incCurrencyTraderWithdrawAmount(amount);
                } else {
                    stats.incWithdrawAmount(amount);
                }

                EntityDaoMrg.getInstance().getDao(PlayerDao.class).statsDao
                        .updateStats(player.getPlayerId(), player.getStatsInfo(), IntLists.singleton(exchangeCurrency));

                final WithdrawStandard withdrawStandard = player.getWithdrawStandard(exchangeCurrency);
                withdrawStandard.setTotalRecharge(0);
                withdrawStandard.resetTemp();
                EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                        .updateWithdrawStandard(player, IntLists.singleton(exchangeCurrency));

                //TODO 邮件通知
                ScriptLoader.getInstance().consumerScript("InboxScript",
                        (IInboxScript script) -> script.sendInboxMail(player, InboxMrg.WITHDRAW_PASS, Collections.singletonList(player.getPlayerName())));

                ScriptLoader.getInstance().consumerScript("InboxScript",
                        (IInboxScript script) -> script.sendEventPubMail(player, InboxMrg.WITHDRAW, true));
            }

        }
    }
}