package com.game.handler.tcp.activity.firstDepositInviteBonus;

import com.game.c_entity.merchant.C_FirstDepositInviteBonus;
import com.game.dao.player.PlayerPromoteDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.player.Player;
import com.game.entity.player.activity.firstDepositInviteBonus.FirstDepositInviteBonusInfo;
import com.game.entity.player.activity.firstDepositInviteBonus.InviteRecharge;
import com.game.entity.player.promote.PlayerPromote;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.ActivityMrg;
import com.game.hall.mrg.CommonMrg;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import com.proto.ActivityMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@IHandlerEntity(mid = MIDMessage.MID.ReqCheckDetailsData_VALUE, msg = ActivityMessage.ReqCheckDetailsDataMessage.class)
public class ReqCheckDetailsDataHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqCheckDetailsDataHandler.class);

    @Override
    public void run() {
        final ActivityMessage.ResCheckDetailsDataMessage.Builder res = ActivityMessage.ResCheckDetailsDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResCheckDetailsData_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final C_FirstDepositInviteBonus c_firstDepositInviteBonus = merchantData.findC_FirstDepositInviteBonus(this.getClass().getSimpleName(), ActivityMrg.FIRSTDEPOSIT_INIVTEBONUS);
            if (c_firstDepositInviteBonus == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final ActivityMessage.ReqCheckDetailsDataMessage req = (ActivityMessage.ReqCheckDetailsDataMessage) getMessage();
            final int page = req.getPage();
            final int pageSize = req.getPageSize();

            final List<Long> playerId = findSubordinates(player);
            final List<Long> allPlayerId = paginate(playerId, page, pageSize);

            if (allPlayerId.isEmpty()) {
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final FirstDepositInviteBonusInfo firstDepositInviteBonusInfo = player.getFirstDepositInviteBonusInfo();

            for (long pid : allPlayerId) {
                final Player p = PlayerMrg.getInstance().findDbPlayer(pid);
                if (p == null) {
                    continue;
                }

                final InviteRecharge inviteRecharge = firstDepositInviteBonusInfo.getInviteRechargeMap().get(pid);
                if (inviteRecharge == null) {
                    continue;
                }

                final double charge = inviteRecharge.getChargeMap()
                        .getOrDefault(inviteRecharge.getCurrencyId(), 0d);

                double bonus = BigDecimalUtils.mul(charge, c_firstDepositInviteBonus.getRewardRate(), 4);
                if (bonus >= c_firstDepositInviteBonus.getMaxRewardAmount()) {
                    bonus = c_firstDepositInviteBonus.getMaxRewardAmount();
                }

                res.addFriendsBonusDataList(buildFriendsBonusData(p, inviteRecharge.getCurrencyId(), charge, bonus));
            }

            res.setTotal(playerId.size())
                    .setTotalPage(CommonMrg.totalPage(playerId.size(), pageSize));
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqCheckDetailsDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    private <T> List<T> paginate(List<T> list, int page, int pageSize) {
        if (list == null || list.isEmpty()) return Collections.emptyList();

        int totalItems = list.size();
        int fromIndex = (page - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, totalItems);

        if (fromIndex >= totalItems) {
            return Collections.emptyList(); // 页码过大返回空列表
        }

        return list.subList(fromIndex, toIndex);
    }

    private ActivityMessage.FriendsBonusData buildFriendsBonusData(Player player, int currencyId, double chargeAmount, double bonus) {
        final ActivityMessage.FriendsBonusData.Builder res = ActivityMessage.FriendsBonusData.newBuilder();
        res.setPlayerId(player.getPlayerId())
                .setHeadId(player.getHeadId())
                .setPlayerName(player.getPlayerName())
                .setCurrencyId(currencyId)
                .setChargeAmount(chargeAmount)
                .setBonus(bonus);
        return res.build();
    }

    private List<Long> findSubordinates(Player player) {
        final int limit = 1000;
        int totalCount = 0;
        final List<Long> subordinates = new ArrayList<>();
        while (true) {
            final List<PlayerPromote> friendsList = EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class).loadAllBySuperiorId(player.getPlayerId(), totalCount, limit);
            if (friendsList == null || friendsList.isEmpty()) break;

            for (var friend : friendsList) {
                if (friend == null) continue;
                subordinates.add(friend.getPlayerId());
            }

            totalCount += friendsList.size();
            if (friendsList.size() < limit) break;
        }
        return subordinates;
    }
}
