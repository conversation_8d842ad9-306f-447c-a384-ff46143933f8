package com.game.handler.tcp.billing;

import com.game.c_entity.merchant.C_PlatformWithdraw;
import com.game.c_entity.merchant.C_RechargeWithdrawLimit;
import com.game.c_entity.merchant.C_VipClub;
import com.game.c_entity.merchant.C_WithdrawExchange;
import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.util.misc.Tuple2;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.UniqueIDGenerator;
import com.game.entity.player.*;
import com.game.entity.player.stats.Stats;
import com.game.entity.player.vip.VipClub;
import com.game.enums.ErrorCode;
import com.game.enums.SpendReason;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.mrg.currency.SpendRequest;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.IBillingScript;
import com.game.manager.EntityDaoMrg;
import com.game.manager.ServersMrg;
import com.proto.BillingMessage;
import com.proto.MIDMessage;
import io.netty.channel.Channel;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Update;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;


@IHandlerEntity(mid = MIDMessage.MID.ReqCreateWithdrawOrder_VALUE, msg = BillingMessage.ReqCreateWithdrawOrderMessage.class)
public class ReqCreateWithdrawOrderHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqCreateWithdrawOrderHandler.class);

    @Override
    public void run() {
        final BillingMessage.ResCreateWithdrawOrderMessage.Builder res = BillingMessage.ResCreateWithdrawOrderMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResCreateWithdrawOrder_VALUE);

        final Player player = PlayerMrg.getInstance().getOnlinePlayer(this.pid, this.getClass().getSimpleName());
        if (player == null) {
            res.setError(ErrorCode.Player_Offline.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
            return;
        }

        try {
            final BillingMessage.ReqCreateWithdrawOrderMessage req = (BillingMessage.ReqCreateWithdrawOrderMessage) getMessage();
            final int currencyId = req.getCurrencyId();
            int exchangeCurrency = req.getCurrencyId();

            if (currencyId == 0) {
                LOGGER.error("playerId：{}，currencyId is 0", player.getPlayerId());
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final String withdrawExchangeOpen = merchantData.findC_GlobalValue(this.getClass().getSimpleName(), "withdrawExchangeOpen");
            if (!StringUtil.isNullOrEmpty(withdrawExchangeOpen) && Boolean.parseBoolean(withdrawExchangeOpen)) {
                final C_WithdrawExchange c_withdrawExchange = merchantData.findC_WithdrawExchange(this.getClass().getSimpleName(), currencyId);
                if (c_withdrawExchange != null) {
                    exchangeCurrency = c_withdrawExchange.getExchangeCurrency();
                }
            }

            if (StringUtil.isNullOrEmpty(withdrawExchangeOpen) || !Boolean.parseBoolean(withdrawExchangeOpen)) {
                if (!player.isPhoneBind()) {
                    res.setError(ErrorCode.Phone_Not_Bound.getCode());
                    MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                    return;
                }
            }

            if (player.isFreeze()) {
                res.setError(ErrorCode.Player_Freeze.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            if (player.isWithdrawalLimit()) {
                res.setError(ErrorCode.WithdrawalLimit.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            if (player.getWithdrawInProgress() > 0) {
                res.setError(ErrorCode.Withdraw_Order_UnderReview.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final Stats stats = player.getStats(exchangeCurrency);
            final VipClub vipClub = player.getVipClub();

            final C_VipClub c_vipClub = merchantData.findC_VipClub(this.getClass().getSimpleName(), vipClub.getVipLevel());
            if (c_vipClub == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            if (stats.getDailyWithdrawTimes() >= c_vipClub.getDailyWithdrawTimes()) {
                LOGGER.warn("playerId：{}，dailyWithdrawTimes：{} - vipDailyWithdrawTimes：{}"
                        , pid, stats.getDailyWithdrawTimes(), c_vipClub.getDailyWithdrawTimes());
                res.setError(ErrorCode.No_Withdraw_Times.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final C_RechargeWithdrawLimit c_limit = merchantData.findC_RechargeWithdrawLimit(this.getClass().getSimpleName(), exchangeCurrency);
            if (c_limit == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            if (stats.getDailyBetTimes() < c_limit.getNeedBetTimes()) {
                LOGGER.warn("playerId：{}，dailyBetTimes：{} - needBetTimes：{}"
                        , pid, stats.getDailyBetTimes(), c_limit.getNeedBetTimes());
                res.setError(ErrorCode.Withdraw_BetTimes_Unfinished.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final int finalExchangeCurrency = exchangeCurrency;
            final Tuple2<Double, Double> tuple2 = ScriptLoader.getInstance().functionScript("BillingScript",
                    (IBillingScript script) -> script.calculateAvailableAmount(player, finalExchangeCurrency, c_limit.getWithdrawType()));
            double available = tuple2.getFirst();

            final UniqueIDGenerator uniqueIDGenerator = HallServer.getInstance().getUniqueIDGenerator();
            final long orderId = uniqueIDGenerator.nextId();
            final int currencyType = currencyId / 1000;
            ErrorCode errorCode = ErrorCode.Success;
            final SpendRequest spendRequest = new SpendRequest();
            double fee = 0;
            double amount = 0;
            switch (currencyType) {
                case 1://法币
                {
                    final BillingMessage.WithdrawFiat withdrawFiat = req.getWithdrawFiat();

                    if (available == 0 || withdrawFiat.getAmount() > available) {
                        res.setError(ErrorCode.Insufficient_Available_Funds.getCode());
                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                        return;
                    }

                    if (BigDecimalUtils.add(stats.getDailyWithdrawAmount(), withdrawFiat.getAmount(), 4) > c_vipClub.getDailyWithdrawAmount()) {
                        res.setError(ErrorCode.Withdraw_UpperLimit.getCode());
                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                        return;
                    }

//                    if (withdrawFiat.getAmount() < c_vipClub.getDailyWithdrawLower() || withdrawFiat.getAmount() > c_vipClub.getDailyWithdrawUpper()) {
//                        res.setError(ErrorCode.WithDraw_Not_Within_The_Range.getCode());
//                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
//                        return;
//                    }

                    final long withdrawAccountId = StringUtil.isNullOrEmpty(withdrawFiat.getWithdrawAccountId()) ? 0 : Long.parseLong(withdrawFiat.getWithdrawAccountId());
                    final String currencyIdPayment = currencyId + "_" + withdrawFiat.getPaymentMethod();
                    final WithdrawAccount withdrawAccount = player.getWithdrawAccountMap().get(currencyIdPayment);
                    if (withdrawAccount == null) {
                        LOGGER.warn("playerId：{}，currencyIdPayment：{}，not withdrawAccount", pid, currencyIdPayment);
                        res.setError(ErrorCode.Not_WithdrawAccount.getCode());
                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                        return;
                    }
                    final PayInfo payInfo = withdrawAccount.getPayInfoMap().get(withdrawAccountId);
                    if (payInfo == null) {
                        LOGGER.warn("playerId：{}，withdrawAccountId：{}，not withdrawAccount", pid, withdrawAccountId);
                        res.setError(ErrorCode.Not_WithdrawAccount.getCode());
                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                        return;
                    }

                    final Map<String, C_PlatformWithdraw> c_platformWithdrawFiatMap = merchantData.findC_PlatformWithdrawFiat(this.getClass().getSimpleName(), currencyId);
                    if (c_platformWithdrawFiatMap == null || c_platformWithdrawFiatMap.isEmpty()) {
                        res.setError(ErrorCode.Data_Error.getCode());
                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                        return;
                    }

                    final int channel = withdrawFiat.getChannel();
                    final int paymentMethod = withdrawFiat.getPaymentMethod();
                    final C_PlatformWithdraw c_platformWithdraw = c_platformWithdrawFiatMap.get(channel + "_" + paymentMethod);
                    if (c_platformWithdraw == null) {
                        LOGGER.warn("config，C_PlatformWithdraw，currencyId：{}，not exits", currencyId);
                        res.setError(ErrorCode.Data_Error.getCode());
                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                        return;
                    }

                    if (withdrawFiat.getAmount() < Math.max(c_vipClub.getDailyWithdrawLower(), c_platformWithdraw.getSingleWithdrawLowerLimit())
                            || withdrawFiat.getAmount() > Math.min(c_vipClub.getDailyWithdrawUpper(), c_platformWithdraw.getSingleWithdrawUpperLimit())) {
                        res.setError(ErrorCode.WithDraw_Not_Within_The_Range.getCode());
                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                        return;
                    }

                    amount = withdrawFiat.getAmount();
                    fee = c_platformWithdraw.calculateFee(player.getPlayerId(), currencyId, player.getRechargeTime(), withdrawFiat.getAmount());
                    spendRequest.addCurrency(currencyId, withdrawFiat.getAmount());
                    final SpendReason spendReason = SpendReason.WithDraw;
                    spendReason.setSource("" + orderId);
                    errorCode = CurrencyMrg.getInstance().spend(player, spendRequest.getCurrencyMap(), spendReason);
                }
                break;
                case 2://虚拟币
                {
                    if (!StringUtil.isNullOrEmpty(withdrawExchangeOpen) && Boolean.parseBoolean(withdrawExchangeOpen)) {
                        final C_WithdrawExchange c_withdrawExchange = merchantData.findC_WithdrawExchange(this.getClass().getSimpleName(), currencyId);
                        if (c_withdrawExchange != null) {
                            available = BigDecimalUtils.div(available, c_withdrawExchange.getRate(), 4);
                            LOGGER.warn("playerId：{}，available：{}", pid, available);
                        }
                    }

                    final BillingMessage.WithdrawCrypto withDrawCrypto = req.getWithdrawCrypto();
                    if (available == 0 || withDrawCrypto.getAmount() > available) {
                        res.setError(ErrorCode.Insufficient_Available_Funds.getCode());
                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                        return;
                    }

                    if (BigDecimalUtils.add(stats.getDailyWithdrawAmount(), withDrawCrypto.getAmount(), 4) > c_vipClub.getDailyWithdrawAmount()) {
                        res.setError(ErrorCode.Withdraw_UpperLimit.getCode());
                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                        return;
                    }

//                    if (withDrawCrypto.getAmount() < c_vipClub.getDailyWithdrawLower() || withDrawCrypto.getAmount() > c_vipClub.getDailyWithdrawUpper()) {
//                        res.setError(ErrorCode.WithDraw_Not_Within_The_Range.getCode());
//                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
//                        return;
//                    }

                    final String network = withDrawCrypto.getNetwork();
                    final List<C_PlatformWithdraw> c_platformWithdrawCryptoList = merchantData.findC_PlatformWithdrawCrypto(this.getClass().getSimpleName(), currencyId);
                    if (c_platformWithdrawCryptoList == null || c_platformWithdrawCryptoList.isEmpty()) {
                        res.setError(ErrorCode.Data_Error.getCode());
                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                        return;
                    }

                    final Optional<C_PlatformWithdraw> optional = c_platformWithdrawCryptoList.stream().filter(c_platform -> Objects.equals(c_platform.getNetwork(), network)).findFirst();
                    if (optional.isEmpty()) {
                        res.setError(ErrorCode.Data_Error.getCode());
                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                        return;
                    }

                    final C_PlatformWithdraw c_platformWithdrawCrypto = optional.get();
                    if (withDrawCrypto.getAmount() < Math.max(c_vipClub.getDailyWithdrawLower(), c_platformWithdrawCrypto.getSingleWithdrawLowerLimit())
                            || withDrawCrypto.getAmount() > Math.min(c_vipClub.getDailyWithdrawUpper(), c_platformWithdrawCrypto.getSingleWithdrawUpperLimit())) {
                        res.setError(ErrorCode.WithDraw_Not_Within_The_Range.getCode());
                        MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                        return;
                    }

                    fee = c_platformWithdrawCrypto.calculateFee(player.getPlayerId(), exchangeCurrency, player.getRechargeTime(), withDrawCrypto.getAmount());
                    amount = withDrawCrypto.getAmount();

                    if (!StringUtil.isNullOrEmpty(withdrawExchangeOpen) && Boolean.parseBoolean(withdrawExchangeOpen)) {
                        final C_WithdrawExchange c_withdrawExchange = merchantData.findC_WithdrawExchange(this.getClass().getSimpleName(), currencyId);
                        if (c_withdrawExchange != null) {
                            amount = BigDecimalUtils.mul(amount, c_withdrawExchange.getRate(), 4);
                        }
                    }

                    spendRequest.addCurrency(exchangeCurrency, amount);
                    final SpendReason spendReason = SpendReason.WithDraw;
                    spendReason.setSource("" + orderId);
                    errorCode = CurrencyMrg.getInstance().spend(player, spendRequest.getCurrencyMap(), spendReason);
                }
                break;
            }

            if (errorCode != ErrorCode.Success) {
                res.setError(errorCode.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            player.setWithdrawInProgress(1);
            final Update update = new Update();
            update.set(PlayerFields.withdrawInProgress, player.getWithdrawInProgress());
            EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                    .updatePlayer(player.getPlayerId(), update);

            final double finalAmount = amount;
            final double finalAvailable = available;
            ScriptLoader.getInstance().consumerScript("BillingScript",
                    (IBillingScript script) -> script.recalculateWithdrawStandard(player, finalExchangeCurrency, finalAvailable, finalAmount, c_limit.getWithdrawType()));

            if (player.getDailyFeeTimes() < c_vipClub.getDailyWithdrawFreeTimes()) {
                player.setDailyFeeTimes(player.getDailyFeeTimes() + 1);
                EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                        .updatePlayerField(pid, PlayerFields.dailyFeeTimes, player.getDailyFeeTimes());
                fee = 0;
            }

            final WithdrawStandard withdrawStandard = player.getWithdrawStandard(player.getCurrencyId());
            final double balance = CurrencyMrg.getInstance().getCurrencyValue(player, currencyId);
            final BillingMessage.ReqCreateWithdrawOrderMessage withDrawOrder = req.toBuilder()
                    .setFee(fee)
                    .setExchangeCurrency(exchangeCurrency)
                    .setBalance(balance)
                    .setOrderId(orderId)
                    .setDrawStandard(withdrawStandard.getLastDrawStandard())
                    .setBettingVolume(withdrawStandard.getLastBettingVolume())
                    .build();

            final ServersMrg serversMrg = HallServer.getInstance().getHallTcpClient2Billing().getServersMrg();
            final Channel billingSession = serversMrg.getSessionByAccountId(pid);
            MsgUtil.sendInnerMsg(billingSession, withDrawOrder, pid, udpSessionId);
        } catch (Exception e) {
            LOGGER.error("HallReqCreateWithDrawOrderHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

}
