package com.game.handler.tcp.affiliate;

import com.game.dao.game.GameNoteDao;
import com.game.dao.player.PlayerPromoteDao;
import com.game.dao.promote.CommissionRewardsNoteDao;
import com.game.dao.promote.TeamRewardsNoteDao;
import com.game.dao.promote.ThreeLevelRewardsNoteDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.util.misc.Tuple2;
import com.game.engine.utils.FastCollectionsUtils;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.TimeUtil;
import com.game.entity.game.GameNoteFields;
import com.game.entity.game.GameWagerStat;
import com.game.entity.player.Player;
import com.game.entity.player.promote.*;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.mrg.currency.RewardRequest;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import com.proto.AffiliateMessage;
import com.proto.MIDMessage;
import it.unimi.dsi.fastutil.ints.Int2DoubleMap;
import it.unimi.dsi.fastutil.objects.ObjectIterator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@IHandlerEntity(mid = MIDMessage.MID.ReqDayRebateData_VALUE, msg = AffiliateMessage.ReqDayRebateDataMessage.class)
public class ReqDayRebateDataHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqDayRebateDataHandler.class);

    @Override
    public void run() {
        final AffiliateMessage.ResDayRebateDataMessage.Builder res = AffiliateMessage.ResDayRebateDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResDayRebateData_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final AffiliateMessage.ReqDayRebateDataMessage req = (AffiliateMessage.ReqDayRebateDataMessage) getMessage();
            final int currencyId = req.getCurrencyId();
            final String date = req.getDate();

//            LOGGER.warn("playerId：{}，date：{}", player.getPlayerId(), date);
            final PlayerPromote playerPromote = player.getPlayerPromote();
            final Map<String, ReferralCode> referralCodeMap = playerPromote.getReferralCodeMap();

            final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-M-d");
            final LocalDate localDate = LocalDate.parse(date, formatter);
            final long timestamp = localDate.atStartOfDay(ZoneId.of(player.getTimeZone()))
                    .toInstant()
                    .toEpochMilli();

            final long startTime = TimeUtil.getTimeBeginOfToday(timestamp, player.getTimeZone());
            final long endTime = TimeUtil.getTimeEndOfToday(timestamp, player.getTimeZone());

            final Tuple2<Integer, List<Long>> oneLevelsTuple = EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class)
                    .findByCodeOneLevels(new ArrayList<>(referralCodeMap.keySet()), startTime, endTime);
            final Tuple2<Integer, List<Long>> twoLevelsTuple = EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class)
                    .findByCodeTwoLevels(new ArrayList<>(referralCodeMap.keySet()), startTime, endTime);
            final Tuple2<Integer, List<Long>> threeLevelsTuple = EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class)
                    .findByCodeThreeLevels(new ArrayList<>(referralCodeMap.keySet()), startTime, endTime);

            //人数
            final int oneLevelFriends = oneLevelsTuple.getFirst();
            final int twoLevelFriends = twoLevelsTuple.getFirst();
            final int threeLevelFriends = threeLevelsTuple.getFirst();
            final int registered = oneLevelFriends + twoLevelFriends + threeLevelFriends;

            //返水
            final double oneRebate = EntityDaoMrg.getInstance().getDao(CommissionRewardsNoteDao.class)
                    .aggregateRewards(player.getPlayerId(), currencyId, CommissionRewardsNoteFields.amount, startTime, endTime);
            final double twoRebate = EntityDaoMrg.getInstance().getDao(TeamRewardsNoteDao.class)
                    .aggregateRewards(player.getPlayerId(), currencyId, TeamRewardsNoteFields.amount, startTime, endTime);
            final double threeRebate = EntityDaoMrg.getInstance().getDao(ThreeLevelRewardsNoteDao.class)
                    .aggregateRewards(player.getPlayerId(), currencyId, ThreeLevelRewardsNoteFields.amount, startTime, endTime);

            //有效下注
            final List<Long> friends = new ArrayList<>();
            friends.addAll(oneLevelsTuple.getSecond());
            friends.addAll(twoLevelsTuple.getSecond());
            friends.addAll(threeLevelsTuple.getSecond());
            final GameWagerStat gameWagerStat = EntityDaoMrg.getInstance().getDao(GameNoteDao.class)
                    .aggregateGameWagerStat(friends, currencyId, GameNoteFields.validBets, startTime, endTime);

            res.setDayRebateInfo(buildDayRebateInfo(registered, gameWagerStat.getTotalWager()
                    , oneLevelFriends, oneRebate
                    , twoLevelFriends, twoRebate
                    , threeLevelFriends, threeRebate));

            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqDayRebateDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    private AffiliateMessage.DayRebateInfo buildDayRebateInfo(int registered, double validWagered,
                                                              int oneLevels, double oneRebate,
                                                              int twoLevels, double twoRebate,
                                                              int threeLevels, double threeRebate) {
        return AffiliateMessage.DayRebateInfo.newBuilder()
                .setRegistered(registered)
                .setValidWagered(validWagered)
                .setOneLevels(oneLevels)
                .setOneRebate(oneRebate)
                .setTwoLevels(twoLevels)
                .setTwoRebate(twoRebate)
                .setThreeLevels(threeLevels)
                .setThreeRebate(threeRebate)
                .build();
    }

}
