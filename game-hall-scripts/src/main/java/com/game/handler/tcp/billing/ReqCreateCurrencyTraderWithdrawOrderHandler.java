package com.game.handler.tcp.billing;

import com.game.c_entity.merchant.C_CurrencyTraderWithdraw;
import com.game.c_entity.merchant.C_RechargeWithdrawLimit;
import com.game.dao.player.PlayerDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.script.ScriptLoader;
import com.game.engine.util.misc.Tuple2;
import com.game.engine.utils.MsgUtil;
import com.game.engine.utils.UniqueIDGenerator;
import com.game.entity.player.*;
import com.game.entity.player.stats.Stats;
import com.game.entity.player.stats.StatsInfo;
import com.game.enums.ErrorCode;
import com.game.enums.FunctionEnabled;
import com.game.enums.SpendReason;
import com.game.hall.main.HallServer;
import com.game.hall.mrg.DataHallMrg;
import com.game.hall.mrg.MerchantData;
import com.game.hall.mrg.currency.CurrencyMrg;
import com.game.hall.mrg.currency.SpendRequest;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.hall.script.IBillingScript;
import com.game.hall.script.IFunctionEnabledScript;
import com.game.manager.EntityDaoMrg;
import com.game.manager.ServersMrg;
import com.proto.BillingMessage;
import com.proto.MIDMessage;
import io.netty.channel.Channel;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Update;

@IHandlerEntity(mid = MIDMessage.MID.ReqCreateCurrencyTraderWithdrawOrder_VALUE, msg = BillingMessage.ReqCreateCurrencyTraderWithdrawOrderMessage.class)
public class ReqCreateCurrencyTraderWithdrawOrderHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqCreateCurrencyTraderWithdrawOrderHandler.class);

    @Override
    public void run() {
        final BillingMessage.ResCreateCurrencyTraderWithdrawOrderMessage.Builder res = BillingMessage.ResCreateCurrencyTraderWithdrawOrderMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResCreateCurrencyTraderWithdrawOrder_VALUE);

        final Player player = PlayerMrg.getInstance().getOnlinePlayer(this.pid, this.getClass().getSimpleName());
        if (player == null) {
            res.setError(ErrorCode.Player_Offline.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
            return;
        }

        final BillingMessage.ReqCreateCurrencyTraderWithdrawOrderMessage req = (BillingMessage.ReqCreateCurrencyTraderWithdrawOrderMessage) getMessage();
        final int currencyId = req.getCurrencyId();
        final int currencyTraderId = req.getCurrencyTraderId();
        final double withdrawAmount = req.getAmount();
        final String withdrawAccountIdStr = req.getWithdrawAccountId();
        try {
            final boolean functionEnabled = ScriptLoader.getInstance().functionScript("FunctionEnabledScript", (IFunctionEnabledScript script)
                    -> script.functionEnabled(player.getBusiness_no(), FunctionEnabled.CurrencyTrader.getType()));
            if (!functionEnabled) {
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            if (currencyId == 0) {
                LOGGER.error("playerId：{}，currencyId is 0", player.getPlayerId());
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            if (!player.isPhoneBind()) {
                res.setError(ErrorCode.Phone_Not_Bound.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            if (player.isFreeze()) {
                res.setError(ErrorCode.Player_Freeze.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            if (player.isWithdrawalLimit()) {
                res.setError(ErrorCode.WithdrawalLimit.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            if (player.getWithdrawInProgress() > 0) {
                res.setError(ErrorCode.Withdraw_Order_UnderReview.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final MerchantData merchantData = DataHallMrg.getInstance().findMerchantData(this.getClass().getSimpleName(), player.getBusiness_no());
            if (merchantData == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final C_RechargeWithdrawLimit c_rechargeWithdrawLimit = merchantData.findC_RechargeWithdrawLimit(this.getClass().getSimpleName(), currencyId);
            if (c_rechargeWithdrawLimit == null) {
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final C_CurrencyTraderWithdraw c_currencyTraderWithdraw = merchantData.findC_CurrencyTraderWithdraw(this.getClass().getSimpleName(), currencyTraderId);
            if (c_currencyTraderWithdraw == null) {
                res.setError(ErrorCode.Data_Error.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final Stats stats = player.getStats(currencyId);
            if (stats.getDailyCurrencyTraderWithdrawTimes() >= c_currencyTraderWithdraw.getWithdrawTimes()) {
                LOGGER.warn("playerId：{}，dailyCurrencyTraderFeeTimes：{} - withdrawTimes：{}"
                        , pid, stats.getDailyCurrencyTraderWithdrawTimes(), c_currencyTraderWithdraw.getWithdrawTimes());
                res.setError(ErrorCode.No_Withdraw_Times.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            if (withdrawAmount < c_currencyTraderWithdraw.getSingleWithdrawLowerLimit()
                    || withdrawAmount > c_currencyTraderWithdraw.getSingleWithdrawUpperLimit()) {
                res.setError(ErrorCode.WithDraw_Not_Within_The_Range.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            final Tuple2<Double, Double> tuple2 = ScriptLoader.getInstance().functionScript("BillingScript",
                    (IBillingScript script) -> script.calculateAvailableAmount(player, currencyId, c_rechargeWithdrawLimit.getWithdrawType()));
            final double available = tuple2.getFirst();

            final UniqueIDGenerator uniqueIDGenerator = HallServer.getInstance().getUniqueIDGenerator();
            final long orderId = uniqueIDGenerator.nextId();
            final int currencyType = currencyId / 1000;

            if (available == 0 || withdrawAmount > available) {
                res.setError(ErrorCode.Insufficient_Available_Funds.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            if (currencyType == 1) {//法币
                final long withdrawAccountId = StringUtil.isNullOrEmpty(withdrawAccountIdStr) ? 0 : Long.parseLong(withdrawAccountIdStr);
                final String currencyIdPayment = currencyId + "_" + 101;
                final WithdrawAccount withdrawAccount = player.getWithdrawAccountMap().get(currencyIdPayment);
                if (withdrawAccount == null) {
                    LOGGER.warn("playerId：{}，currencyIdPayment：{}，not withdrawAccount", pid, currencyIdPayment);
                    res.setError(ErrorCode.Not_WithdrawAccount.getCode());
                    MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                    return;
                }
                final PayInfo payInfo = withdrawAccount.getPayInfoMap().get(withdrawAccountId);
                if (payInfo == null) {
                    LOGGER.warn("playerId：{}，withdrawAccountId：{}，not withdrawAccount", pid, withdrawAccountId);
                    res.setError(ErrorCode.Not_WithdrawAccount.getCode());
                    MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                    return;
                }
            }

            final SpendRequest spendRequest = new SpendRequest();

            double fee = BigDecimalUtils.mul(withdrawAmount, c_currencyTraderWithdraw.getWithdrawFeeRate(), 9);
            spendRequest.addCurrency(currencyId, withdrawAmount);

            final SpendReason spendReason = SpendReason.WithDraw;
            spendReason.setSource("" + orderId);
            final ErrorCode errorCode = CurrencyMrg.getInstance().spend(player, spendRequest.getCurrencyMap(), spendReason);
            if (errorCode != ErrorCode.Success) {
                res.setError(errorCode.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }

            player.setWithdrawInProgress(1);
            final Update update = new Update();
            update.set(PlayerFields.withdrawInProgress, player.getWithdrawInProgress());
            EntityDaoMrg.getInstance().getDao(PlayerDao.class)
                    .updatePlayer(player.getPlayerId(), update);

            ScriptLoader.getInstance().consumerScript("BillingScript",
                    (IBillingScript script) -> script.recalculateWithdrawStandard(player, currencyId, available, withdrawAmount, c_rechargeWithdrawLimit.getWithdrawType()));

            final WithdrawStandard withdrawStandard = player.getWithdrawStandard(player.getCurrencyId());
            final double balance = CurrencyMrg.getInstance().getCurrencyValue(player, currencyId);
            final BillingMessage.ReqCreateCurrencyTraderWithdrawOrderMessage withDrawOrder = req.toBuilder()
                    .setFee(fee)
                    .setBalance(balance)
                    .setOrderId(orderId)
                    .setDrawStandard(withdrawStandard.getLastDrawStandard())
                    .setBettingVolume(withdrawStandard.getLastBettingVolume())
                    .build();

            final ServersMrg serversMrg = HallServer.getInstance().getHallTcpClient2Billing().getServersMrg();
            final Channel billingSession = serversMrg.getSessionByAccountId(pid);
            MsgUtil.sendInnerMsg(billingSession, withDrawOrder, pid, udpSessionId);
        } catch (Exception e) {
            LOGGER.error("ReqCreateCurrencyTraderWithdrawOrderHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

}
