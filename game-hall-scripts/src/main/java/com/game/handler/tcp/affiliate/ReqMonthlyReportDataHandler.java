package com.game.handler.tcp.affiliate;

import com.game.dao.game.GameNoteDao;
import com.game.dao.player.PlayerPromoteDao;
import com.game.dao.promote.CommissionRewardsNoteDao;
import com.game.dao.promote.TeamRewardsNoteDao;
import com.game.dao.promote.ThreeLevelRewardsNoteDao;
import com.game.engine.io.handler.TcpHandler;
import com.game.engine.math.BigDecimalUtils;
import com.game.engine.script.IHandlerEntity;
import com.game.engine.util.misc.Tuple2;
import com.game.engine.utils.MsgUtil;
import com.game.entity.MonthlyReport;
import com.game.entity.game.GameWagerStat;
import com.game.entity.player.Player;
import com.game.entity.player.promote.*;
import com.game.enums.ErrorCode;
import com.game.hall.mrg.player.PlayerMrg;
import com.game.manager.EntityDaoMrg;
import com.proto.AffiliateMessage;
import com.proto.MIDMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@IHandlerEntity(mid = MIDMessage.MID.ReqMonthlyReportData_VALUE, msg = AffiliateMessage.ReqMonthlyReportDataMessage.class)
public class ReqMonthlyReportDataHandler extends TcpHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(ReqMonthlyReportDataHandler.class);

    @Override
    public void run() {
        final AffiliateMessage.ResMonthlyReportDataMessage.Builder res = AffiliateMessage.ResMonthlyReportDataMessage.newBuilder();
        res.setMsgID(MIDMessage.MID.ResMonthlyReportData_VALUE);
        try {
            final Player player = PlayerMrg.getInstance().getOnlinePlayer(pid, this.getClass().getSimpleName());
            if (player == null) {
                res.setError(ErrorCode.Player_Offline.getCode());
                MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
                return;
            }
            final AffiliateMessage.ReqMonthlyReportDataMessage req = (AffiliateMessage.ReqMonthlyReportDataMessage) getMessage();
            final int currencyId = req.getCurrencyId();
            final int year = req.getYear();

            final List<MonthlyReport> monthlyReports = getMonthlyReport(player, currencyId, year);
            for (final MonthlyReport monthlyReport : monthlyReports) {
                res.addMonthlyReport(buildMonthlyReport(monthlyReport));
            }

            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        } catch (Exception e) {
            LOGGER.error("ReqMonthlyReportDataHandler", e);
            res.setError(ErrorCode.Internal_Server_Error.getCode());
            MsgUtil.responseHttp(res.build(), res.getMsgID(), session);
        }
    }

    private AffiliateMessage.MonthlyReport buildMonthlyReport(MonthlyReport monthlyReport) {
        return AffiliateMessage.MonthlyReport.newBuilder()
                .setMonth(monthlyReport.getMonth())
                .setActivePlayer(monthlyReport.getActivePlayer())
                .setWagered(monthlyReport.getWagered())
                .setRebate(monthlyReport.getRebate())
                .build();
    }

    public List<MonthlyReport> getMonthlyReport(Player player, int currencyId, int year) {
        final PlayerPromote playerPromote = player.getPlayerPromote();
        final Map<String, ReferralCode> referralCodeMap = playerPromote.getReferralCodeMap();

        final List<MonthlyReport> monthlyReports = new ArrayList<>();

        final ZoneId zoneId = ZoneId.of(player.getTimeZone());
        final int currentYear = LocalDate.now().getYear();
        final int currentMonth = LocalDate.now().getMonthValue();

        // 如果是今年，只查到当前月；否则查到12月
        final int maxMonth = (year == currentYear) ? currentMonth : 12;

        for (int month = 1; month <= maxMonth; month++) {
            final LocalDate startDate = LocalDate.of(year, month, 1);
            final LocalDate endDate = startDate.plusMonths(1);

            final long startTime = startDate.atStartOfDay(zoneId).toInstant().toEpochMilli();
            final long endTime = endDate.atStartOfDay(zoneId).toInstant().toEpochMilli();

            final Tuple2<Integer, List<Long>> oneLevelsTuple = EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class)
                    .findByCodeOneLevels(new ArrayList<>(referralCodeMap.keySet()), startTime, endTime);
            final Tuple2<Integer, List<Long>> twoLevelsTuple = EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class)
                    .findByCodeTwoLevels(new ArrayList<>(referralCodeMap.keySet()), startTime, endTime);
            final Tuple2<Integer, List<Long>> threeLevelsTuple = EntityDaoMrg.getInstance().getDao(PlayerPromoteDao.class)
                    .findByCodeThreeLevels(new ArrayList<>(referralCodeMap.keySet()), startTime, endTime);

            final List<Long> friends = new ArrayList<>();
            friends.addAll(oneLevelsTuple.getSecond());
            friends.addAll(twoLevelsTuple.getSecond());
            friends.addAll(threeLevelsTuple.getSecond());
            final GameWagerStat gameWagerStat = EntityDaoMrg.getInstance().getDao(GameNoteDao.class)
                    .aggregateActivePlayerAndWagered(friends, currencyId, startTime, endTime);

            final double totalCommissionRewards = EntityDaoMrg.getInstance().getDao(CommissionRewardsNoteDao.class)
                    .aggregateRewards(player.getPlayerId(), currencyId, CommissionRewardsNoteFields.amount, startTime, endTime);
            final double totalTeamRewards = EntityDaoMrg.getInstance().getDao(TeamRewardsNoteDao.class)
                    .aggregateRewards(player.getPlayerId(), currencyId, TeamRewardsNoteFields.amount, startTime, endTime);
            final double totalThreeLevelRewards = EntityDaoMrg.getInstance().getDao(ThreeLevelRewardsNoteDao.class)
                    .aggregateRewards(player.getPlayerId(), currencyId, ThreeLevelRewardsNoteFields.amount, startTime, endTime);
            final double totalRebate = BigDecimalUtils.add(totalCommissionRewards + totalTeamRewards, totalThreeLevelRewards, 9);

            final MonthlyReport monthlyReport = new MonthlyReport();
            monthlyReport.setMonth(month);
            monthlyReport.setActivePlayer(gameWagerStat.getTotalActivePlayerCount());
            monthlyReport.setWagered(gameWagerStat.getTotalWager());
            monthlyReport.setRebate(totalRebate);

            monthlyReports.add(monthlyReport);
        }
        return monthlyReports;
    }
}
