package com.game.loginsr.mrg;

import com.game.c_entity.merchant.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

public class MerchantData {
    private static final Logger LOGGER = LoggerFactory.getLogger(MerchantData.class);

    private String business_no;

    private final Map<String, String> c_globalMap = new ConcurrentHashMap<>(8);
    private final Map<String, C_WebSite> c_webSiteMap = new ConcurrentHashMap<>(8);
    private final Map<String, C_PhoneMsg> c_phoneMsgMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_MailSmtp> c_mailSmtpMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_MaintainNotice> c_maintainNoticeMap = new ConcurrentHashMap<>(8);

    private final Map<String, C_Advertise> c_advertiseMap = new ConcurrentHashMap<>(8);
    private final Map<Integer, C_Agent> c_proxyMap = new ConcurrentHashMap<>(8);

    private Query queryBusiness_no(String business_no) {
        final Criteria criteria = new Criteria();
        criteria.and("business_no").is(business_no);
        return new Query(criteria);
    }

    void loadData(String business_no) throws Exception {
        //后台
        this.business_no = business_no;

        loadGlobal(business_no);
        loadWebSite(business_no);
        loadPhoneMsg(business_no);
        loadMailSmtp(business_no);
        loadMaintainNotice(business_no);

        loadAdvertise(business_no);
        loadAgent(business_no);
    }

    public void loadGlobal(String business_no) throws Exception {
        //全局
        c_globalMap.clear();
        List<C_Global> c_globals = DataLoginMrg.mongoTemplate.find(queryBusiness_no(business_no), C_Global.class);
        for (C_Global c_global : c_globals) {
            c_global.check();
            c_globalMap.put(c_global.getName(), c_global.getValue());
        }
    }

    public void loadWebSite(String business_no) throws Exception {
        //站点信息
        c_webSiteMap.clear();
        final Query query = queryBusiness_no(business_no);
        query.addCriteria(Criteria.where("open").is(true));

        List<C_WebSite> c_webSites = DataLoginMrg.mongoTemplate.find(query, C_WebSite.class);
        for (C_WebSite c_webSite : c_webSites) {
            c_webSite.check();
            for (String domainName : c_webSite.getDomainName()) {
                c_webSiteMap.put(domainName, c_webSite);
            }
        }
    }

    public void loadPhoneMsg(String business_no) throws Exception {
        //短信
        c_phoneMsgMap.clear();
        final Query query = queryBusiness_no(business_no);
        query.addCriteria(Criteria.where("open").is(true)
                .and("baseOpen").is(true));

        final List<C_PhoneMsg> c_phoneMsgList = DataLoginMrg.mongoTemplate.find(query, C_PhoneMsg.class);
        for (final C_PhoneMsg c_phoneMsg : c_phoneMsgList) {
            c_phoneMsg.check();
            c_phoneMsgMap.put(c_phoneMsg.getThreePartyName(), c_phoneMsg);
        }
    }

    public void loadMailSmtp(String business_no) throws Exception {
        //邮件
        c_mailSmtpMap.clear();
        final Query query = queryBusiness_no(business_no);
        query.addCriteria(Criteria.where("open").is(true)
                .and("baseOpen").is(true));

        final List<C_MailSmtp> c_mailSmtps = DataLoginMrg.mongoTemplate.find(query, C_MailSmtp.class);
        for (final C_MailSmtp c_mailSmtp : c_mailSmtps) {
            c_mailSmtp.check();
            c_mailSmtpMap.put(c_mailSmtp.getC_Id(), c_mailSmtp);
        }
    }

    public void loadMaintainNotice(String business_no) throws Exception {
        //维护公告
        c_maintainNoticeMap.clear();
        final Query query = queryBusiness_no(business_no);
        query.addCriteria(Criteria.where("status").is(true));

        final List<C_MaintainNotice> c_maintainNoticeList = DataLoginMrg.mongoTemplate.find(query, C_MaintainNotice.class);
        for (C_MaintainNotice c_maintainNotice : c_maintainNoticeList) {
            c_maintainNotice.check();
            c_maintainNoticeMap.put(c_maintainNotice.getC_id(), c_maintainNotice);
        }
    }

    public void loadAdvertise(String business_no) throws Exception {
        //广告
        c_advertiseMap.clear();
        final Query query = queryBusiness_no(business_no);

        final List<C_Advertise> c_advertises = DataLoginMrg.mongoTemplate.find(query, C_Advertise.class);
        for (C_Advertise c_advertise : c_advertises) {
            c_advertise.check();
            c_advertiseMap.put(c_advertise.getAdvertiseId(), c_advertise);
        }
    }

    public void loadAgent(String business_no) throws Exception {
        //代理
        c_proxyMap.clear();
        final Query query = queryBusiness_no(business_no);

        final List<C_Agent> c_proxies = DataLoginMrg.mongoTemplate.find(query, C_Agent.class);
        for (C_Agent c_proxy : c_proxies) {
            c_proxy.check();
            c_proxyMap.put(c_proxy.getC_id(), c_proxy);
        }
    }

    public String findC_GlobalValue(String name) {
        final String value = c_globalMap.get(name);
        if (value == null) {
            LOGGER.warn("config，C_Global，name：{}，not exits", name);
            return "";
        }
        return value;
    }

    public C_WebSite findC_WebSiteModel(String host) {
        final C_WebSite c_webSite = c_webSiteMap.get(host);
        if (c_webSite == null) {
            LOGGER.warn("c_webSite，host：{}，not exist", host);
            return null;
        }
        return c_webSite;
    }

    public C_Advertise findC_Advertise(String advertiseId) {
        final C_Advertise c_advertise = c_advertiseMap.get(advertiseId);
        if (c_advertise == null) {
            LOGGER.warn("C_Advertise，advertiseId：{}，not exist", advertiseId);
            return null;
        }
        return c_advertise;
    }

    public C_Agent findC_Proxy(int c_id) {
        final C_Agent c_proxy = c_proxyMap.get(c_id);
        if (c_proxy == null) {
            LOGGER.warn("C_Proxy，c_id：{}，not exist", c_id);
            return null;
        }
        return c_proxy;
    }

    public String getBusiness_no() {
        return business_no;
    }

    public C_MailSmtp findC_MailSmtp() {
        final Optional<C_MailSmtp> optional = c_mailSmtpMap.values().stream().findFirst();
        return optional.orElse(null);
    }

    public C_MaintainNotice findC_MaintainNotice() {
        final Optional<C_MaintainNotice> optional = c_maintainNoticeMap.values().stream().findFirst();
        return optional.orElse(null);
    }

    public Map<String, C_PhoneMsg> getC_phoneMsgMap() {
        return c_phoneMsgMap;
    }

    public Map<Integer, C_Agent> getC_proxyMap() {
        return c_proxyMap;
    }
}
