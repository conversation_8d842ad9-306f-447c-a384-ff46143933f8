package com.game.loginsr.script;

import com.game.c_entity.middleplatform.C_BaseMerchant;
import com.game.engine.script.IBaseScript;
import com.proto.LoginMessage;
import io.netty.channel.Channel;

public interface IRegisterScript extends IBaseScript {

    default void mailRegister(LoginMessage.ReqRegisterMessage req, C_BaseMerchant c_baseMerchant, String ip,
                              String country, Channel session) {
    }

    default void phoneRegister(LoginMessage.ReqRegisterMessage req, C_BaseMerchant c_baseMerchant, String ip,
                               String country, Channel session) {
    }

    default void accountRegister(LoginMessage.ReqRegisterMessage req, C_BaseMerchant c_baseMerchant, String ip,
                                 String country, Channel session) {
    }

    default void walletRegister(LoginMessage.ReqRegisterMessage req, C_BaseMerchant c_baseMerchant, String ip,
                                 String country, Channel session) {
    }
}
